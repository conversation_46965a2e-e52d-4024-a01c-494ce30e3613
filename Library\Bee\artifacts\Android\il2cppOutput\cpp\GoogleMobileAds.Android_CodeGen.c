﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Android.AdErrorClient::.ctor(UnityEngine.AndroidJavaObject)
extern void AdErrorClient__ctor_m88DBD2BAF730F42FE760FD01615B69CD260FC334 (void);
// 0x00000002 System.Int32 GoogleMobileAds.Android.AdErrorClient::GetCode()
extern void AdErrorClient_GetCode_m76DE51A678EBE5ED59E61C0E38DF817A7A1FBCFA (void);
// 0x00000003 System.String GoogleMobileAds.Android.AdErrorClient::GetDomain()
extern void AdErrorClient_GetDomain_m3CD89CD160405DEA1DCB44C5977E626629EB38E6 (void);
// 0x00000004 System.String GoogleMobileAds.Android.AdErrorClient::GetMessage()
extern void AdErrorClient_GetMessage_m876728CC16F4DBCF29AEA4D0FE301EFC95A35595 (void);
// 0x00000005 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Android.AdErrorClient::GetCause()
extern void AdErrorClient_GetCause_m5852A32BB1A66A4BBD7C46D65E332F393C3F3D34 (void);
// 0x00000006 System.String GoogleMobileAds.Android.AdErrorClient::ToString()
extern void AdErrorClient_ToString_mC10A5B4E0830154DD766DFE4A78D587A4FF1881A (void);
// 0x00000007 System.Void GoogleMobileAds.Android.AdInspectorErrorClient::.ctor(UnityEngine.AndroidJavaObject)
extern void AdInspectorErrorClient__ctor_m690F6D0D0CA5AAA979E8A8BAD1F5F97E5E769B40 (void);
// 0x00000008 System.Void GoogleMobileAds.Android.AdInspectorListener::.ctor(System.Action`1<GoogleMobileAds.Common.AdInspectorErrorClientEventArgs>)
extern void AdInspectorListener__ctor_m0B2D2DCE53CD0692CE4D07EA36FDF891CBE71DE2 (void);
// 0x00000009 System.Void GoogleMobileAds.Android.AdInspectorListener::onAdInspectorClosed(UnityEngine.AndroidJavaObject)
extern void AdInspectorListener_onAdInspectorClosed_mDA7B29960EF40C32F591934D6A5F36697B0CA564 (void);
// 0x0000000A System.Void GoogleMobileAds.Android.AdManagerBannerClient::.ctor()
extern void AdManagerBannerClient__ctor_mFC060A869E58CA1AA869420809741EA9CCBFC2D6 (void);
// 0x0000000B System.Void GoogleMobileAds.Android.AdManagerBannerClient::add_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerBannerClient_add_OnAppEvent_m9BC36DAB94A5F0936DA4B11A0BC6FC07197A8412 (void);
// 0x0000000C System.Void GoogleMobileAds.Android.AdManagerBannerClient::remove_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerBannerClient_remove_OnAppEvent_m3D886B78264EBE03EE545AE03E3E0A56D1201469 (void);
// 0x0000000D System.Collections.Generic.List`1<GoogleMobileAds.Api.AdSize> GoogleMobileAds.Android.AdManagerBannerClient::get_ValidAdSizes()
extern void AdManagerBannerClient_get_ValidAdSizes_m1A2462C94FB067C2420D25DA4766AE1A02E9F028 (void);
// 0x0000000E System.Void GoogleMobileAds.Android.AdManagerBannerClient::set_ValidAdSizes(System.Collections.Generic.List`1<GoogleMobileAds.Api.AdSize>)
extern void AdManagerBannerClient_set_ValidAdSizes_mF2CB220CCDC3C0DECB6F121693321622F4F37374 (void);
// 0x0000000F System.Void GoogleMobileAds.Android.AdManagerBannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
extern void AdManagerBannerClient_LoadAd_mA8922886629F2D634D50E9E659BEBDF7FCCBB316 (void);
// 0x00000010 System.Void GoogleMobileAds.Android.AdManagerBannerClient::onAppEvent(System.String,System.String)
extern void AdManagerBannerClient_onAppEvent_m7D7736CEFDF2A91526462DA9094C80718F1E0E26 (void);
// 0x00000011 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::.ctor()
extern void AdManagerInterstitialClient__ctor_mC2BA8FC797B0B094E8F097AF41E87C30618B4DF1 (void);
// 0x00000012 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdLoaded_m2E1436BF40EA10F3A4F01D4AAE3C11F8AFFA8E31 (void);
// 0x00000013 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdLoaded_m1CF1F59EB3117AB278BB56520D15FB2B431B5663 (void);
// 0x00000014 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_add_OnAdFailedToLoad_mC08209147BCCBAB6DF63A3A820C5358806396E23 (void);
// 0x00000015 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdFailedToLoad_m59F607A89A9B4A0BFDAAA1D29EAAC02C982A753F (void);
// 0x00000016 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_add_OnAdFailedToPresentFullScreenContent_m336DB4A57D1157CFD5FD2C2C1589354BE2D63047 (void);
// 0x00000017 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m75C475AE28279D367F7ADB9B54D42B1DC2CC8E90 (void);
// 0x00000018 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdDidPresentFullScreenContent_mC8F8ECC60E5137E0D0F0FED4CEF3F69F2BCACA9B (void);
// 0x00000019 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdDidPresentFullScreenContent_m3CD52CD0239906E2182E8BF15ECCD32AE480B466 (void);
// 0x0000001A System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdDidDismissFullScreenContent_mAA7F18058A3A3900364B7BFF910B49AAE708E746 (void);
// 0x0000001B System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdDidDismissFullScreenContent_mAE690C4DD49CC821046F67B90B2A923FFF5BFE0B (void);
// 0x0000001C System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_add_OnAdDidRecordImpression_mF1A907AA5E7FED7F98778A11A85876988CFFC2BE (void);
// 0x0000001D System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AdManagerInterstitialClient_remove_OnAdDidRecordImpression_m0582C502969653F216B5DD0634AC92E23AF1EB8A (void);
// 0x0000001E System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void AdManagerInterstitialClient_add_OnPaidEvent_m4485F524565F4BE34A0CC3D8B02C0E9AD51D65D5 (void);
// 0x0000001F System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void AdManagerInterstitialClient_remove_OnPaidEvent_m19EC18DCF300DA120F1243CB4D690574BEB66701 (void);
// 0x00000020 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerInterstitialClient_add_OnAppEvent_m80172796E371686146E464FF7F2B2C8F12D622A4 (void);
// 0x00000021 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
extern void AdManagerInterstitialClient_remove_OnAppEvent_mE0810A5AD2955BAF13FA86059A959EB9E9A6D9F9 (void);
// 0x00000022 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::add_OnAdClicked(System.Action)
extern void AdManagerInterstitialClient_add_OnAdClicked_m79C3631452D6793140EF26D1D7E3F0CBD563D241 (void);
// 0x00000023 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::remove_OnAdClicked(System.Action)
extern void AdManagerInterstitialClient_remove_OnAdClicked_mAEC6F34D263E8BD5A58DBC7C62267134AE090CDC (void);
// 0x00000024 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::CreateInterstitialAd()
extern void AdManagerInterstitialClient_CreateInterstitialAd_m58D8941C1EE741EE23151D7F9C0722653C4A52E3 (void);
// 0x00000025 System.Boolean GoogleMobileAds.Android.AdManagerInterstitialClient::IsAdAvailable(System.String)
extern void AdManagerInterstitialClient_IsAdAvailable_m7E4AE23105D0AF37BB2D42B600C2F3D6C1BA527E (void);
// 0x00000026 GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.Android.AdManagerInterstitialClient::PollAd(System.String)
extern void AdManagerInterstitialClient_PollAd_mD15A94062E8E45A12B57124230EF55E3A120C8E9 (void);
// 0x00000027 GoogleMobileAds.Common.IAdManagerInterstitialClient GoogleMobileAds.Android.AdManagerInterstitialClient::PollAdManagerAd(System.String)
extern void AdManagerInterstitialClient_PollAdManagerAd_mD98CB0768D5EBD87CFAAABE6EE3F2D1A299C0805 (void);
// 0x00000028 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void AdManagerInterstitialClient_LoadAd_mE2E4C20E9E87BA952B49B993306ABB78F136D60E (void);
// 0x00000029 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::Show()
extern void AdManagerInterstitialClient_Show_m83AA16389CA1AF20A194032BAA61414EA07805B7 (void);
// 0x0000002A System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::DestroyInterstitial()
extern void AdManagerInterstitialClient_DestroyInterstitial_m665607C6220C4DB4279C58A4B5CB186FA0D8F9B5 (void);
// 0x0000002B System.String GoogleMobileAds.Android.AdManagerInterstitialClient::GetAdUnitID()
extern void AdManagerInterstitialClient_GetAdUnitID_m28839B725945F3497532BD86CA13CCD53D6F02EE (void);
// 0x0000002C GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.AdManagerInterstitialClient::GetResponseInfoClient()
extern void AdManagerInterstitialClient_GetResponseInfoClient_m40150B7F187AD0AA734512D3F825DB82DCCF380C (void);
// 0x0000002D System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onInterstitialAdLoaded()
extern void AdManagerInterstitialClient_onInterstitialAdLoaded_mBEF6F6A7C3E6470CD5DD5376739E3D3A75A065C5 (void);
// 0x0000002E System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onInterstitialAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void AdManagerInterstitialClient_onInterstitialAdFailedToLoad_m10B6459575B8AAEEB4B44967BC3B36945BC8A221 (void);
// 0x0000002F System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void AdManagerInterstitialClient_onAdFailedToShowFullScreenContent_mC4DC7A2E994227B992180C20271324DA83BE3AB1 (void);
// 0x00000030 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdShowedFullScreenContent()
extern void AdManagerInterstitialClient_onAdShowedFullScreenContent_m693AD080EAD48A3C92300779BF1ED713ACA6E8CF (void);
// 0x00000031 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdDismissedFullScreenContent()
extern void AdManagerInterstitialClient_onAdDismissedFullScreenContent_m9D19B85AA88CDEC032E4D4A222E62FBC9A989DB9 (void);
// 0x00000032 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdImpression()
extern void AdManagerInterstitialClient_onAdImpression_m7E00A10747C28F9BA83B39BFBA58DC17FB03ED3A (void);
// 0x00000033 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAdClicked()
extern void AdManagerInterstitialClient_onAdClicked_m9B71EA9EA80505A5493C7BA33E5613BA5A2C2684 (void);
// 0x00000034 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void AdManagerInterstitialClient_onPaidEvent_m4E10C84D5D377B6733FDE9B85B5A78579E8DB8FE (void);
// 0x00000035 System.Void GoogleMobileAds.Android.AdManagerInterstitialClient::onAppEvent(System.String,System.String)
extern void AdManagerInterstitialClient_onAppEvent_m6FF69EF9D28D2D4E0CB3933C4DAE6E9212FD2546 (void);
// 0x00000036 System.Void GoogleMobileAds.Android.AdapterResponseInfoClient::.ctor(UnityEngine.AndroidJavaObject)
extern void AdapterResponseInfoClient__ctor_m3AD39ACE8F60553902B78BA44EFE94563464DC85 (void);
// 0x00000037 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdapterClassName()
extern void AdapterResponseInfoClient_get_AdapterClassName_mD4F62ECEB70D18C43926CBE2142D0F454EE1FCF2 (void);
// 0x00000038 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceId()
extern void AdapterResponseInfoClient_get_AdSourceId_m38E79DB97CAB5F2F70AED5BFF120B94A55780246 (void);
// 0x00000039 System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceName()
extern void AdapterResponseInfoClient_get_AdSourceName_m248689B367E7E1D2DA8741551AC5CD977A6E4E9A (void);
// 0x0000003A System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceInstanceId()
extern void AdapterResponseInfoClient_get_AdSourceInstanceId_m689AAC03D41C6C511EE48FA42EE4568350CA79B1 (void);
// 0x0000003B System.String GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdSourceInstanceName()
extern void AdapterResponseInfoClient_get_AdSourceInstanceName_m78BC2A6F3545578088BA0B45397CEF5579D31D98 (void);
// 0x0000003C System.Int64 GoogleMobileAds.Android.AdapterResponseInfoClient::get_LatencyMillis()
extern void AdapterResponseInfoClient_get_LatencyMillis_mD11CDCBB8C964E90278A0C2A76BC93277833329F (void);
// 0x0000003D System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdUnitMapping()
extern void AdapterResponseInfoClient_get_AdUnitMapping_mF0D6B6C34D4DA3E5443C678F85890FAC23F9AEE9 (void);
// 0x0000003E GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Android.AdapterResponseInfoClient::get_AdError()
extern void AdapterResponseInfoClient_get_AdError_mC03B26064B4AF8DABE01009D492661048D7BA0BE (void);
// 0x0000003F System.String GoogleMobileAds.Android.AdapterResponseInfoClient::ToString()
extern void AdapterResponseInfoClient_ToString_mE337C161E5DD3EA2C23068B19F8AB78EC44CB6EF (void);
// 0x00000040 System.Void GoogleMobileAds.Android.AppOpenAdClient::.ctor()
extern void AppOpenAdClient__ctor_mEB376D4F185D1E9946EBE78F01389BC4569DFF65 (void);
// 0x00000041 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdLoaded_m0284EE5461CCBF9FBEFC75142CF46213C20F3B16 (void);
// 0x00000042 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdLoaded_m6F483B82BA3FB708B04ECB380F367317368E1525 (void);
// 0x00000043 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AppOpenAdClient_add_OnAdFailedToLoad_m37DEAA83ACA6B33D948B88EF072B4AD7CD814818 (void);
// 0x00000044 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void AppOpenAdClient_remove_OnAdFailedToLoad_m3234FC6979CCDB95A39AD7BE4F87099CA3F44A82 (void);
// 0x00000045 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void AppOpenAdClient_add_OnPaidEvent_mBE4A5B7FEF838B1FA2342331DFB0468C313D3EB2 (void);
// 0x00000046 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void AppOpenAdClient_remove_OnPaidEvent_m6957B1E86C7D9573C43D74BF3F34C6DDE746BB42 (void);
// 0x00000047 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AppOpenAdClient_add_OnAdFailedToPresentFullScreenContent_mAD1D8D9FD175D06B396B0B7DF38386BD540FE18B (void);
// 0x00000048 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void AppOpenAdClient_remove_OnAdFailedToPresentFullScreenContent_mF30C2A1A102AB220FCF4D3B08AFB13B57AA37C0D (void);
// 0x00000049 System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdDidPresentFullScreenContent_m7AD5F1DB70569DA07BF81892B1F68F6B6D363405 (void);
// 0x0000004A System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdDidPresentFullScreenContent_m1579719351CD4BADC8EFD8377CD74ECF8E2472CE (void);
// 0x0000004B System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdDidDismissFullScreenContent_m60D5BC495067B1B57379DE5B6E6E4F4BF119AFEA (void);
// 0x0000004C System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdDidDismissFullScreenContent_mCE166B329D7E18C9247F96F09E4FD8553FB1DDDC (void);
// 0x0000004D System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_add_OnAdDidRecordImpression_mFBF4070CFC1C9BB028437FD118082BD8572A4A03 (void);
// 0x0000004E System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void AppOpenAdClient_remove_OnAdDidRecordImpression_m738814410D3671514A09FD0A11C4D4ED5394B9E1 (void);
// 0x0000004F System.Void GoogleMobileAds.Android.AppOpenAdClient::add_OnAdClicked(System.Action)
extern void AppOpenAdClient_add_OnAdClicked_m286233DF6F67B44E6CA01C157036A26054042A2C (void);
// 0x00000050 System.Void GoogleMobileAds.Android.AppOpenAdClient::remove_OnAdClicked(System.Action)
extern void AppOpenAdClient_remove_OnAdClicked_mF345B1E2A8409022E61AF1AA3EC17094CB88E9E9 (void);
// 0x00000051 System.Void GoogleMobileAds.Android.AppOpenAdClient::CreateAppOpenAd()
extern void AppOpenAdClient_CreateAppOpenAd_m1261CA2D18446A2DFA216165DB4982D9B48C1450 (void);
// 0x00000052 System.Void GoogleMobileAds.Android.AppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void AppOpenAdClient_LoadAd_m8A520FAEBB175D7F257AE7BDC6C684E3852C0074 (void);
// 0x00000053 System.Void GoogleMobileAds.Android.AppOpenAdClient::Show()
extern void AppOpenAdClient_Show_m1E86C944E26AD298277031AAF2FFAEA15D0F0B22 (void);
// 0x00000054 System.String GoogleMobileAds.Android.AppOpenAdClient::GetAdUnitID()
extern void AppOpenAdClient_GetAdUnitID_mDD84F1327C085B17FA0243A3A43E00D46D25DB32 (void);
// 0x00000055 System.Boolean GoogleMobileAds.Android.AppOpenAdClient::IsAdAvailable(System.String)
extern void AppOpenAdClient_IsAdAvailable_mB74100C82C6972F64D254C5507307D4CE30A2C0D (void);
// 0x00000056 GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.Android.AppOpenAdClient::PollAd(System.String)
extern void AppOpenAdClient_PollAd_m2777EF8D3FC861691ED2209A3606BFF5B2D42DDA (void);
// 0x00000057 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.AppOpenAdClient::GetResponseInfoClient()
extern void AppOpenAdClient_GetResponseInfoClient_m714F64BA2CAFEBA769DB8249D12999A60DCF3704 (void);
// 0x00000058 System.Void GoogleMobileAds.Android.AppOpenAdClient::DestroyAppOpenAd()
extern void AppOpenAdClient_DestroyAppOpenAd_m6F81444D6CC9285C90C18CF45305C106E831F2A7 (void);
// 0x00000059 System.Void GoogleMobileAds.Android.AppOpenAdClient::onAppOpenAdLoaded()
extern void AppOpenAdClient_onAppOpenAdLoaded_m71E71B17DE1A92B61B7FB4399CC39A9D279C60E9 (void);
// 0x0000005A System.Void GoogleMobileAds.Android.AppOpenAdClient::onAppOpenAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void AppOpenAdClient_onAppOpenAdFailedToLoad_m8FF125176A9D6AB24CCB9C1C0614E85DEBFA600D (void);
// 0x0000005B System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void AppOpenAdClient_onAdFailedToShowFullScreenContent_m2E8311D06935F505855850F51E94EEF4973796A6 (void);
// 0x0000005C System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdShowedFullScreenContent()
extern void AppOpenAdClient_onAdShowedFullScreenContent_m824EB057901C14E6C30C9BC6847C9028C3E58497 (void);
// 0x0000005D System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdDismissedFullScreenContent()
extern void AppOpenAdClient_onAdDismissedFullScreenContent_m39D6400BFB219F9DE439CEDD1DB70A1D5DDFA3CC (void);
// 0x0000005E System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdImpression()
extern void AppOpenAdClient_onAdImpression_m8761B82F00493DBD3821096969516E4D70CED677 (void);
// 0x0000005F System.Void GoogleMobileAds.Android.AppOpenAdClient::onAdClicked()
extern void AppOpenAdClient_onAdClicked_m4CE850BB55D897B734D899401ABB4420DBE0830F (void);
// 0x00000060 System.Void GoogleMobileAds.Android.AppOpenAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void AppOpenAdClient_onPaidEvent_m82844CEF3194744E3CC7003465B4D04A63BB9218 (void);
// 0x00000061 System.Void GoogleMobileAds.Android.AppStateEventClient::.ctor()
extern void AppStateEventClient__ctor_m4C240F785BCC2D2C26649A4F76B7D5D9132BAF30 (void);
// 0x00000062 System.Void GoogleMobileAds.Android.AppStateEventClient::add_appStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_appStateChanged_m2820A831E5CEC722234335227B18692391C3A4D6 (void);
// 0x00000063 System.Void GoogleMobileAds.Android.AppStateEventClient::remove_appStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_appStateChanged_m5BF3BD017CA00CD8DED3FE3ABC585943472EBA0B (void);
// 0x00000064 System.Void GoogleMobileAds.Android.AppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_AppStateChanged_m1BA95FE1F1EA1779F9ADFE666A9AC4C7CC4F8E85 (void);
// 0x00000065 System.Void GoogleMobileAds.Android.AppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_AppStateChanged_m049B62BC034B17D6657A6098559F2F9A58C941B5 (void);
// 0x00000066 System.Void GoogleMobileAds.Android.AppStateEventClient::onAppStateChanged(System.Boolean)
extern void AppStateEventClient_onAppStateChanged_mBB471FD53C4A64AAFBDAD0E4F5FB609A6FB1DDBD (void);
// 0x00000067 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::.ctor()
extern void ApplicationPreferencesClient__ctor_mCA619AB5DEF6C891E7B2333F292C8B3D95BAC210 (void);
// 0x00000068 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::SetInt(System.String,System.Int32)
extern void ApplicationPreferencesClient_SetInt_m5253C3C818602B0AB0F73A6EC65C0AE87E49A379 (void);
// 0x00000069 System.Void GoogleMobileAds.Android.ApplicationPreferencesClient::SetString(System.String,System.String)
extern void ApplicationPreferencesClient_SetString_m541ABCE830B9F82BDA17A12906822BD3409F233E (void);
// 0x0000006A System.Int32 GoogleMobileAds.Android.ApplicationPreferencesClient::GetInt(System.String)
extern void ApplicationPreferencesClient_GetInt_m2E31450FB7A0C02D1DEA787F4FA559D13884E4A4 (void);
// 0x0000006B System.String GoogleMobileAds.Android.ApplicationPreferencesClient::GetString(System.String)
extern void ApplicationPreferencesClient_GetString_mFF12EE10D66AD620FA6B206ADBC576F6EF1F76A6 (void);
// 0x0000006C System.Void GoogleMobileAds.Android.BannerClient::.ctor(System.String)
extern void BannerClient__ctor_mF8BE5F4C0BC785560D8F935A30F83076805F0B37 (void);
// 0x0000006D System.Void GoogleMobileAds.Android.BannerClient::.ctor()
extern void BannerClient__ctor_mF5F2EE9004D78F2DF591248B3FA97630BF55A917 (void);
// 0x0000006E System.Void GoogleMobileAds.Android.BannerClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_add_OnAdLoaded_m6FE124A5DC5C30E7EF545B18DAEA53FEA06F1A13 (void);
// 0x0000006F System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_remove_OnAdLoaded_m7D4BBF12EA8824C681CFB6CAAADC0FD75381816C (void);
// 0x00000070 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void BannerClient_add_OnAdFailedToLoad_m4144FDD58A2D680EB4B5F76F6D622230E00C8F48 (void);
// 0x00000071 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void BannerClient_remove_OnAdFailedToLoad_mD883FF94D8FC8E946D7D6FDE16DAE0FAB3D20C80 (void);
// 0x00000072 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdOpening(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_add_OnAdOpening_mAA500C8B9B9627F8647ACDBBA57D306E3BDD99E0 (void);
// 0x00000073 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdOpening(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_remove_OnAdOpening_m10C5A2C9DA9B55D5F22C92B470D7C9966F571925 (void);
// 0x00000074 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdClosed(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_add_OnAdClosed_m7D4598C4D8F07EB44BFBCD2B46317730C1D2C762 (void);
// 0x00000075 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdClosed(System.EventHandler`1<System.EventArgs>)
extern void BannerClient_remove_OnAdClosed_m6C3BE9E5A82B2EBC1A785AF9ED632F22F5EEDAD3 (void);
// 0x00000076 System.Void GoogleMobileAds.Android.BannerClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void BannerClient_add_OnPaidEvent_m677DCC8319399F9FEEC57CEC69A90A85E40B36CC (void);
// 0x00000077 System.Void GoogleMobileAds.Android.BannerClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void BannerClient_remove_OnPaidEvent_mD3CD1D4E2B41DFAD942075EC3A02D0A236B996B7 (void);
// 0x00000078 System.Void GoogleMobileAds.Android.BannerClient::add_OnAdClicked(System.Action)
extern void BannerClient_add_OnAdClicked_m8A2BFFF9C066D6281E71D3DA1F6A355272CF238C (void);
// 0x00000079 System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdClicked(System.Action)
extern void BannerClient_remove_OnAdClicked_m3307B46C723DAFD9B2005CCA8F2552BFCA5E2CF0 (void);
// 0x0000007A System.Void GoogleMobileAds.Android.BannerClient::add_OnAdImpressionRecorded(System.Action)
extern void BannerClient_add_OnAdImpressionRecorded_m2E41A8A18EC99BACEACA1A7E20273AB29B1FD525 (void);
// 0x0000007B System.Void GoogleMobileAds.Android.BannerClient::remove_OnAdImpressionRecorded(System.Action)
extern void BannerClient_remove_OnAdImpressionRecorded_mE489F864528F273F12AE731D3C277646913FD24A (void);
// 0x0000007C System.Void GoogleMobileAds.Android.BannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void BannerClient_CreateBannerView_m6C38246E4FDDE07A257BEC5520636D0A11202B9C (void);
// 0x0000007D System.Void GoogleMobileAds.Android.BannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,System.Int32,System.Int32)
extern void BannerClient_CreateBannerView_m545E5F3D9502F7FA9E3314DBFF11481369773517 (void);
// 0x0000007E System.Void GoogleMobileAds.Android.BannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
extern void BannerClient_LoadAd_mA0475B8FF6AD8A08E379E3182F0163B437456738 (void);
// 0x0000007F System.Void GoogleMobileAds.Android.BannerClient::ShowBannerView()
extern void BannerClient_ShowBannerView_m41FCFE959258D59BA51CC922D6D5F19236C7FB0F (void);
// 0x00000080 System.Void GoogleMobileAds.Android.BannerClient::HideBannerView()
extern void BannerClient_HideBannerView_m0F945DACAB243264235FA4FD4162365FCCF0A906 (void);
// 0x00000081 System.Void GoogleMobileAds.Android.BannerClient::DestroyBannerView()
extern void BannerClient_DestroyBannerView_m0B15993F75A7B442C0DF1B7528F7DF4687F019CA (void);
// 0x00000082 System.String GoogleMobileAds.Android.BannerClient::GetAdUnitID()
extern void BannerClient_GetAdUnitID_mE1652386E12A84BC6A2A86A83A9B21C56BF1EE6B (void);
// 0x00000083 System.Single GoogleMobileAds.Android.BannerClient::GetHeightInPixels()
extern void BannerClient_GetHeightInPixels_m2DC15ADEBE58FD53C08186F5F8F3B04545206F66 (void);
// 0x00000084 System.Single GoogleMobileAds.Android.BannerClient::GetWidthInPixels()
extern void BannerClient_GetWidthInPixels_m0FC495326EA8F38CC0D410F355F859D1220A9E8B (void);
// 0x00000085 System.Void GoogleMobileAds.Android.BannerClient::SetPosition(GoogleMobileAds.Api.AdPosition)
extern void BannerClient_SetPosition_m2301CA7EA87C4E1C10948EDB46E83F049A44F68F (void);
// 0x00000086 System.Void GoogleMobileAds.Android.BannerClient::SetPosition(System.Int32,System.Int32)
extern void BannerClient_SetPosition_m923265E13104BC122B7774BED86369DCA389EB2A (void);
// 0x00000087 System.Boolean GoogleMobileAds.Android.BannerClient::IsCollapsible()
extern void BannerClient_IsCollapsible_m6E9B2F8E218ACA27E7B4197DA4A8DA3C17178137 (void);
// 0x00000088 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.BannerClient::GetResponseInfoClient()
extern void BannerClient_GetResponseInfoClient_m6DFB0A855207F903D56C8A1BA430E759091E2A66 (void);
// 0x00000089 System.Void GoogleMobileAds.Android.BannerClient::onAdLoaded()
extern void BannerClient_onAdLoaded_m5FFB00BDBA55AD1A9C018200157B46B34DC34558 (void);
// 0x0000008A System.Void GoogleMobileAds.Android.BannerClient::onAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void BannerClient_onAdFailedToLoad_mF0B7DFE4403FF3405FAC63F8A59D7C43A405AA6F (void);
// 0x0000008B System.Void GoogleMobileAds.Android.BannerClient::onAdOpened()
extern void BannerClient_onAdOpened_mDC8C60A191781B683699C254120EE50A0048CB87 (void);
// 0x0000008C System.Void GoogleMobileAds.Android.BannerClient::onAdClosed()
extern void BannerClient_onAdClosed_m0D8AAA6C2F2EADB3B91AF9494D1893E6439BD7D2 (void);
// 0x0000008D System.Void GoogleMobileAds.Android.BannerClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void BannerClient_onPaidEvent_mAF7BE811BE76C62824D043DE640475A5A206728B (void);
// 0x0000008E System.Void GoogleMobileAds.Android.BannerClient::onAdClicked()
extern void BannerClient_onAdClicked_mF3A7326E8C2C2F2EE431729E57648A956AEAB6C7 (void);
// 0x0000008F System.Void GoogleMobileAds.Android.BannerClient::onAdImpression()
extern void BannerClient_onAdImpression_m4739D123B68191C2A4C193F1E7B807E5277683D3 (void);
// 0x00000090 System.Void GoogleMobileAds.Android.DisplayMetrics::.ctor()
extern void DisplayMetrics__ctor_m8F895C80EBCA4AED8758E3DEBD2CF65248BFC2B4 (void);
// 0x00000091 System.Single GoogleMobileAds.Android.DisplayMetrics::get_Density()
extern void DisplayMetrics_get_Density_mD688C6F9AAA4C676BFAA2B772C5E622A808236BD (void);
// 0x00000092 System.Void GoogleMobileAds.Android.DisplayMetrics::set_Density(System.Single)
extern void DisplayMetrics_set_Density_m3D0B71F3B81BB60A34574A5325F28A3274EC2B37 (void);
// 0x00000093 System.Int32 GoogleMobileAds.Android.DisplayMetrics::get_HeightPixels()
extern void DisplayMetrics_get_HeightPixels_m7F22FEA3AA8FB8D0FBD3262A350E2221315DA5A4 (void);
// 0x00000094 System.Void GoogleMobileAds.Android.DisplayMetrics::set_HeightPixels(System.Int32)
extern void DisplayMetrics_set_HeightPixels_mA2C374FB8C1D825A6275A0359357E27723F35819 (void);
// 0x00000095 System.Int32 GoogleMobileAds.Android.DisplayMetrics::get_WidthPixels()
extern void DisplayMetrics_get_WidthPixels_mEF6E3679EDF3407EC588DB679F81B12D3EE8C914 (void);
// 0x00000096 System.Void GoogleMobileAds.Android.DisplayMetrics::set_WidthPixels(System.Int32)
extern void DisplayMetrics_set_WidthPixels_mCE3C42D134493FD7CBAF587BC906CF6BB8AB5277 (void);
// 0x00000097 System.Void GoogleMobileAds.GoogleMobileAdsClientFactory::.ctor()
extern void GoogleMobileAdsClientFactory__ctor_mEB046D88F7DF05C39310C9ED148CB93684C0C146 (void);
// 0x00000098 GoogleMobileAds.Common.IAppStateEventClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAppStateEventClient()
extern void GoogleMobileAdsClientFactory_BuildAppStateEventClient_m745422CFE30BFE5D03212133E63261F75764BB47 (void);
// 0x00000099 GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAppOpenAdClient()
extern void GoogleMobileAdsClientFactory_BuildAppOpenAdClient_m04C7325C9CD581B21989D9A01208981D1F6EE3F3 (void);
// 0x0000009A GoogleMobileAds.Common.IBannerClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildBannerClient()
extern void GoogleMobileAdsClientFactory_BuildBannerClient_m1DA1036CC88B2C930AEF747959F8F4F4F42C5E4F (void);
// 0x0000009B GoogleMobileAds.Common.IAdManagerBannerClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAdManagerBannerClient()
extern void GoogleMobileAdsClientFactory_BuildAdManagerBannerClient_m483F43A3B5F0A4DFA6A06DFE964BE1A5299C6281 (void);
// 0x0000009C GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildInterstitialClient()
extern void GoogleMobileAdsClientFactory_BuildInterstitialClient_mBA16B22AAB2CA58F48437FAADA0DCEB4C1FAA1CE (void);
// 0x0000009D GoogleMobileAds.Common.IAdManagerInterstitialClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildAdManagerInterstitialClient()
extern void GoogleMobileAdsClientFactory_BuildAdManagerInterstitialClient_m8709773D0E17E49F8AAF23E8C599A383B84ACF35 (void);
// 0x0000009E GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildRewardedAdClient()
extern void GoogleMobileAdsClientFactory_BuildRewardedAdClient_mE947E4BBB661F4F464F0995B815C987FD26B177D (void);
// 0x0000009F GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildRewardedInterstitialAdClient()
extern void GoogleMobileAdsClientFactory_BuildRewardedInterstitialAdClient_m7AA712489261202B58FADB8EE40909487B79FB62 (void);
// 0x000000A0 GoogleMobileAds.Common.INativeOverlayAdClient GoogleMobileAds.GoogleMobileAdsClientFactory::BuildNativeOverlayAdClient()
extern void GoogleMobileAdsClientFactory_BuildNativeOverlayAdClient_m7B77A962907AA9310B423D4B91EA04458B77E0A7 (void);
// 0x000000A1 GoogleMobileAds.Common.IApplicationPreferencesClient GoogleMobileAds.GoogleMobileAdsClientFactory::ApplicationPreferencesInstance()
extern void GoogleMobileAdsClientFactory_ApplicationPreferencesInstance_m488ECD4316C948DA4F7A67FAC4F815FF2580F1AC (void);
// 0x000000A2 GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.GoogleMobileAdsClientFactory::MobileAdsInstance()
extern void GoogleMobileAdsClientFactory_MobileAdsInstance_mF8E999E2A7C2AAA9ACB0D18F478F3368A2C80270 (void);
// 0x000000A3 System.Void GoogleMobileAds.Android.InitializationStatusClient::.ctor(UnityEngine.AndroidJavaObject)
extern void InitializationStatusClient__ctor_m0A5A2C3A71BE10A20943AF9DEDAFD81AEF517C49 (void);
// 0x000000A4 GoogleMobileAds.Api.AdapterStatus GoogleMobileAds.Android.InitializationStatusClient::getAdapterStatusForClassName(System.String)
extern void InitializationStatusClient_getAdapterStatusForClassName_mC2F67D3EF84E3587425BB50CD0CB643564F79214 (void);
// 0x000000A5 System.Collections.Generic.Dictionary`2<System.String,GoogleMobileAds.Api.AdapterStatus> GoogleMobileAds.Android.InitializationStatusClient::getAdapterStatusMap()
extern void InitializationStatusClient_getAdapterStatusMap_m2E3F2B6AC0171BD072905B2697018CD244D19583 (void);
// 0x000000A6 System.String[] GoogleMobileAds.Android.InitializationStatusClient::getKeys()
extern void InitializationStatusClient_getKeys_m5CD44BACBC53E3D9559A6B38C54636AACCA92C3A (void);
// 0x000000A7 System.Void GoogleMobileAds.Android.InterstitialClient::.ctor()
extern void InterstitialClient__ctor_m0046CDB719B03D590B6E356A884B10D1EAF173E5 (void);
// 0x000000A8 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdLoaded_m07063D92BFEC39E271454020479CC90B9B4F9B3A (void);
// 0x000000A9 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdLoaded_m2D3180CC3E6D23840F294D866D09E1D5DEA6E88D (void);
// 0x000000AA System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void InterstitialClient_add_OnAdFailedToLoad_m2A7C1CD94B215716100E1E6AAFD284D4B5D77D47 (void);
// 0x000000AB System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void InterstitialClient_remove_OnAdFailedToLoad_mA0E4C72E83B953A0D6AD99B7FC552B22D4519644 (void);
// 0x000000AC System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void InterstitialClient_add_OnAdFailedToPresentFullScreenContent_mEE4DEC7B7671BFF1DB70E18FEF2F551DFFCB1EA7 (void);
// 0x000000AD System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void InterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m0EC96B3CF42CAF270A2A6CDAC233943539BC2575 (void);
// 0x000000AE System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdDidPresentFullScreenContent_mC6902B34E2C45CE47C5FAE05117BEB11D7D91465 (void);
// 0x000000AF System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdDidPresentFullScreenContent_mF18E16D8D780CEAFFDB9C0376A32BA85103EE964 (void);
// 0x000000B0 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdDidDismissFullScreenContent_m6CDA62483ED94196361C62DAB7FF0B2D10A1C369 (void);
// 0x000000B1 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdDidDismissFullScreenContent_m5F998C352AD81569324F2869F870341767E925EC (void);
// 0x000000B2 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_add_OnAdDidRecordImpression_m8764F9C580F3B8341A346A2AF3FC8166599C7434 (void);
// 0x000000B3 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void InterstitialClient_remove_OnAdDidRecordImpression_m0C444B50E64989149B59DB9BEF6077411A5CB2F8 (void);
// 0x000000B4 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void InterstitialClient_add_OnPaidEvent_m37BD2E68F7BA38FE65947B40D80F4414D2AE49D3 (void);
// 0x000000B5 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void InterstitialClient_remove_OnPaidEvent_mB4F5076C1EAA9B1C4AD3BD5C8A639D6DD4CBDE1D (void);
// 0x000000B6 System.Void GoogleMobileAds.Android.InterstitialClient::add_OnAdClicked(System.Action)
extern void InterstitialClient_add_OnAdClicked_m5C8BAA0D0B8F29608ADA456703F9E9B3D4A584B8 (void);
// 0x000000B7 System.Void GoogleMobileAds.Android.InterstitialClient::remove_OnAdClicked(System.Action)
extern void InterstitialClient_remove_OnAdClicked_m529A9963C15FFDF0C89816C5C796A4AE75C65931 (void);
// 0x000000B8 System.Void GoogleMobileAds.Android.InterstitialClient::CreateInterstitialAd()
extern void InterstitialClient_CreateInterstitialAd_m2D34F34A4EE6AC30888D5E42FE7F387752A066C1 (void);
// 0x000000B9 System.Void GoogleMobileAds.Android.InterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void InterstitialClient_LoadAd_m797AED7355744DEB02084DEEA67040F2E149DC42 (void);
// 0x000000BA System.Void GoogleMobileAds.Android.InterstitialClient::Show()
extern void InterstitialClient_Show_m38BEFDBA16B5A9DCE8EC0BCE0AF9960D954E610D (void);
// 0x000000BB System.Void GoogleMobileAds.Android.InterstitialClient::DestroyInterstitial()
extern void InterstitialClient_DestroyInterstitial_m99769CECB16B905F729BCC38C7D9EF39D9820F8E (void);
// 0x000000BC System.String GoogleMobileAds.Android.InterstitialClient::GetAdUnitID()
extern void InterstitialClient_GetAdUnitID_mB4F5FCAFB00A6450F60310432CFEC0A05D26E8F1 (void);
// 0x000000BD System.Boolean GoogleMobileAds.Android.InterstitialClient::IsAdAvailable(System.String)
extern void InterstitialClient_IsAdAvailable_m2877494B014114CA96B02BCBC47BB4E9B1DC7FE5 (void);
// 0x000000BE GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.Android.InterstitialClient::PollAd(System.String)
extern void InterstitialClient_PollAd_m46749DFE6FE30F94A69D8158C87AC62A741E019F (void);
// 0x000000BF GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.InterstitialClient::GetResponseInfoClient()
extern void InterstitialClient_GetResponseInfoClient_mF87067249A30656FB2D85D088534866F008E5C34 (void);
// 0x000000C0 System.Void GoogleMobileAds.Android.InterstitialClient::onInterstitialAdLoaded()
extern void InterstitialClient_onInterstitialAdLoaded_mF497C69BEE1FEE0F0FD5E0425B69F8F3085EF57E (void);
// 0x000000C1 System.Void GoogleMobileAds.Android.InterstitialClient::onInterstitialAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void InterstitialClient_onInterstitialAdFailedToLoad_mAED345A3CC012E3770BA5F3A6246669019F1497C (void);
// 0x000000C2 System.Void GoogleMobileAds.Android.InterstitialClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void InterstitialClient_onAdFailedToShowFullScreenContent_m9300E7914F254E3B985F89A88291710527B6DF90 (void);
// 0x000000C3 System.Void GoogleMobileAds.Android.InterstitialClient::onAdShowedFullScreenContent()
extern void InterstitialClient_onAdShowedFullScreenContent_m713BEBACEEACF05906AC1D6A726C62A0F0CBE7CB (void);
// 0x000000C4 System.Void GoogleMobileAds.Android.InterstitialClient::onAdDismissedFullScreenContent()
extern void InterstitialClient_onAdDismissedFullScreenContent_m639D15141E9D8555FFFCFE0ACBF53337BE053640 (void);
// 0x000000C5 System.Void GoogleMobileAds.Android.InterstitialClient::onAdImpression()
extern void InterstitialClient_onAdImpression_m917C895516B08862159F2BAE1B063E0FCC9AE747 (void);
// 0x000000C6 System.Void GoogleMobileAds.Android.InterstitialClient::onAdClicked()
extern void InterstitialClient_onAdClicked_m580B81D56B1DF03C99755F0E8CBA3EFE0FDBEFBE (void);
// 0x000000C7 System.Void GoogleMobileAds.Android.InterstitialClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void InterstitialClient_onPaidEvent_m7D99A23A9739A07FDC4775DD8A5316FFC331271D (void);
// 0x000000C8 System.Void GoogleMobileAds.Android.LoadAdErrorClient::.ctor(UnityEngine.AndroidJavaObject)
extern void LoadAdErrorClient__ctor_mAAB2CB3CA79F7FC1CDDA014A8194DDB6A26F9967 (void);
// 0x000000C9 System.Int32 GoogleMobileAds.Android.LoadAdErrorClient::GetCode()
extern void LoadAdErrorClient_GetCode_m1E3E3BAE9933C1C80B2F302BBE61D3EA4DA9638C (void);
// 0x000000CA System.String GoogleMobileAds.Android.LoadAdErrorClient::GetDomain()
extern void LoadAdErrorClient_GetDomain_m35585024A100B399625FC5AE66A6D1BD7D1B4255 (void);
// 0x000000CB System.String GoogleMobileAds.Android.LoadAdErrorClient::GetMessage()
extern void LoadAdErrorClient_GetMessage_mCC4D7B22A5DAA57D9E4C4FBAABABACBD3DD1F220 (void);
// 0x000000CC GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Android.LoadAdErrorClient::GetCause()
extern void LoadAdErrorClient_GetCause_m9B8B220890D751A94FDB0A8869ACD333B8A9842E (void);
// 0x000000CD GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.LoadAdErrorClient::GetResponseInfoClient()
extern void LoadAdErrorClient_GetResponseInfoClient_mCA1E3314C9FC6F5D90B6332CA03ACDC21D4759BF (void);
// 0x000000CE System.String GoogleMobileAds.Android.LoadAdErrorClient::ToString()
extern void LoadAdErrorClient_ToString_mC2DB12F6A131B98CD61C244E9A54053BAE54D792 (void);
// 0x000000CF System.Void GoogleMobileAds.Android.MobileAdsClient::.ctor()
extern void MobileAdsClient__ctor_mDBED7FEC13313685E587BAB4009F3786A9EBDA8F (void);
// 0x000000D0 GoogleMobileAds.Android.MobileAdsClient GoogleMobileAds.Android.MobileAdsClient::get_Instance()
extern void MobileAdsClient_get_Instance_m0C849C8A0818CB529BEEB5B77E94FBD1DA87C4D8 (void);
// 0x000000D1 System.Void GoogleMobileAds.Android.MobileAdsClient::Initialize(System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>)
extern void MobileAdsClient_Initialize_m2921FC0DE0E8930BFD279EEA0831733FF2457C40 (void);
// 0x000000D2 System.Void GoogleMobileAds.Android.MobileAdsClient::SetApplicationVolume(System.Single)
extern void MobileAdsClient_SetApplicationVolume_mED369113DB00AB208B4E47A72C5ACB3DE2317899 (void);
// 0x000000D3 System.Void GoogleMobileAds.Android.MobileAdsClient::DisableMediationInitialization()
extern void MobileAdsClient_DisableMediationInitialization_m266F611C99F1766B0028A14AEF5CE710C60381B4 (void);
// 0x000000D4 System.Void GoogleMobileAds.Android.MobileAdsClient::SetApplicationMuted(System.Boolean)
extern void MobileAdsClient_SetApplicationMuted_m80E4D9E3DCE21608F632EB5FB2FCB857E2AE7055 (void);
// 0x000000D5 System.Void GoogleMobileAds.Android.MobileAdsClient::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
extern void MobileAdsClient_SetRequestConfiguration_mDE0A431E5D075CF5FAF8A45864ECEA9B61757897 (void);
// 0x000000D6 GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Android.MobileAdsClient::GetRequestConfiguration()
extern void MobileAdsClient_GetRequestConfiguration_m2F4C593D01ABC588EDF10CB91DF2533A885A9B0B (void);
// 0x000000D7 System.Void GoogleMobileAds.Android.MobileAdsClient::SetiOSAppPauseOnBackground(System.Boolean)
extern void MobileAdsClient_SetiOSAppPauseOnBackground_m91239F1DDD61CE3F8441C8434B77EC6A8F4080A0 (void);
// 0x000000D8 System.Void GoogleMobileAds.Android.MobileAdsClient::DisableSDKCrashReporting()
extern void MobileAdsClient_DisableSDKCrashReporting_m498A10B14F5CDDC20184540E8911454F59B58D3B (void);
// 0x000000D9 System.Void GoogleMobileAds.Android.MobileAdsClient::OpenAdInspector(System.Action`1<GoogleMobileAds.Common.AdInspectorErrorClientEventArgs>)
extern void MobileAdsClient_OpenAdInspector_m6F19B5F80831FABE486EAAFC738F4FF49FA51ED4 (void);
// 0x000000DA System.Void GoogleMobileAds.Android.MobileAdsClient::Preload(System.Collections.Generic.List`1<GoogleMobileAds.Api.PreloadConfiguration>,System.Action`1<GoogleMobileAds.Api.PreloadConfiguration>,System.Action`1<GoogleMobileAds.Api.PreloadConfiguration>)
extern void MobileAdsClient_Preload_mE2E6BEEA61BC80B5DC43F1AB59344E90024B3199 (void);
// 0x000000DB System.Single GoogleMobileAds.Android.MobileAdsClient::GetDeviceScale()
extern void MobileAdsClient_GetDeviceScale_m9AA91F81BC16A2FA45370718FCBA5C649CC765DF (void);
// 0x000000DC System.Int32 GoogleMobileAds.Android.MobileAdsClient::GetDeviceSafeWidth()
extern void MobileAdsClient_GetDeviceSafeWidth_mC892F93688B64CA9C2C4F380F0D335556CF77889 (void);
// 0x000000DD System.Version GoogleMobileAds.Android.MobileAdsClient::GetSDKVersion()
extern void MobileAdsClient_GetSDKVersion_mE5CEDA1CC85442C46B734A50F87A28659EF27172 (void);
// 0x000000DE System.Void GoogleMobileAds.Android.MobileAdsClient::onInitializationComplete(UnityEngine.AndroidJavaObject)
extern void MobileAdsClient_onInitializationComplete_mF4F780F1B71E469AC5CB452695BA1DFD97E59938 (void);
// 0x000000DF System.Void GoogleMobileAds.Android.MobileAdsClient::.cctor()
extern void MobileAdsClient__cctor_mE401FAC02059DAD69A194FEE3BFF8613C7914F85 (void);
// 0x000000E0 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::.ctor()
extern void NativeOverlayAdClient__ctor_m35ABD2F46C9077900F9787528146C838A1FE2566 (void);
// 0x000000E1 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_add_OnAdLoaded_mC3BB8057D3779E8F61432FE7A592ACD518691FE4 (void);
// 0x000000E2 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_remove_OnAdLoaded_m619DFB123FBCB27B2E2C32D31A31D723A8B772BF (void);
// 0x000000E3 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void NativeOverlayAdClient_add_OnAdFailedToLoad_mB5CBB814BAB55788F71551410CD795C7AED1376D (void);
// 0x000000E4 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void NativeOverlayAdClient_remove_OnAdFailedToLoad_mFDD3F2C27EA188957EF0C07AA12F068EE611F108 (void);
// 0x000000E5 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_add_OnAdDidRecordImpression_m816C8863E071AF34C97DB5500C3F8BBAB0EF9B93 (void);
// 0x000000E6 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_remove_OnAdDidRecordImpression_mA604C1D3E524D1EA5DBDD7230F7F35BF6BEFD217 (void);
// 0x000000E7 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_add_OnAdDidPresentFullScreenContent_m427D70740EE8BEC9B130C1A4786E63147D614CEB (void);
// 0x000000E8 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_remove_OnAdDidPresentFullScreenContent_mDFA8B843AF6AABBF76B6FE7E0878695C3066BFD2 (void);
// 0x000000E9 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_add_OnAdDidDismissFullScreenContent_mF4498DC2098194765761D15394B2CA5FF9495AC2 (void);
// 0x000000EA System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void NativeOverlayAdClient_remove_OnAdDidDismissFullScreenContent_m173CA4FE6BB5866BFD204C65E46F83D8F384B401 (void);
// 0x000000EB System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnAdClicked(System.Action)
extern void NativeOverlayAdClient_add_OnAdClicked_m18D5583F8859D15C77F4851F571AC22D2CF60B33 (void);
// 0x000000EC System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnAdClicked(System.Action)
extern void NativeOverlayAdClient_remove_OnAdClicked_mA149D110C4F687DCAFF90DEE99AD01F222B6ED16 (void);
// 0x000000ED System.Void GoogleMobileAds.Android.NativeOverlayAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void NativeOverlayAdClient_add_OnPaidEvent_mA90B00527EDF35DEB130DAF56D9E4D7B5DA18865 (void);
// 0x000000EE System.Void GoogleMobileAds.Android.NativeOverlayAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void NativeOverlayAdClient_remove_OnPaidEvent_mBDF33F011BEFEA34385F42998BC1614F495BB61A (void);
// 0x000000EF System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Load(System.String,GoogleMobileAds.Api.AdRequest,GoogleMobileAds.Api.NativeAdOptions)
extern void NativeOverlayAdClient_Load_mCDAA08EFF3B520F4C155F69339C076DF031DA26C (void);
// 0x000000F0 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Hide()
extern void NativeOverlayAdClient_Hide_m75634D9A90F4D5A5B7487904B57176562CC3F54D (void);
// 0x000000F1 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Show()
extern void NativeOverlayAdClient_Show_m1A62C8AE06F3D1B4A7D8E787548D7B33609A3E9A (void);
// 0x000000F2 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::SetPosition(GoogleMobileAds.Api.AdPosition)
extern void NativeOverlayAdClient_SetPosition_mDF380BB94C0D81757B638DDA76F5A31916B5533C (void);
// 0x000000F3 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::SetPosition(System.Int32,System.Int32)
extern void NativeOverlayAdClient_SetPosition_m9F040367F83809C60D9824E634C5B99DAE111901 (void);
// 0x000000F4 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
extern void NativeOverlayAdClient_Render_m736BD49FD9602F20FFF07212A2215B7C06C9F31D (void);
// 0x000000F5 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,GoogleMobileAds.Api.AdSize,System.Int32,System.Int32)
extern void NativeOverlayAdClient_Render_m4AC7583411A7347EF635ACA57E5003B07A4C2301 (void);
// 0x000000F6 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,GoogleMobileAds.Api.AdPosition)
extern void NativeOverlayAdClient_Render_mE7E3A95183DE96EB1A8D84D4B6B42C6540D77559 (void);
// 0x000000F7 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,System.Int32,System.Int32)
extern void NativeOverlayAdClient_Render_m1F3FC83F77C11B6416315B0CFB488CEE97696EBC (void);
// 0x000000F8 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::DestroyAd()
extern void NativeOverlayAdClient_DestroyAd_m53A8C433B9D06CC3A50969D9AFF65C2C3CCF048F (void);
// 0x000000F9 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.NativeOverlayAdClient::GetResponseInfoClient()
extern void NativeOverlayAdClient_GetResponseInfoClient_m4F3960A8587C8CF06D74A89E8CFC9DDB4BB39BAA (void);
// 0x000000FA System.Single GoogleMobileAds.Android.NativeOverlayAdClient::GetHeightInPixels()
extern void NativeOverlayAdClient_GetHeightInPixels_mADE067F4F90EC50D322EA298FAB42AE08EE416A6 (void);
// 0x000000FB System.Single GoogleMobileAds.Android.NativeOverlayAdClient::GetWidthInPixels()
extern void NativeOverlayAdClient_GetWidthInPixels_mBC5626792CBF6751AF90420F1CB614A96672D2F8 (void);
// 0x000000FC System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onNativeAdLoaded()
extern void NativeOverlayAdClient_onNativeAdLoaded_m3F52EC1B6F74C80C9170CFC142778C216912D3D5 (void);
// 0x000000FD System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onNativeAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void NativeOverlayAdClient_onNativeAdFailedToLoad_m840F8F49D9C8BE7044D525649013ADDF903D81A9 (void);
// 0x000000FE System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onAdImpression()
extern void NativeOverlayAdClient_onAdImpression_mC6D184AADE70394C36B65902F831E0BE5EDB0602 (void);
// 0x000000FF System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onAdClicked()
extern void NativeOverlayAdClient_onAdClicked_m9D68731BE703BB135C97A7188319D37E4EFA8BCB (void);
// 0x00000100 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onAdShowedFullScreenContent()
extern void NativeOverlayAdClient_onAdShowedFullScreenContent_mCF1C44F09FB4968DADD2BD8A6FECCB8E7ECF3FBE (void);
// 0x00000101 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onAdDismissedFullScreenContent()
extern void NativeOverlayAdClient_onAdDismissedFullScreenContent_m8DA765B0B51913FFAB3FCE9BD117758A326FD440 (void);
// 0x00000102 System.Void GoogleMobileAds.Android.NativeOverlayAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void NativeOverlayAdClient_onPaidEvent_m358BCBFC916F409348FB5586060B51AC1130FF41 (void);
// 0x00000103 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.NativeOverlayAdClient::GetNativeTemplateStyleJavaObject(GoogleMobileAds.Api.NativeTemplateStyle)
extern void NativeOverlayAdClient_GetNativeTemplateStyleJavaObject_m82F11397AACB0D8C004F2F8EB918EAEB6C3611BE (void);
// 0x00000104 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.NativeOverlayAdClient::GetNativeTemplateTextStyleJavaObject(GoogleMobileAds.Api.NativeTemplateTextStyle)
extern void NativeOverlayAdClient_GetNativeTemplateTextStyleJavaObject_m61D41CB228A5070CB308845EC996DB2773571D49 (void);
// 0x00000105 System.Void GoogleMobileAds.Android.PreloadListener::.ctor(System.Action`1<GoogleMobileAds.Api.PreloadConfiguration>,System.Action`1<GoogleMobileAds.Api.PreloadConfiguration>)
extern void PreloadListener__ctor_m3E934E1D71A2A1A872D1AC20DF5AC47C31999258 (void);
// 0x00000106 System.Void GoogleMobileAds.Android.PreloadListener::onAdsAvailable(UnityEngine.AndroidJavaObject)
extern void PreloadListener_onAdsAvailable_mCE6335B2B7724956CD3B04F7EAE7037B6D3B9C92 (void);
// 0x00000107 System.Void GoogleMobileAds.Android.PreloadListener::onAdsExhausted(UnityEngine.AndroidJavaObject)
extern void PreloadListener_onAdsExhausted_mBBC008EBF3F54AEC0AB6EC1857D165CE1E58125A (void);
// 0x00000108 System.Void GoogleMobileAds.Android.RequestConfigurationClient::.ctor()
extern void RequestConfigurationClient__ctor_m4F8B07D96798B93FBAB17E6015A4DDEA86332F71 (void);
// 0x00000109 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.RequestConfigurationClient::BuildRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
extern void RequestConfigurationClient_BuildRequestConfiguration_m8F0EB5E489CADE0C8A0A6DE37723A1D5E12D87D7 (void);
// 0x0000010A GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Android.RequestConfigurationClient::GetRequestConfiguration(UnityEngine.AndroidJavaObject)
extern void RequestConfigurationClient_GetRequestConfiguration_mF82939C37FF82486FBCAC91CECDF0AB8808C4A6B (void);
// 0x0000010B System.Void GoogleMobileAds.Android.ResponseInfoClient::.ctor(GoogleMobileAds.Common.ResponseInfoClientType,UnityEngine.AndroidJavaObject)
extern void ResponseInfoClient__ctor_mB3010C36F05A40A73A03FC081FBBD7A15C528EDC (void);
// 0x0000010C System.Collections.Generic.List`1<GoogleMobileAds.Common.IAdapterResponseInfoClient> GoogleMobileAds.Android.ResponseInfoClient::GetAdapterResponses()
extern void ResponseInfoClient_GetAdapterResponses_mED5C3350D09693349549CAB292D4AAAA48025335 (void);
// 0x0000010D GoogleMobileAds.Common.IAdapterResponseInfoClient GoogleMobileAds.Android.ResponseInfoClient::GetLoadedAdapterResponseInfo()
extern void ResponseInfoClient_GetLoadedAdapterResponseInfo_mF951381D9B3302BD82FC914835C6FA84A6C870E5 (void);
// 0x0000010E System.String GoogleMobileAds.Android.ResponseInfoClient::GetMediationAdapterClassName()
extern void ResponseInfoClient_GetMediationAdapterClassName_m841D4E968FB1AAFD9B0973287701C61036B3FC1D (void);
// 0x0000010F System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Android.ResponseInfoClient::GetResponseExtras()
extern void ResponseInfoClient_GetResponseExtras_m408FC75D4FC44779CB2494552DC28CD0C7B0D0B3 (void);
// 0x00000110 System.String GoogleMobileAds.Android.ResponseInfoClient::GetResponseId()
extern void ResponseInfoClient_GetResponseId_mB16EC1E99B79FBE6200426B8511E5CCE95B47981 (void);
// 0x00000111 System.String GoogleMobileAds.Android.ResponseInfoClient::ToString()
extern void ResponseInfoClient_ToString_m024342CDAF182849718895EF4A4EC24E59D2B1F9 (void);
// 0x00000112 System.Void GoogleMobileAds.Android.RewardedAdClient::.ctor()
extern void RewardedAdClient__ctor_m2086861247473AE89A2ED13636930BE90A015194 (void);
// 0x00000113 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdLoaded_m76A83EA046A32E9169B9B36479170754704F0495 (void);
// 0x00000114 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdLoaded_m9D9890947879ACD3D959B609F4072F4972DECA48 (void);
// 0x00000115 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedAdClient_add_OnAdFailedToLoad_m4C8B615252DDDC96553C02B9CF5A524DD17B4302 (void);
// 0x00000116 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedAdClient_remove_OnAdFailedToLoad_mCDC431B3B322CE896AABA9B3EE7B3B861AB949D6 (void);
// 0x00000117 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedAdClient_add_OnUserEarnedReward_m316CAE6E39A916EFD57A99C72140A9175C44FD26 (void);
// 0x00000118 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedAdClient_remove_OnUserEarnedReward_m29F7B4FC80286ADC6635449EE342136C52D1DE50 (void);
// 0x00000119 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedAdClient_add_OnPaidEvent_m5E5BC0A875AA7A686D295BF106C43EA50C0FEC86 (void);
// 0x0000011A System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedAdClient_remove_OnPaidEvent_mF5687F8689BA6CF373BBF8F478A5F8194E4B8E6D (void);
// 0x0000011B System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedAdClient_add_OnAdFailedToPresentFullScreenContent_mFFFEA4D36186A650F0A0B3CFC4A38DA54174CBCA (void);
// 0x0000011C System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedAdClient_remove_OnAdFailedToPresentFullScreenContent_m4EF48F65582F95036DD28A146FB2CB29B9CC3E9E (void);
// 0x0000011D System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdDidPresentFullScreenContent_mE0FEC27A7ED1D2F0A8E2B3DD5CD84E59CC3EA989 (void);
// 0x0000011E System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdDidPresentFullScreenContent_m9B06F5C51458C65EE435A61A5B74964FB72D2F3A (void);
// 0x0000011F System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdDidDismissFullScreenContent_mFC264149FD303B89D9C415E25388E286158D95F1 (void);
// 0x00000120 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdDidDismissFullScreenContent_mD9A14E399471D6A39D4B523D2E440CF345950F2C (void);
// 0x00000121 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_add_OnAdDidRecordImpression_mBC469B8B2290A444847C444CF7724E7A83BF749A (void);
// 0x00000122 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedAdClient_remove_OnAdDidRecordImpression_m58353D0EB123644A534F52B095EF86E63F434272 (void);
// 0x00000123 System.Void GoogleMobileAds.Android.RewardedAdClient::add_OnAdClicked(System.Action)
extern void RewardedAdClient_add_OnAdClicked_m6E08AE90876C3ECC9903B6272ED8CA3E740A4783 (void);
// 0x00000124 System.Void GoogleMobileAds.Android.RewardedAdClient::remove_OnAdClicked(System.Action)
extern void RewardedAdClient_remove_OnAdClicked_mD16DD234D085AF2753C60255FDD93550EC29274C (void);
// 0x00000125 System.Void GoogleMobileAds.Android.RewardedAdClient::CreateRewardedAd()
extern void RewardedAdClient_CreateRewardedAd_m251A1B20C3726BFED8A9397DC1960FBB39E30FA4 (void);
// 0x00000126 System.Void GoogleMobileAds.Android.RewardedAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void RewardedAdClient_LoadAd_m5AE53471AF85CA169C5BFAC9A15073BD717D5172 (void);
// 0x00000127 System.Void GoogleMobileAds.Android.RewardedAdClient::Show()
extern void RewardedAdClient_Show_m73423F0E14613315864F0C933C6244FBBA3CCA21 (void);
// 0x00000128 System.Void GoogleMobileAds.Android.RewardedAdClient::SetServerSideVerificationOptions(GoogleMobileAds.Api.ServerSideVerificationOptions)
extern void RewardedAdClient_SetServerSideVerificationOptions_m5169D5A142F07ADF313CE231282EF5A9BFD10971 (void);
// 0x00000129 GoogleMobileAds.Api.Reward GoogleMobileAds.Android.RewardedAdClient::GetRewardItem()
extern void RewardedAdClient_GetRewardItem_mA0729ED1397412164EBC06B68C179B2A0F3ED2B4 (void);
// 0x0000012A System.String GoogleMobileAds.Android.RewardedAdClient::GetAdUnitID()
extern void RewardedAdClient_GetAdUnitID_mD7079C7796D00572B48361FF5B0776112B07DF21 (void);
// 0x0000012B System.Boolean GoogleMobileAds.Android.RewardedAdClient::IsAdAvailable(System.String)
extern void RewardedAdClient_IsAdAvailable_mD1D45163B6DE3748F13BCEE7816DF55F8BD2F768 (void);
// 0x0000012C GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.Android.RewardedAdClient::PollAd(System.String)
extern void RewardedAdClient_PollAd_mEA518ECF81E82940A35B63D7ECDC41A76AADE5DF (void);
// 0x0000012D GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.RewardedAdClient::GetResponseInfoClient()
extern void RewardedAdClient_GetResponseInfoClient_m656FAB7CAE38A9C2C3ABBAC0C81B345983FE5D77 (void);
// 0x0000012E System.Void GoogleMobileAds.Android.RewardedAdClient::DestroyRewardedAd()
extern void RewardedAdClient_DestroyRewardedAd_m86BCC2B6506D90BD702B6FCD4039B999D4559D9F (void);
// 0x0000012F System.Void GoogleMobileAds.Android.RewardedAdClient::onRewardedAdLoaded()
extern void RewardedAdClient_onRewardedAdLoaded_m47B4E8ABC89437758084BB04D581542F5B647A1C (void);
// 0x00000130 System.Void GoogleMobileAds.Android.RewardedAdClient::onRewardedAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void RewardedAdClient_onRewardedAdFailedToLoad_m3789A9FF6433620D8418CEC0EF8FE169CC491640 (void);
// 0x00000131 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void RewardedAdClient_onAdFailedToShowFullScreenContent_mAC11D570CB1C6E39D3074EF4D35E90CFF5D13E22 (void);
// 0x00000132 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdShowedFullScreenContent()
extern void RewardedAdClient_onAdShowedFullScreenContent_mD010B5B214BFF75B94C96AC3E4CACD36F41FD931 (void);
// 0x00000133 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdDismissedFullScreenContent()
extern void RewardedAdClient_onAdDismissedFullScreenContent_mDDAFDD797E0F08646BF92024F729622BA03A224F (void);
// 0x00000134 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdImpression()
extern void RewardedAdClient_onAdImpression_mF9E29BE592138FCA88C4ED6749D0A5911FE9ECD4 (void);
// 0x00000135 System.Void GoogleMobileAds.Android.RewardedAdClient::onAdClicked()
extern void RewardedAdClient_onAdClicked_m23B3C998A6FAAECDC0BB49F1C2BAD077E3E7E541 (void);
// 0x00000136 System.Void GoogleMobileAds.Android.RewardedAdClient::onUserEarnedReward(System.String,System.Single)
extern void RewardedAdClient_onUserEarnedReward_m2A49D078B6795CAFD1D690BFD3B6C2D9EA1CD155 (void);
// 0x00000137 System.Void GoogleMobileAds.Android.RewardedAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void RewardedAdClient_onPaidEvent_m9808D3DA5A710D7D8E3E84F36E1B79520D954E67 (void);
// 0x00000138 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::.ctor()
extern void RewardedInterstitialAdClient__ctor_m7DCD4A3A92E137ADC479BF330963BD4676375E11 (void);
// 0x00000139 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdLoaded_m25D0E3DCDAE16821388DB623D0BB4F390B16CB1D (void);
// 0x0000013A System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdLoaded_mA6B686A99FE5C57789517D13DD0EEF34CC657F3E (void);
// 0x0000013B System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdFailedToLoad_m1507674796F89D1394CAAA4283E3A7953CB71071 (void);
// 0x0000013C System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdFailedToLoad_m29D801FFA53BE78F7684CB75F69E911919B52A85 (void);
// 0x0000013D System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedInterstitialAdClient_add_OnUserEarnedReward_mDD3C9F7E39B8B675284CE62040DDFFA81906E6CC (void);
// 0x0000013E System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
extern void RewardedInterstitialAdClient_remove_OnUserEarnedReward_m6CBD114149C539E02F7D1E446C534AE4C176A072 (void);
// 0x0000013F System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedInterstitialAdClient_add_OnPaidEvent_m5478036DB8E6F4DC212C24B371FABCD1BF7F2325 (void);
// 0x00000140 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
extern void RewardedInterstitialAdClient_remove_OnPaidEvent_m187C0902BF3F923DB4FC0AE30445D1EA44137B97 (void);
// 0x00000141 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdFailedToPresentFullScreenContent_mCC615B2C3E502F90474A4382E5733B93C0374D1B (void);
// 0x00000142 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdFailedToPresentFullScreenContent_m06671EFBF8F2279F8BAC134C7696A9FF592F7E20 (void);
// 0x00000143 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdDidPresentFullScreenContent_m319D27B3E285874805D80E5DB185A4C25A86EF06 (void);
// 0x00000144 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdDidPresentFullScreenContent_mD78534356A3DD83D37BF6ADE855DFCDF7FBEF443 (void);
// 0x00000145 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdDidDismissFullScreenContent_mA9E66A0515F34D7E17875BE866215DC953C9E77B (void);
// 0x00000146 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdDidDismissFullScreenContent_m6319B9040A03378437283C9F59FB54623AE2DBB4 (void);
// 0x00000147 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_add_OnAdDidRecordImpression_mB5A227A3AD02FEF148C2E39B7C141BDE897BE9EB (void);
// 0x00000148 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
extern void RewardedInterstitialAdClient_remove_OnAdDidRecordImpression_m5AAD9D74B407DED10906F9EF5C736768A33C838F (void);
// 0x00000149 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::add_OnAdClicked(System.Action)
extern void RewardedInterstitialAdClient_add_OnAdClicked_mDD0AF7B806B0558A61CA4B9663439609A8789F7E (void);
// 0x0000014A System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::remove_OnAdClicked(System.Action)
extern void RewardedInterstitialAdClient_remove_OnAdClicked_m453176735897016B916E8DC4B47A70E3D6020C09 (void);
// 0x0000014B System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::CreateRewardedInterstitialAd()
extern void RewardedInterstitialAdClient_CreateRewardedInterstitialAd_m721A8F8D1915ECA4A0759F6AD278E1B6EFE90FD6 (void);
// 0x0000014C System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
extern void RewardedInterstitialAdClient_LoadAd_m2950E1CB22284C315B13263EDBEBE8311DB027E0 (void);
// 0x0000014D System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::Show()
extern void RewardedInterstitialAdClient_Show_m348AEC7D9D65FC6DC7C160615008AAD3EFA934FA (void);
// 0x0000014E GoogleMobileAds.Api.Reward GoogleMobileAds.Android.RewardedInterstitialAdClient::GetRewardItem()
extern void RewardedInterstitialAdClient_GetRewardItem_mFFE7C78154E911845BEF729A31D55CC29ED0B05D (void);
// 0x0000014F System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::SetServerSideVerificationOptions(GoogleMobileAds.Api.ServerSideVerificationOptions)
extern void RewardedInterstitialAdClient_SetServerSideVerificationOptions_m9E0A837733D7DC9D301401D8CFCA08E57054DFF6 (void);
// 0x00000150 System.String GoogleMobileAds.Android.RewardedInterstitialAdClient::GetAdUnitID()
extern void RewardedInterstitialAdClient_GetAdUnitID_m397CE8942E1A57BAC78ED8BCF92E29FA7C0F22F0 (void);
// 0x00000151 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Android.RewardedInterstitialAdClient::GetResponseInfoClient()
extern void RewardedInterstitialAdClient_GetResponseInfoClient_m423E814E1FA46FAA43F23672C11FEFA2A59616B2 (void);
// 0x00000152 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::DestroyRewardedInterstitialAd()
extern void RewardedInterstitialAdClient_DestroyRewardedInterstitialAd_m087C8FA09FD7BCD2946C738FE67B49FBB4A7244E (void);
// 0x00000153 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onRewardedInterstitialAdLoaded()
extern void RewardedInterstitialAdClient_onRewardedInterstitialAdLoaded_mA1B474386B635F71B9B745355FC8A1431ED62A15 (void);
// 0x00000154 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onRewardedInterstitialAdFailedToLoad(UnityEngine.AndroidJavaObject)
extern void RewardedInterstitialAdClient_onRewardedInterstitialAdFailedToLoad_mA02DF39CB4F953517E20464FADDD61E6ADA8517B (void);
// 0x00000155 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdFailedToShowFullScreenContent(UnityEngine.AndroidJavaObject)
extern void RewardedInterstitialAdClient_onAdFailedToShowFullScreenContent_m853771E331C548A39C18F8421C2EF694A9039B7E (void);
// 0x00000156 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdShowedFullScreenContent()
extern void RewardedInterstitialAdClient_onAdShowedFullScreenContent_mF70285F43F6E05885DB3A7BC4CF9337FF65A4BBF (void);
// 0x00000157 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdDismissedFullScreenContent()
extern void RewardedInterstitialAdClient_onAdDismissedFullScreenContent_m51F1F86A8E4F74F94D596E480B2086DC41DBC682 (void);
// 0x00000158 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdImpression()
extern void RewardedInterstitialAdClient_onAdImpression_m2BEDE09D31150879FF91B30C300CD57B88B431A1 (void);
// 0x00000159 System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onAdClicked()
extern void RewardedInterstitialAdClient_onAdClicked_m1BA2E359C6C6A21DCCB8A50A8D021FB5A7E8166A (void);
// 0x0000015A System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onUserEarnedReward(System.String,System.Single)
extern void RewardedInterstitialAdClient_onUserEarnedReward_m35797F6B822515A1A775B0FB9B2C868C82EECF5F (void);
// 0x0000015B System.Void GoogleMobileAds.Android.RewardedInterstitialAdClient::onPaidEvent(System.Int32,System.Int64,System.String)
extern void RewardedInterstitialAdClient_onPaidEvent_mE92D596D43C191C3CDC49C4221433D48C6B4C51F (void);
// 0x0000015C System.Void GoogleMobileAds.Android.Utils::.ctor()
extern void Utils__ctor_m4005C63CB1C37042333363AA9055D2E96CC9E877 (void);
// 0x0000015D UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetAdSizeJavaObject(GoogleMobileAds.Api.AdSize)
extern void Utils_GetAdSizeJavaObject_m4043EE77245135A288B27C4ABEF9D40C190E249E (void);
// 0x0000015E System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Android.Utils::GetDictionary(UnityEngine.AndroidJavaObject)
extern void Utils_GetDictionary_mF53B34A1FA55C966E576F0E597CE8C74D9169664 (void);
// 0x0000015F System.Int32 GoogleMobileAds.Android.Utils::GetScreenWidth()
extern void Utils_GetScreenWidth_m3E783A25A917A05D2E3EE0771A258011D7351ACA (void);
// 0x00000160 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetAdRequestJavaObject(GoogleMobileAds.Api.AdRequest,System.String)
extern void Utils_GetAdRequestJavaObject_m1B081FD57A546B7181D07C58C1101E57A227EF7E (void);
// 0x00000161 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetAdManagerAdRequestJavaObject(GoogleMobileAds.Api.AdRequest,System.String)
extern void Utils_GetAdManagerAdRequestJavaObject_m30E35471BE07BC674168CEA59A83A943B16FEA76 (void);
// 0x00000162 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetPreloadConfigurationJavaObject(GoogleMobileAds.Api.PreloadConfiguration)
extern void Utils_GetPreloadConfigurationJavaObject_m53ECBFCBDD9F8F62CFABEEBB1B75E9F2D55DB2FB (void);
// 0x00000163 GoogleMobileAds.Api.PreloadConfiguration GoogleMobileAds.Android.Utils::GetPreloadConfiguration(UnityEngine.AndroidJavaObject)
extern void Utils_GetPreloadConfiguration_m569C2C24AAB65E458D1AF8FC0EE4469E9120CA1A (void);
// 0x00000164 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetJavaListObject(System.Collections.Generic.List`1<System.String>)
extern void Utils_GetJavaListObject_mD6C5663A9E13A398147EFCD17EC9283239B257B8 (void);
// 0x00000165 System.Collections.Generic.List`1<System.String> GoogleMobileAds.Android.Utils::GetCsTypeList(UnityEngine.AndroidJavaObject)
extern void Utils_GetCsTypeList_m14B7F25395CB6945B5F1DD35C713B627AE02DAB8 (void);
// 0x00000166 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetServerSideVerificationOptionsJavaObject(GoogleMobileAds.Api.ServerSideVerificationOptions)
extern void Utils_GetServerSideVerificationOptionsJavaObject_m167BC5038C51E5F410DAC9BB86617E8A4F4389C7 (void);
// 0x00000167 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetNativeAdOptionsJavaObject(GoogleMobileAds.Api.NativeAdOptions)
extern void Utils_GetNativeAdOptionsJavaObject_m827B5A98D97FE8636879E37CE451EC3EA5E903A4 (void);
// 0x00000168 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetVideoOptionsJavaObject(GoogleMobileAds.Api.VideoOptions)
extern void Utils_GetVideoOptionsJavaObject_m151ADF8276914528289A75E1F757859FD76E55EA (void);
// 0x00000169 UnityEngine.AndroidJavaObject GoogleMobileAds.Android.Utils::GetCurrentActivityAndroidJavaObject()
extern void Utils_GetCurrentActivityAndroidJavaObject_m2A63222E44E8FB1766E8EA4AE6F8A624C441A3BD (void);
static Il2CppMethodPointer s_methodPointers[361] = 
{
	AdErrorClient__ctor_m88DBD2BAF730F42FE760FD01615B69CD260FC334,
	AdErrorClient_GetCode_m76DE51A678EBE5ED59E61C0E38DF817A7A1FBCFA,
	AdErrorClient_GetDomain_m3CD89CD160405DEA1DCB44C5977E626629EB38E6,
	AdErrorClient_GetMessage_m876728CC16F4DBCF29AEA4D0FE301EFC95A35595,
	AdErrorClient_GetCause_m5852A32BB1A66A4BBD7C46D65E332F393C3F3D34,
	AdErrorClient_ToString_mC10A5B4E0830154DD766DFE4A78D587A4FF1881A,
	AdInspectorErrorClient__ctor_m690F6D0D0CA5AAA979E8A8BAD1F5F97E5E769B40,
	AdInspectorListener__ctor_m0B2D2DCE53CD0692CE4D07EA36FDF891CBE71DE2,
	AdInspectorListener_onAdInspectorClosed_mDA7B29960EF40C32F591934D6A5F36697B0CA564,
	AdManagerBannerClient__ctor_mFC060A869E58CA1AA869420809741EA9CCBFC2D6,
	AdManagerBannerClient_add_OnAppEvent_m9BC36DAB94A5F0936DA4B11A0BC6FC07197A8412,
	AdManagerBannerClient_remove_OnAppEvent_m3D886B78264EBE03EE545AE03E3E0A56D1201469,
	AdManagerBannerClient_get_ValidAdSizes_m1A2462C94FB067C2420D25DA4766AE1A02E9F028,
	AdManagerBannerClient_set_ValidAdSizes_mF2CB220CCDC3C0DECB6F121693321622F4F37374,
	AdManagerBannerClient_LoadAd_mA8922886629F2D634D50E9E659BEBDF7FCCBB316,
	AdManagerBannerClient_onAppEvent_m7D7736CEFDF2A91526462DA9094C80718F1E0E26,
	AdManagerInterstitialClient__ctor_mC2BA8FC797B0B094E8F097AF41E87C30618B4DF1,
	AdManagerInterstitialClient_add_OnAdLoaded_m2E1436BF40EA10F3A4F01D4AAE3C11F8AFFA8E31,
	AdManagerInterstitialClient_remove_OnAdLoaded_m1CF1F59EB3117AB278BB56520D15FB2B431B5663,
	AdManagerInterstitialClient_add_OnAdFailedToLoad_mC08209147BCCBAB6DF63A3A820C5358806396E23,
	AdManagerInterstitialClient_remove_OnAdFailedToLoad_m59F607A89A9B4A0BFDAAA1D29EAAC02C982A753F,
	AdManagerInterstitialClient_add_OnAdFailedToPresentFullScreenContent_m336DB4A57D1157CFD5FD2C2C1589354BE2D63047,
	AdManagerInterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m75C475AE28279D367F7ADB9B54D42B1DC2CC8E90,
	AdManagerInterstitialClient_add_OnAdDidPresentFullScreenContent_mC8F8ECC60E5137E0D0F0FED4CEF3F69F2BCACA9B,
	AdManagerInterstitialClient_remove_OnAdDidPresentFullScreenContent_m3CD52CD0239906E2182E8BF15ECCD32AE480B466,
	AdManagerInterstitialClient_add_OnAdDidDismissFullScreenContent_mAA7F18058A3A3900364B7BFF910B49AAE708E746,
	AdManagerInterstitialClient_remove_OnAdDidDismissFullScreenContent_mAE690C4DD49CC821046F67B90B2A923FFF5BFE0B,
	AdManagerInterstitialClient_add_OnAdDidRecordImpression_mF1A907AA5E7FED7F98778A11A85876988CFFC2BE,
	AdManagerInterstitialClient_remove_OnAdDidRecordImpression_m0582C502969653F216B5DD0634AC92E23AF1EB8A,
	AdManagerInterstitialClient_add_OnPaidEvent_m4485F524565F4BE34A0CC3D8B02C0E9AD51D65D5,
	AdManagerInterstitialClient_remove_OnPaidEvent_m19EC18DCF300DA120F1243CB4D690574BEB66701,
	AdManagerInterstitialClient_add_OnAppEvent_m80172796E371686146E464FF7F2B2C8F12D622A4,
	AdManagerInterstitialClient_remove_OnAppEvent_mE0810A5AD2955BAF13FA86059A959EB9E9A6D9F9,
	AdManagerInterstitialClient_add_OnAdClicked_m79C3631452D6793140EF26D1D7E3F0CBD563D241,
	AdManagerInterstitialClient_remove_OnAdClicked_mAEC6F34D263E8BD5A58DBC7C62267134AE090CDC,
	AdManagerInterstitialClient_CreateInterstitialAd_m58D8941C1EE741EE23151D7F9C0722653C4A52E3,
	AdManagerInterstitialClient_IsAdAvailable_m7E4AE23105D0AF37BB2D42B600C2F3D6C1BA527E,
	AdManagerInterstitialClient_PollAd_mD15A94062E8E45A12B57124230EF55E3A120C8E9,
	AdManagerInterstitialClient_PollAdManagerAd_mD98CB0768D5EBD87CFAAABE6EE3F2D1A299C0805,
	AdManagerInterstitialClient_LoadAd_mE2E4C20E9E87BA952B49B993306ABB78F136D60E,
	AdManagerInterstitialClient_Show_m83AA16389CA1AF20A194032BAA61414EA07805B7,
	AdManagerInterstitialClient_DestroyInterstitial_m665607C6220C4DB4279C58A4B5CB186FA0D8F9B5,
	AdManagerInterstitialClient_GetAdUnitID_m28839B725945F3497532BD86CA13CCD53D6F02EE,
	AdManagerInterstitialClient_GetResponseInfoClient_m40150B7F187AD0AA734512D3F825DB82DCCF380C,
	AdManagerInterstitialClient_onInterstitialAdLoaded_mBEF6F6A7C3E6470CD5DD5376739E3D3A75A065C5,
	AdManagerInterstitialClient_onInterstitialAdFailedToLoad_m10B6459575B8AAEEB4B44967BC3B36945BC8A221,
	AdManagerInterstitialClient_onAdFailedToShowFullScreenContent_mC4DC7A2E994227B992180C20271324DA83BE3AB1,
	AdManagerInterstitialClient_onAdShowedFullScreenContent_m693AD080EAD48A3C92300779BF1ED713ACA6E8CF,
	AdManagerInterstitialClient_onAdDismissedFullScreenContent_m9D19B85AA88CDEC032E4D4A222E62FBC9A989DB9,
	AdManagerInterstitialClient_onAdImpression_m7E00A10747C28F9BA83B39BFBA58DC17FB03ED3A,
	AdManagerInterstitialClient_onAdClicked_m9B71EA9EA80505A5493C7BA33E5613BA5A2C2684,
	AdManagerInterstitialClient_onPaidEvent_m4E10C84D5D377B6733FDE9B85B5A78579E8DB8FE,
	AdManagerInterstitialClient_onAppEvent_m6FF69EF9D28D2D4E0CB3933C4DAE6E9212FD2546,
	AdapterResponseInfoClient__ctor_m3AD39ACE8F60553902B78BA44EFE94563464DC85,
	AdapterResponseInfoClient_get_AdapterClassName_mD4F62ECEB70D18C43926CBE2142D0F454EE1FCF2,
	AdapterResponseInfoClient_get_AdSourceId_m38E79DB97CAB5F2F70AED5BFF120B94A55780246,
	AdapterResponseInfoClient_get_AdSourceName_m248689B367E7E1D2DA8741551AC5CD977A6E4E9A,
	AdapterResponseInfoClient_get_AdSourceInstanceId_m689AAC03D41C6C511EE48FA42EE4568350CA79B1,
	AdapterResponseInfoClient_get_AdSourceInstanceName_m78BC2A6F3545578088BA0B45397CEF5579D31D98,
	AdapterResponseInfoClient_get_LatencyMillis_mD11CDCBB8C964E90278A0C2A76BC93277833329F,
	AdapterResponseInfoClient_get_AdUnitMapping_mF0D6B6C34D4DA3E5443C678F85890FAC23F9AEE9,
	AdapterResponseInfoClient_get_AdError_mC03B26064B4AF8DABE01009D492661048D7BA0BE,
	AdapterResponseInfoClient_ToString_mE337C161E5DD3EA2C23068B19F8AB78EC44CB6EF,
	AppOpenAdClient__ctor_mEB376D4F185D1E9946EBE78F01389BC4569DFF65,
	AppOpenAdClient_add_OnAdLoaded_m0284EE5461CCBF9FBEFC75142CF46213C20F3B16,
	AppOpenAdClient_remove_OnAdLoaded_m6F483B82BA3FB708B04ECB380F367317368E1525,
	AppOpenAdClient_add_OnAdFailedToLoad_m37DEAA83ACA6B33D948B88EF072B4AD7CD814818,
	AppOpenAdClient_remove_OnAdFailedToLoad_m3234FC6979CCDB95A39AD7BE4F87099CA3F44A82,
	AppOpenAdClient_add_OnPaidEvent_mBE4A5B7FEF838B1FA2342331DFB0468C313D3EB2,
	AppOpenAdClient_remove_OnPaidEvent_m6957B1E86C7D9573C43D74BF3F34C6DDE746BB42,
	AppOpenAdClient_add_OnAdFailedToPresentFullScreenContent_mAD1D8D9FD175D06B396B0B7DF38386BD540FE18B,
	AppOpenAdClient_remove_OnAdFailedToPresentFullScreenContent_mF30C2A1A102AB220FCF4D3B08AFB13B57AA37C0D,
	AppOpenAdClient_add_OnAdDidPresentFullScreenContent_m7AD5F1DB70569DA07BF81892B1F68F6B6D363405,
	AppOpenAdClient_remove_OnAdDidPresentFullScreenContent_m1579719351CD4BADC8EFD8377CD74ECF8E2472CE,
	AppOpenAdClient_add_OnAdDidDismissFullScreenContent_m60D5BC495067B1B57379DE5B6E6E4F4BF119AFEA,
	AppOpenAdClient_remove_OnAdDidDismissFullScreenContent_mCE166B329D7E18C9247F96F09E4FD8553FB1DDDC,
	AppOpenAdClient_add_OnAdDidRecordImpression_mFBF4070CFC1C9BB028437FD118082BD8572A4A03,
	AppOpenAdClient_remove_OnAdDidRecordImpression_m738814410D3671514A09FD0A11C4D4ED5394B9E1,
	AppOpenAdClient_add_OnAdClicked_m286233DF6F67B44E6CA01C157036A26054042A2C,
	AppOpenAdClient_remove_OnAdClicked_mF345B1E2A8409022E61AF1AA3EC17094CB88E9E9,
	AppOpenAdClient_CreateAppOpenAd_m1261CA2D18446A2DFA216165DB4982D9B48C1450,
	AppOpenAdClient_LoadAd_m8A520FAEBB175D7F257AE7BDC6C684E3852C0074,
	AppOpenAdClient_Show_m1E86C944E26AD298277031AAF2FFAEA15D0F0B22,
	AppOpenAdClient_GetAdUnitID_mDD84F1327C085B17FA0243A3A43E00D46D25DB32,
	AppOpenAdClient_IsAdAvailable_mB74100C82C6972F64D254C5507307D4CE30A2C0D,
	AppOpenAdClient_PollAd_m2777EF8D3FC861691ED2209A3606BFF5B2D42DDA,
	AppOpenAdClient_GetResponseInfoClient_m714F64BA2CAFEBA769DB8249D12999A60DCF3704,
	AppOpenAdClient_DestroyAppOpenAd_m6F81444D6CC9285C90C18CF45305C106E831F2A7,
	AppOpenAdClient_onAppOpenAdLoaded_m71E71B17DE1A92B61B7FB4399CC39A9D279C60E9,
	AppOpenAdClient_onAppOpenAdFailedToLoad_m8FF125176A9D6AB24CCB9C1C0614E85DEBFA600D,
	AppOpenAdClient_onAdFailedToShowFullScreenContent_m2E8311D06935F505855850F51E94EEF4973796A6,
	AppOpenAdClient_onAdShowedFullScreenContent_m824EB057901C14E6C30C9BC6847C9028C3E58497,
	AppOpenAdClient_onAdDismissedFullScreenContent_m39D6400BFB219F9DE439CEDD1DB70A1D5DDFA3CC,
	AppOpenAdClient_onAdImpression_m8761B82F00493DBD3821096969516E4D70CED677,
	AppOpenAdClient_onAdClicked_m4CE850BB55D897B734D899401ABB4420DBE0830F,
	AppOpenAdClient_onPaidEvent_m82844CEF3194744E3CC7003465B4D04A63BB9218,
	AppStateEventClient__ctor_m4C240F785BCC2D2C26649A4F76B7D5D9132BAF30,
	AppStateEventClient_add_appStateChanged_m2820A831E5CEC722234335227B18692391C3A4D6,
	AppStateEventClient_remove_appStateChanged_m5BF3BD017CA00CD8DED3FE3ABC585943472EBA0B,
	AppStateEventClient_add_AppStateChanged_m1BA95FE1F1EA1779F9ADFE666A9AC4C7CC4F8E85,
	AppStateEventClient_remove_AppStateChanged_m049B62BC034B17D6657A6098559F2F9A58C941B5,
	AppStateEventClient_onAppStateChanged_mBB471FD53C4A64AAFBDAD0E4F5FB609A6FB1DDBD,
	ApplicationPreferencesClient__ctor_mCA619AB5DEF6C891E7B2333F292C8B3D95BAC210,
	ApplicationPreferencesClient_SetInt_m5253C3C818602B0AB0F73A6EC65C0AE87E49A379,
	ApplicationPreferencesClient_SetString_m541ABCE830B9F82BDA17A12906822BD3409F233E,
	ApplicationPreferencesClient_GetInt_m2E31450FB7A0C02D1DEA787F4FA559D13884E4A4,
	ApplicationPreferencesClient_GetString_mFF12EE10D66AD620FA6B206ADBC576F6EF1F76A6,
	BannerClient__ctor_mF8BE5F4C0BC785560D8F935A30F83076805F0B37,
	BannerClient__ctor_mF5F2EE9004D78F2DF591248B3FA97630BF55A917,
	BannerClient_add_OnAdLoaded_m6FE124A5DC5C30E7EF545B18DAEA53FEA06F1A13,
	BannerClient_remove_OnAdLoaded_m7D4BBF12EA8824C681CFB6CAAADC0FD75381816C,
	BannerClient_add_OnAdFailedToLoad_m4144FDD58A2D680EB4B5F76F6D622230E00C8F48,
	BannerClient_remove_OnAdFailedToLoad_mD883FF94D8FC8E946D7D6FDE16DAE0FAB3D20C80,
	BannerClient_add_OnAdOpening_mAA500C8B9B9627F8647ACDBBA57D306E3BDD99E0,
	BannerClient_remove_OnAdOpening_m10C5A2C9DA9B55D5F22C92B470D7C9966F571925,
	BannerClient_add_OnAdClosed_m7D4598C4D8F07EB44BFBCD2B46317730C1D2C762,
	BannerClient_remove_OnAdClosed_m6C3BE9E5A82B2EBC1A785AF9ED632F22F5EEDAD3,
	BannerClient_add_OnPaidEvent_m677DCC8319399F9FEEC57CEC69A90A85E40B36CC,
	BannerClient_remove_OnPaidEvent_mD3CD1D4E2B41DFAD942075EC3A02D0A236B996B7,
	BannerClient_add_OnAdClicked_m8A2BFFF9C066D6281E71D3DA1F6A355272CF238C,
	BannerClient_remove_OnAdClicked_m3307B46C723DAFD9B2005CCA8F2552BFCA5E2CF0,
	BannerClient_add_OnAdImpressionRecorded_m2E41A8A18EC99BACEACA1A7E20273AB29B1FD525,
	BannerClient_remove_OnAdImpressionRecorded_mE489F864528F273F12AE731D3C277646913FD24A,
	BannerClient_CreateBannerView_m6C38246E4FDDE07A257BEC5520636D0A11202B9C,
	BannerClient_CreateBannerView_m545E5F3D9502F7FA9E3314DBFF11481369773517,
	BannerClient_LoadAd_mA0475B8FF6AD8A08E379E3182F0163B437456738,
	BannerClient_ShowBannerView_m41FCFE959258D59BA51CC922D6D5F19236C7FB0F,
	BannerClient_HideBannerView_m0F945DACAB243264235FA4FD4162365FCCF0A906,
	BannerClient_DestroyBannerView_m0B15993F75A7B442C0DF1B7528F7DF4687F019CA,
	BannerClient_GetAdUnitID_mE1652386E12A84BC6A2A86A83A9B21C56BF1EE6B,
	BannerClient_GetHeightInPixels_m2DC15ADEBE58FD53C08186F5F8F3B04545206F66,
	BannerClient_GetWidthInPixels_m0FC495326EA8F38CC0D410F355F859D1220A9E8B,
	BannerClient_SetPosition_m2301CA7EA87C4E1C10948EDB46E83F049A44F68F,
	BannerClient_SetPosition_m923265E13104BC122B7774BED86369DCA389EB2A,
	BannerClient_IsCollapsible_m6E9B2F8E218ACA27E7B4197DA4A8DA3C17178137,
	BannerClient_GetResponseInfoClient_m6DFB0A855207F903D56C8A1BA430E759091E2A66,
	BannerClient_onAdLoaded_m5FFB00BDBA55AD1A9C018200157B46B34DC34558,
	BannerClient_onAdFailedToLoad_mF0B7DFE4403FF3405FAC63F8A59D7C43A405AA6F,
	BannerClient_onAdOpened_mDC8C60A191781B683699C254120EE50A0048CB87,
	BannerClient_onAdClosed_m0D8AAA6C2F2EADB3B91AF9494D1893E6439BD7D2,
	BannerClient_onPaidEvent_mAF7BE811BE76C62824D043DE640475A5A206728B,
	BannerClient_onAdClicked_mF3A7326E8C2C2F2EE431729E57648A956AEAB6C7,
	BannerClient_onAdImpression_m4739D123B68191C2A4C193F1E7B807E5277683D3,
	DisplayMetrics__ctor_m8F895C80EBCA4AED8758E3DEBD2CF65248BFC2B4,
	DisplayMetrics_get_Density_mD688C6F9AAA4C676BFAA2B772C5E622A808236BD,
	DisplayMetrics_set_Density_m3D0B71F3B81BB60A34574A5325F28A3274EC2B37,
	DisplayMetrics_get_HeightPixels_m7F22FEA3AA8FB8D0FBD3262A350E2221315DA5A4,
	DisplayMetrics_set_HeightPixels_mA2C374FB8C1D825A6275A0359357E27723F35819,
	DisplayMetrics_get_WidthPixels_mEF6E3679EDF3407EC588DB679F81B12D3EE8C914,
	DisplayMetrics_set_WidthPixels_mCE3C42D134493FD7CBAF587BC906CF6BB8AB5277,
	GoogleMobileAdsClientFactory__ctor_mEB046D88F7DF05C39310C9ED148CB93684C0C146,
	GoogleMobileAdsClientFactory_BuildAppStateEventClient_m745422CFE30BFE5D03212133E63261F75764BB47,
	GoogleMobileAdsClientFactory_BuildAppOpenAdClient_m04C7325C9CD581B21989D9A01208981D1F6EE3F3,
	GoogleMobileAdsClientFactory_BuildBannerClient_m1DA1036CC88B2C930AEF747959F8F4F4F42C5E4F,
	GoogleMobileAdsClientFactory_BuildAdManagerBannerClient_m483F43A3B5F0A4DFA6A06DFE964BE1A5299C6281,
	GoogleMobileAdsClientFactory_BuildInterstitialClient_mBA16B22AAB2CA58F48437FAADA0DCEB4C1FAA1CE,
	GoogleMobileAdsClientFactory_BuildAdManagerInterstitialClient_m8709773D0E17E49F8AAF23E8C599A383B84ACF35,
	GoogleMobileAdsClientFactory_BuildRewardedAdClient_mE947E4BBB661F4F464F0995B815C987FD26B177D,
	GoogleMobileAdsClientFactory_BuildRewardedInterstitialAdClient_m7AA712489261202B58FADB8EE40909487B79FB62,
	GoogleMobileAdsClientFactory_BuildNativeOverlayAdClient_m7B77A962907AA9310B423D4B91EA04458B77E0A7,
	GoogleMobileAdsClientFactory_ApplicationPreferencesInstance_m488ECD4316C948DA4F7A67FAC4F815FF2580F1AC,
	GoogleMobileAdsClientFactory_MobileAdsInstance_mF8E999E2A7C2AAA9ACB0D18F478F3368A2C80270,
	InitializationStatusClient__ctor_m0A5A2C3A71BE10A20943AF9DEDAFD81AEF517C49,
	InitializationStatusClient_getAdapterStatusForClassName_mC2F67D3EF84E3587425BB50CD0CB643564F79214,
	InitializationStatusClient_getAdapterStatusMap_m2E3F2B6AC0171BD072905B2697018CD244D19583,
	InitializationStatusClient_getKeys_m5CD44BACBC53E3D9559A6B38C54636AACCA92C3A,
	InterstitialClient__ctor_m0046CDB719B03D590B6E356A884B10D1EAF173E5,
	InterstitialClient_add_OnAdLoaded_m07063D92BFEC39E271454020479CC90B9B4F9B3A,
	InterstitialClient_remove_OnAdLoaded_m2D3180CC3E6D23840F294D866D09E1D5DEA6E88D,
	InterstitialClient_add_OnAdFailedToLoad_m2A7C1CD94B215716100E1E6AAFD284D4B5D77D47,
	InterstitialClient_remove_OnAdFailedToLoad_mA0E4C72E83B953A0D6AD99B7FC552B22D4519644,
	InterstitialClient_add_OnAdFailedToPresentFullScreenContent_mEE4DEC7B7671BFF1DB70E18FEF2F551DFFCB1EA7,
	InterstitialClient_remove_OnAdFailedToPresentFullScreenContent_m0EC96B3CF42CAF270A2A6CDAC233943539BC2575,
	InterstitialClient_add_OnAdDidPresentFullScreenContent_mC6902B34E2C45CE47C5FAE05117BEB11D7D91465,
	InterstitialClient_remove_OnAdDidPresentFullScreenContent_mF18E16D8D780CEAFFDB9C0376A32BA85103EE964,
	InterstitialClient_add_OnAdDidDismissFullScreenContent_m6CDA62483ED94196361C62DAB7FF0B2D10A1C369,
	InterstitialClient_remove_OnAdDidDismissFullScreenContent_m5F998C352AD81569324F2869F870341767E925EC,
	InterstitialClient_add_OnAdDidRecordImpression_m8764F9C580F3B8341A346A2AF3FC8166599C7434,
	InterstitialClient_remove_OnAdDidRecordImpression_m0C444B50E64989149B59DB9BEF6077411A5CB2F8,
	InterstitialClient_add_OnPaidEvent_m37BD2E68F7BA38FE65947B40D80F4414D2AE49D3,
	InterstitialClient_remove_OnPaidEvent_mB4F5076C1EAA9B1C4AD3BD5C8A639D6DD4CBDE1D,
	InterstitialClient_add_OnAdClicked_m5C8BAA0D0B8F29608ADA456703F9E9B3D4A584B8,
	InterstitialClient_remove_OnAdClicked_m529A9963C15FFDF0C89816C5C796A4AE75C65931,
	InterstitialClient_CreateInterstitialAd_m2D34F34A4EE6AC30888D5E42FE7F387752A066C1,
	InterstitialClient_LoadAd_m797AED7355744DEB02084DEEA67040F2E149DC42,
	InterstitialClient_Show_m38BEFDBA16B5A9DCE8EC0BCE0AF9960D954E610D,
	InterstitialClient_DestroyInterstitial_m99769CECB16B905F729BCC38C7D9EF39D9820F8E,
	InterstitialClient_GetAdUnitID_mB4F5FCAFB00A6450F60310432CFEC0A05D26E8F1,
	InterstitialClient_IsAdAvailable_m2877494B014114CA96B02BCBC47BB4E9B1DC7FE5,
	InterstitialClient_PollAd_m46749DFE6FE30F94A69D8158C87AC62A741E019F,
	InterstitialClient_GetResponseInfoClient_mF87067249A30656FB2D85D088534866F008E5C34,
	InterstitialClient_onInterstitialAdLoaded_mF497C69BEE1FEE0F0FD5E0425B69F8F3085EF57E,
	InterstitialClient_onInterstitialAdFailedToLoad_mAED345A3CC012E3770BA5F3A6246669019F1497C,
	InterstitialClient_onAdFailedToShowFullScreenContent_m9300E7914F254E3B985F89A88291710527B6DF90,
	InterstitialClient_onAdShowedFullScreenContent_m713BEBACEEACF05906AC1D6A726C62A0F0CBE7CB,
	InterstitialClient_onAdDismissedFullScreenContent_m639D15141E9D8555FFFCFE0ACBF53337BE053640,
	InterstitialClient_onAdImpression_m917C895516B08862159F2BAE1B063E0FCC9AE747,
	InterstitialClient_onAdClicked_m580B81D56B1DF03C99755F0E8CBA3EFE0FDBEFBE,
	InterstitialClient_onPaidEvent_m7D99A23A9739A07FDC4775DD8A5316FFC331271D,
	LoadAdErrorClient__ctor_mAAB2CB3CA79F7FC1CDDA014A8194DDB6A26F9967,
	LoadAdErrorClient_GetCode_m1E3E3BAE9933C1C80B2F302BBE61D3EA4DA9638C,
	LoadAdErrorClient_GetDomain_m35585024A100B399625FC5AE66A6D1BD7D1B4255,
	LoadAdErrorClient_GetMessage_mCC4D7B22A5DAA57D9E4C4FBAABABACBD3DD1F220,
	LoadAdErrorClient_GetCause_m9B8B220890D751A94FDB0A8869ACD333B8A9842E,
	LoadAdErrorClient_GetResponseInfoClient_mCA1E3314C9FC6F5D90B6332CA03ACDC21D4759BF,
	LoadAdErrorClient_ToString_mC2DB12F6A131B98CD61C244E9A54053BAE54D792,
	MobileAdsClient__ctor_mDBED7FEC13313685E587BAB4009F3786A9EBDA8F,
	MobileAdsClient_get_Instance_m0C849C8A0818CB529BEEB5B77E94FBD1DA87C4D8,
	MobileAdsClient_Initialize_m2921FC0DE0E8930BFD279EEA0831733FF2457C40,
	MobileAdsClient_SetApplicationVolume_mED369113DB00AB208B4E47A72C5ACB3DE2317899,
	MobileAdsClient_DisableMediationInitialization_m266F611C99F1766B0028A14AEF5CE710C60381B4,
	MobileAdsClient_SetApplicationMuted_m80E4D9E3DCE21608F632EB5FB2FCB857E2AE7055,
	MobileAdsClient_SetRequestConfiguration_mDE0A431E5D075CF5FAF8A45864ECEA9B61757897,
	MobileAdsClient_GetRequestConfiguration_m2F4C593D01ABC588EDF10CB91DF2533A885A9B0B,
	MobileAdsClient_SetiOSAppPauseOnBackground_m91239F1DDD61CE3F8441C8434B77EC6A8F4080A0,
	MobileAdsClient_DisableSDKCrashReporting_m498A10B14F5CDDC20184540E8911454F59B58D3B,
	MobileAdsClient_OpenAdInspector_m6F19B5F80831FABE486EAAFC738F4FF49FA51ED4,
	MobileAdsClient_Preload_mE2E6BEEA61BC80B5DC43F1AB59344E90024B3199,
	MobileAdsClient_GetDeviceScale_m9AA91F81BC16A2FA45370718FCBA5C649CC765DF,
	MobileAdsClient_GetDeviceSafeWidth_mC892F93688B64CA9C2C4F380F0D335556CF77889,
	MobileAdsClient_GetSDKVersion_mE5CEDA1CC85442C46B734A50F87A28659EF27172,
	MobileAdsClient_onInitializationComplete_mF4F780F1B71E469AC5CB452695BA1DFD97E59938,
	MobileAdsClient__cctor_mE401FAC02059DAD69A194FEE3BFF8613C7914F85,
	NativeOverlayAdClient__ctor_m35ABD2F46C9077900F9787528146C838A1FE2566,
	NativeOverlayAdClient_add_OnAdLoaded_mC3BB8057D3779E8F61432FE7A592ACD518691FE4,
	NativeOverlayAdClient_remove_OnAdLoaded_m619DFB123FBCB27B2E2C32D31A31D723A8B772BF,
	NativeOverlayAdClient_add_OnAdFailedToLoad_mB5CBB814BAB55788F71551410CD795C7AED1376D,
	NativeOverlayAdClient_remove_OnAdFailedToLoad_mFDD3F2C27EA188957EF0C07AA12F068EE611F108,
	NativeOverlayAdClient_add_OnAdDidRecordImpression_m816C8863E071AF34C97DB5500C3F8BBAB0EF9B93,
	NativeOverlayAdClient_remove_OnAdDidRecordImpression_mA604C1D3E524D1EA5DBDD7230F7F35BF6BEFD217,
	NativeOverlayAdClient_add_OnAdDidPresentFullScreenContent_m427D70740EE8BEC9B130C1A4786E63147D614CEB,
	NativeOverlayAdClient_remove_OnAdDidPresentFullScreenContent_mDFA8B843AF6AABBF76B6FE7E0878695C3066BFD2,
	NativeOverlayAdClient_add_OnAdDidDismissFullScreenContent_mF4498DC2098194765761D15394B2CA5FF9495AC2,
	NativeOverlayAdClient_remove_OnAdDidDismissFullScreenContent_m173CA4FE6BB5866BFD204C65E46F83D8F384B401,
	NativeOverlayAdClient_add_OnAdClicked_m18D5583F8859D15C77F4851F571AC22D2CF60B33,
	NativeOverlayAdClient_remove_OnAdClicked_mA149D110C4F687DCAFF90DEE99AD01F222B6ED16,
	NativeOverlayAdClient_add_OnPaidEvent_mA90B00527EDF35DEB130DAF56D9E4D7B5DA18865,
	NativeOverlayAdClient_remove_OnPaidEvent_mBDF33F011BEFEA34385F42998BC1614F495BB61A,
	NativeOverlayAdClient_Load_mCDAA08EFF3B520F4C155F69339C076DF031DA26C,
	NativeOverlayAdClient_Hide_m75634D9A90F4D5A5B7487904B57176562CC3F54D,
	NativeOverlayAdClient_Show_m1A62C8AE06F3D1B4A7D8E787548D7B33609A3E9A,
	NativeOverlayAdClient_SetPosition_mDF380BB94C0D81757B638DDA76F5A31916B5533C,
	NativeOverlayAdClient_SetPosition_m9F040367F83809C60D9824E634C5B99DAE111901,
	NativeOverlayAdClient_Render_m736BD49FD9602F20FFF07212A2215B7C06C9F31D,
	NativeOverlayAdClient_Render_m4AC7583411A7347EF635ACA57E5003B07A4C2301,
	NativeOverlayAdClient_Render_mE7E3A95183DE96EB1A8D84D4B6B42C6540D77559,
	NativeOverlayAdClient_Render_m1F3FC83F77C11B6416315B0CFB488CEE97696EBC,
	NativeOverlayAdClient_DestroyAd_m53A8C433B9D06CC3A50969D9AFF65C2C3CCF048F,
	NativeOverlayAdClient_GetResponseInfoClient_m4F3960A8587C8CF06D74A89E8CFC9DDB4BB39BAA,
	NativeOverlayAdClient_GetHeightInPixels_mADE067F4F90EC50D322EA298FAB42AE08EE416A6,
	NativeOverlayAdClient_GetWidthInPixels_mBC5626792CBF6751AF90420F1CB614A96672D2F8,
	NativeOverlayAdClient_onNativeAdLoaded_m3F52EC1B6F74C80C9170CFC142778C216912D3D5,
	NativeOverlayAdClient_onNativeAdFailedToLoad_m840F8F49D9C8BE7044D525649013ADDF903D81A9,
	NativeOverlayAdClient_onAdImpression_mC6D184AADE70394C36B65902F831E0BE5EDB0602,
	NativeOverlayAdClient_onAdClicked_m9D68731BE703BB135C97A7188319D37E4EFA8BCB,
	NativeOverlayAdClient_onAdShowedFullScreenContent_mCF1C44F09FB4968DADD2BD8A6FECCB8E7ECF3FBE,
	NativeOverlayAdClient_onAdDismissedFullScreenContent_m8DA765B0B51913FFAB3FCE9BD117758A326FD440,
	NativeOverlayAdClient_onPaidEvent_m358BCBFC916F409348FB5586060B51AC1130FF41,
	NativeOverlayAdClient_GetNativeTemplateStyleJavaObject_m82F11397AACB0D8C004F2F8EB918EAEB6C3611BE,
	NativeOverlayAdClient_GetNativeTemplateTextStyleJavaObject_m61D41CB228A5070CB308845EC996DB2773571D49,
	PreloadListener__ctor_m3E934E1D71A2A1A872D1AC20DF5AC47C31999258,
	PreloadListener_onAdsAvailable_mCE6335B2B7724956CD3B04F7EAE7037B6D3B9C92,
	PreloadListener_onAdsExhausted_mBBC008EBF3F54AEC0AB6EC1857D165CE1E58125A,
	RequestConfigurationClient__ctor_m4F8B07D96798B93FBAB17E6015A4DDEA86332F71,
	RequestConfigurationClient_BuildRequestConfiguration_m8F0EB5E489CADE0C8A0A6DE37723A1D5E12D87D7,
	RequestConfigurationClient_GetRequestConfiguration_mF82939C37FF82486FBCAC91CECDF0AB8808C4A6B,
	ResponseInfoClient__ctor_mB3010C36F05A40A73A03FC081FBBD7A15C528EDC,
	ResponseInfoClient_GetAdapterResponses_mED5C3350D09693349549CAB292D4AAAA48025335,
	ResponseInfoClient_GetLoadedAdapterResponseInfo_mF951381D9B3302BD82FC914835C6FA84A6C870E5,
	ResponseInfoClient_GetMediationAdapterClassName_m841D4E968FB1AAFD9B0973287701C61036B3FC1D,
	ResponseInfoClient_GetResponseExtras_m408FC75D4FC44779CB2494552DC28CD0C7B0D0B3,
	ResponseInfoClient_GetResponseId_mB16EC1E99B79FBE6200426B8511E5CCE95B47981,
	ResponseInfoClient_ToString_m024342CDAF182849718895EF4A4EC24E59D2B1F9,
	RewardedAdClient__ctor_m2086861247473AE89A2ED13636930BE90A015194,
	RewardedAdClient_add_OnAdLoaded_m76A83EA046A32E9169B9B36479170754704F0495,
	RewardedAdClient_remove_OnAdLoaded_m9D9890947879ACD3D959B609F4072F4972DECA48,
	RewardedAdClient_add_OnAdFailedToLoad_m4C8B615252DDDC96553C02B9CF5A524DD17B4302,
	RewardedAdClient_remove_OnAdFailedToLoad_mCDC431B3B322CE896AABA9B3EE7B3B861AB949D6,
	RewardedAdClient_add_OnUserEarnedReward_m316CAE6E39A916EFD57A99C72140A9175C44FD26,
	RewardedAdClient_remove_OnUserEarnedReward_m29F7B4FC80286ADC6635449EE342136C52D1DE50,
	RewardedAdClient_add_OnPaidEvent_m5E5BC0A875AA7A686D295BF106C43EA50C0FEC86,
	RewardedAdClient_remove_OnPaidEvent_mF5687F8689BA6CF373BBF8F478A5F8194E4B8E6D,
	RewardedAdClient_add_OnAdFailedToPresentFullScreenContent_mFFFEA4D36186A650F0A0B3CFC4A38DA54174CBCA,
	RewardedAdClient_remove_OnAdFailedToPresentFullScreenContent_m4EF48F65582F95036DD28A146FB2CB29B9CC3E9E,
	RewardedAdClient_add_OnAdDidPresentFullScreenContent_mE0FEC27A7ED1D2F0A8E2B3DD5CD84E59CC3EA989,
	RewardedAdClient_remove_OnAdDidPresentFullScreenContent_m9B06F5C51458C65EE435A61A5B74964FB72D2F3A,
	RewardedAdClient_add_OnAdDidDismissFullScreenContent_mFC264149FD303B89D9C415E25388E286158D95F1,
	RewardedAdClient_remove_OnAdDidDismissFullScreenContent_mD9A14E399471D6A39D4B523D2E440CF345950F2C,
	RewardedAdClient_add_OnAdDidRecordImpression_mBC469B8B2290A444847C444CF7724E7A83BF749A,
	RewardedAdClient_remove_OnAdDidRecordImpression_m58353D0EB123644A534F52B095EF86E63F434272,
	RewardedAdClient_add_OnAdClicked_m6E08AE90876C3ECC9903B6272ED8CA3E740A4783,
	RewardedAdClient_remove_OnAdClicked_mD16DD234D085AF2753C60255FDD93550EC29274C,
	RewardedAdClient_CreateRewardedAd_m251A1B20C3726BFED8A9397DC1960FBB39E30FA4,
	RewardedAdClient_LoadAd_m5AE53471AF85CA169C5BFAC9A15073BD717D5172,
	RewardedAdClient_Show_m73423F0E14613315864F0C933C6244FBBA3CCA21,
	RewardedAdClient_SetServerSideVerificationOptions_m5169D5A142F07ADF313CE231282EF5A9BFD10971,
	RewardedAdClient_GetRewardItem_mA0729ED1397412164EBC06B68C179B2A0F3ED2B4,
	RewardedAdClient_GetAdUnitID_mD7079C7796D00572B48361FF5B0776112B07DF21,
	RewardedAdClient_IsAdAvailable_mD1D45163B6DE3748F13BCEE7816DF55F8BD2F768,
	RewardedAdClient_PollAd_mEA518ECF81E82940A35B63D7ECDC41A76AADE5DF,
	RewardedAdClient_GetResponseInfoClient_m656FAB7CAE38A9C2C3ABBAC0C81B345983FE5D77,
	RewardedAdClient_DestroyRewardedAd_m86BCC2B6506D90BD702B6FCD4039B999D4559D9F,
	RewardedAdClient_onRewardedAdLoaded_m47B4E8ABC89437758084BB04D581542F5B647A1C,
	RewardedAdClient_onRewardedAdFailedToLoad_m3789A9FF6433620D8418CEC0EF8FE169CC491640,
	RewardedAdClient_onAdFailedToShowFullScreenContent_mAC11D570CB1C6E39D3074EF4D35E90CFF5D13E22,
	RewardedAdClient_onAdShowedFullScreenContent_mD010B5B214BFF75B94C96AC3E4CACD36F41FD931,
	RewardedAdClient_onAdDismissedFullScreenContent_mDDAFDD797E0F08646BF92024F729622BA03A224F,
	RewardedAdClient_onAdImpression_mF9E29BE592138FCA88C4ED6749D0A5911FE9ECD4,
	RewardedAdClient_onAdClicked_m23B3C998A6FAAECDC0BB49F1C2BAD077E3E7E541,
	RewardedAdClient_onUserEarnedReward_m2A49D078B6795CAFD1D690BFD3B6C2D9EA1CD155,
	RewardedAdClient_onPaidEvent_m9808D3DA5A710D7D8E3E84F36E1B79520D954E67,
	RewardedInterstitialAdClient__ctor_m7DCD4A3A92E137ADC479BF330963BD4676375E11,
	RewardedInterstitialAdClient_add_OnAdLoaded_m25D0E3DCDAE16821388DB623D0BB4F390B16CB1D,
	RewardedInterstitialAdClient_remove_OnAdLoaded_mA6B686A99FE5C57789517D13DD0EEF34CC657F3E,
	RewardedInterstitialAdClient_add_OnAdFailedToLoad_m1507674796F89D1394CAAA4283E3A7953CB71071,
	RewardedInterstitialAdClient_remove_OnAdFailedToLoad_m29D801FFA53BE78F7684CB75F69E911919B52A85,
	RewardedInterstitialAdClient_add_OnUserEarnedReward_mDD3C9F7E39B8B675284CE62040DDFFA81906E6CC,
	RewardedInterstitialAdClient_remove_OnUserEarnedReward_m6CBD114149C539E02F7D1E446C534AE4C176A072,
	RewardedInterstitialAdClient_add_OnPaidEvent_m5478036DB8E6F4DC212C24B371FABCD1BF7F2325,
	RewardedInterstitialAdClient_remove_OnPaidEvent_m187C0902BF3F923DB4FC0AE30445D1EA44137B97,
	RewardedInterstitialAdClient_add_OnAdFailedToPresentFullScreenContent_mCC615B2C3E502F90474A4382E5733B93C0374D1B,
	RewardedInterstitialAdClient_remove_OnAdFailedToPresentFullScreenContent_m06671EFBF8F2279F8BAC134C7696A9FF592F7E20,
	RewardedInterstitialAdClient_add_OnAdDidPresentFullScreenContent_m319D27B3E285874805D80E5DB185A4C25A86EF06,
	RewardedInterstitialAdClient_remove_OnAdDidPresentFullScreenContent_mD78534356A3DD83D37BF6ADE855DFCDF7FBEF443,
	RewardedInterstitialAdClient_add_OnAdDidDismissFullScreenContent_mA9E66A0515F34D7E17875BE866215DC953C9E77B,
	RewardedInterstitialAdClient_remove_OnAdDidDismissFullScreenContent_m6319B9040A03378437283C9F59FB54623AE2DBB4,
	RewardedInterstitialAdClient_add_OnAdDidRecordImpression_mB5A227A3AD02FEF148C2E39B7C141BDE897BE9EB,
	RewardedInterstitialAdClient_remove_OnAdDidRecordImpression_m5AAD9D74B407DED10906F9EF5C736768A33C838F,
	RewardedInterstitialAdClient_add_OnAdClicked_mDD0AF7B806B0558A61CA4B9663439609A8789F7E,
	RewardedInterstitialAdClient_remove_OnAdClicked_m453176735897016B916E8DC4B47A70E3D6020C09,
	RewardedInterstitialAdClient_CreateRewardedInterstitialAd_m721A8F8D1915ECA4A0759F6AD278E1B6EFE90FD6,
	RewardedInterstitialAdClient_LoadAd_m2950E1CB22284C315B13263EDBEBE8311DB027E0,
	RewardedInterstitialAdClient_Show_m348AEC7D9D65FC6DC7C160615008AAD3EFA934FA,
	RewardedInterstitialAdClient_GetRewardItem_mFFE7C78154E911845BEF729A31D55CC29ED0B05D,
	RewardedInterstitialAdClient_SetServerSideVerificationOptions_m9E0A837733D7DC9D301401D8CFCA08E57054DFF6,
	RewardedInterstitialAdClient_GetAdUnitID_m397CE8942E1A57BAC78ED8BCF92E29FA7C0F22F0,
	RewardedInterstitialAdClient_GetResponseInfoClient_m423E814E1FA46FAA43F23672C11FEFA2A59616B2,
	RewardedInterstitialAdClient_DestroyRewardedInterstitialAd_m087C8FA09FD7BCD2946C738FE67B49FBB4A7244E,
	RewardedInterstitialAdClient_onRewardedInterstitialAdLoaded_mA1B474386B635F71B9B745355FC8A1431ED62A15,
	RewardedInterstitialAdClient_onRewardedInterstitialAdFailedToLoad_mA02DF39CB4F953517E20464FADDD61E6ADA8517B,
	RewardedInterstitialAdClient_onAdFailedToShowFullScreenContent_m853771E331C548A39C18F8421C2EF694A9039B7E,
	RewardedInterstitialAdClient_onAdShowedFullScreenContent_mF70285F43F6E05885DB3A7BC4CF9337FF65A4BBF,
	RewardedInterstitialAdClient_onAdDismissedFullScreenContent_m51F1F86A8E4F74F94D596E480B2086DC41DBC682,
	RewardedInterstitialAdClient_onAdImpression_m2BEDE09D31150879FF91B30C300CD57B88B431A1,
	RewardedInterstitialAdClient_onAdClicked_m1BA2E359C6C6A21DCCB8A50A8D021FB5A7E8166A,
	RewardedInterstitialAdClient_onUserEarnedReward_m35797F6B822515A1A775B0FB9B2C868C82EECF5F,
	RewardedInterstitialAdClient_onPaidEvent_mE92D596D43C191C3CDC49C4221433D48C6B4C51F,
	Utils__ctor_m4005C63CB1C37042333363AA9055D2E96CC9E877,
	Utils_GetAdSizeJavaObject_m4043EE77245135A288B27C4ABEF9D40C190E249E,
	Utils_GetDictionary_mF53B34A1FA55C966E576F0E597CE8C74D9169664,
	Utils_GetScreenWidth_m3E783A25A917A05D2E3EE0771A258011D7351ACA,
	Utils_GetAdRequestJavaObject_m1B081FD57A546B7181D07C58C1101E57A227EF7E,
	Utils_GetAdManagerAdRequestJavaObject_m30E35471BE07BC674168CEA59A83A943B16FEA76,
	Utils_GetPreloadConfigurationJavaObject_m53ECBFCBDD9F8F62CFABEEBB1B75E9F2D55DB2FB,
	Utils_GetPreloadConfiguration_m569C2C24AAB65E458D1AF8FC0EE4469E9120CA1A,
	Utils_GetJavaListObject_mD6C5663A9E13A398147EFCD17EC9283239B257B8,
	Utils_GetCsTypeList_m14B7F25395CB6945B5F1DD35C713B627AE02DAB8,
	Utils_GetServerSideVerificationOptionsJavaObject_m167BC5038C51E5F410DAC9BB86617E8A4F4389C7,
	Utils_GetNativeAdOptionsJavaObject_m827B5A98D97FE8636879E37CE451EC3EA5E903A4,
	Utils_GetVideoOptionsJavaObject_m151ADF8276914528289A75E1F757859FD76E55EA,
	Utils_GetCurrentActivityAndroidJavaObject_m2A63222E44E8FB1766E8EA4AE6F8A624C441A3BD,
};
static const int32_t s_InvokerIndices[361] = 
{
	5040,
	6178,
	6204,
	6204,
	6204,
	6204,
	5040,
	5040,
	5040,
	6329,
	5040,
	5040,
	6204,
	5040,
	5040,
	2883,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	6329,
	3597,
	4479,
	4479,
	2883,
	6329,
	6329,
	6204,
	6204,
	6329,
	5040,
	5040,
	6329,
	6329,
	6329,
	6329,
	1451,
	2883,
	5040,
	6204,
	6204,
	6204,
	6204,
	6204,
	6179,
	6204,
	6204,
	6204,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	6329,
	2883,
	6329,
	6204,
	3597,
	4479,
	6204,
	6329,
	6329,
	5040,
	5040,
	6329,
	6329,
	6329,
	6329,
	1451,
	6329,
	5040,
	5040,
	5040,
	5040,
	4945,
	6329,
	2877,
	2883,
	4201,
	4479,
	5040,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	1509,
	1021,
	5040,
	6329,
	6329,
	6329,
	6204,
	6259,
	6259,
	5016,
	2604,
	6108,
	6204,
	6329,
	5040,
	6329,
	6329,
	1451,
	6329,
	6329,
	6329,
	6259,
	5087,
	6178,
	5016,
	6178,
	5016,
	6329,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	5040,
	4479,
	6204,
	6204,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	6329,
	2883,
	6329,
	6329,
	6204,
	3597,
	4479,
	6204,
	6329,
	5040,
	5040,
	6329,
	6329,
	6329,
	6329,
	1451,
	5040,
	6178,
	6204,
	6204,
	6204,
	6204,
	6204,
	6329,
	9523,
	5040,
	5087,
	6329,
	4945,
	5040,
	6204,
	4945,
	6329,
	5040,
	1512,
	6259,
	6178,
	6204,
	5040,
	9569,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	1512,
	6329,
	6329,
	5016,
	2604,
	1509,
	1021,
	2877,
	1490,
	6329,
	6204,
	6259,
	6259,
	6329,
	5040,
	6329,
	6329,
	6329,
	6329,
	1451,
	4479,
	4479,
	2883,
	5040,
	5040,
	6329,
	9160,
	9160,
	2626,
	6204,
	6204,
	6204,
	6204,
	6204,
	6204,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	6329,
	2883,
	6329,
	5040,
	6204,
	6204,
	3597,
	4479,
	6204,
	6329,
	6329,
	5040,
	5040,
	6329,
	6329,
	6329,
	6329,
	2889,
	1451,
	6329,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	6329,
	2883,
	6329,
	6204,
	5040,
	6204,
	6204,
	6329,
	6329,
	5040,
	5040,
	6329,
	6329,
	6329,
	6329,
	2889,
	1451,
	6329,
	9160,
	9160,
	9515,
	8534,
	8534,
	9160,
	9160,
	9160,
	9160,
	9160,
	9160,
	9160,
	9523,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Android_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Android_CodeGenModule = 
{
	"GoogleMobileAds.Android.dll",
	361,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
