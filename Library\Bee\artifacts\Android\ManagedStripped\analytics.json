{"DataTable": {"attribute_marked_count_always_link_assembly": 0, "attribute_swept_count_always_link_assembly": 1, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 54, "attribute_total_count_preserve": 33, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 0, "attribute_swept_count_required_member": 8, "attribute_total_count_required_member": 8, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 0, "attribute_total_count_require_attribute_usages": 0, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 104, "assembly_counts_link": 46, "assembly_counts_copy": 9, "assembly_counts_delete": 49, "assembly_counts_total_out": 55, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 291, "unrecognized_reflection_access_core_count": 160, "unrecognized_reflection_access_unity_count": 109, "unrecognized_reflection_access_user_count": 22, "recognized_reflection_access_total_count": 39, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 4, "recognized_reflection_access_user_count": 6, "link_xml_total_count": 12, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 11, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 63, "engine_module_deleted": 36, "engine_module_total_out": 27, "option_rule_set": "Conservative", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": true, "option_unity_root_strategy": "UltraConservative", "option_enable_ildump": false}}