<linker>
	<assembly fullname="Assembly-CSharp" ignoreIfMissing="1">
		<type fullname="FarmingModmanager">
			<method name="NextLevel"/>
		</type>
		<type fullname="RCC_CustomizerExample">
			<method name="ChangeWheelsBySlider"/>
			<method name="SetABS"/>
			<method name="SetClutchThresholdBySlider"/>
			<method name="SetCounterSteeringByToggle"/>
			<method name="SetESP"/>
			<method name="SetExhaustFlameByToggle"/>
			<method name="SetFrontCambersBySlider"/>
			<method name="SetFrontSuspensionDistancesBySlider"/>
			<method name="SetFrontSuspensionsSpringDamperBySlider"/>
			<method name="SetFrontSuspensionsSpringForceBySlider"/>
			<method name="SetGearShiftingThresholdBySlider"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetNOSByToggle"/>
			<method name="SetRearCambersBySlider"/>
			<method name="SetRearSuspensionDistancesBySlider"/>
			<method name="SetRearSuspensionsSpringDamperBySlider"/>
			<method name="SetRearSuspensionsSpringForceBySlider"/>
			<method name="SetRevLimiterByToggle"/>
			<method name="SetSH"/>
			<method name="SetSHStrength"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetTCS"/>
			<method name="SetTransmission"/>
			<method name="SetTurboByToggle"/>
			<method name="TogglePreviewExhaustFlameByToggle"/>
			<method name="TogglePreviewSmokeByToggle"/>
		</type>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime" ignoreIfMissing="1">
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton">
			<method name="OnAction"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField">
			<method name="OnDecrement"/>
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
			<method name="OnIncrement"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel">
			<method name="OnScrollbarClicked"/>
			<method name="OnScrollbarClicked"/>
			<method name="ResetDebugManager"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField">
			<method name="OnDecrement"/>
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
			<method name="OnIncrement"/>
		</type>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule" ignoreIfMissing="1">
		<type fullname="UnityEngine.GameObject">
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
		</type>
	</assembly>
</linker>
