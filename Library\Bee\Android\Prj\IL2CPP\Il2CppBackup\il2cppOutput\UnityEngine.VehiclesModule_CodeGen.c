﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.Collider UnityEngine.WheelHit::get_collider()
extern void WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D (void);
// 0x00000002 UnityEngine.Vector3 UnityEngine.WheelHit::get_point()
extern void WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C (void);
// 0x00000003 UnityEngine.Vector3 UnityEngine.WheelHit::get_normal()
extern void WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162 (void);
// 0x00000004 System.Single UnityEngine.WheelHit::get_force()
extern void WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E (void);
// 0x00000005 System.Single UnityEngine.WheelHit::get_forwardSlip()
extern void WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE (void);
// 0x00000006 System.Single UnityEngine.WheelHit::get_sidewaysSlip()
extern void WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B (void);
// 0x00000007 System.Single UnityEngine.WheelCollider::get_radius()
extern void WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F (void);
// 0x00000008 System.Void UnityEngine.WheelCollider::set_radius(System.Single)
extern void WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184 (void);
// 0x00000009 System.Single UnityEngine.WheelCollider::get_suspensionDistance()
extern void WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F (void);
// 0x0000000A System.Void UnityEngine.WheelCollider::set_suspensionDistance(System.Single)
extern void WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348 (void);
// 0x0000000B UnityEngine.JointSpring UnityEngine.WheelCollider::get_suspensionSpring()
extern void WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543 (void);
// 0x0000000C System.Void UnityEngine.WheelCollider::set_suspensionSpring(UnityEngine.JointSpring)
extern void WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7 (void);
// 0x0000000D System.Void UnityEngine.WheelCollider::set_forceAppPointDistance(System.Single)
extern void WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB (void);
// 0x0000000E System.Void UnityEngine.WheelCollider::set_mass(System.Single)
extern void WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E (void);
// 0x0000000F System.Void UnityEngine.WheelCollider::set_wheelDampingRate(System.Single)
extern void WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505 (void);
// 0x00000010 UnityEngine.WheelFrictionCurve UnityEngine.WheelCollider::get_forwardFriction()
extern void WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0 (void);
// 0x00000011 System.Void UnityEngine.WheelCollider::set_forwardFriction(UnityEngine.WheelFrictionCurve)
extern void WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510 (void);
// 0x00000012 UnityEngine.WheelFrictionCurve UnityEngine.WheelCollider::get_sidewaysFriction()
extern void WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8 (void);
// 0x00000013 System.Void UnityEngine.WheelCollider::set_sidewaysFriction(UnityEngine.WheelFrictionCurve)
extern void WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8 (void);
// 0x00000014 System.Single UnityEngine.WheelCollider::get_motorTorque()
extern void WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F (void);
// 0x00000015 System.Void UnityEngine.WheelCollider::set_motorTorque(System.Single)
extern void WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8 (void);
// 0x00000016 System.Single UnityEngine.WheelCollider::get_brakeTorque()
extern void WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF (void);
// 0x00000017 System.Void UnityEngine.WheelCollider::set_brakeTorque(System.Single)
extern void WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB (void);
// 0x00000018 System.Single UnityEngine.WheelCollider::get_steerAngle()
extern void WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5 (void);
// 0x00000019 System.Void UnityEngine.WheelCollider::set_steerAngle(System.Single)
extern void WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3 (void);
// 0x0000001A System.Boolean UnityEngine.WheelCollider::get_isGrounded()
extern void WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327 (void);
// 0x0000001B System.Single UnityEngine.WheelCollider::get_rpm()
extern void WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52 (void);
// 0x0000001C System.Void UnityEngine.WheelCollider::GetWorldPose(UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890 (void);
// 0x0000001D System.Boolean UnityEngine.WheelCollider::GetGroundHit(UnityEngine.WheelHit&)
extern void WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2 (void);
// 0x0000001E System.Void UnityEngine.WheelCollider::get_suspensionSpring_Injected(UnityEngine.JointSpring&)
extern void WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB (void);
// 0x0000001F System.Void UnityEngine.WheelCollider::set_suspensionSpring_Injected(UnityEngine.JointSpring&)
extern void WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F (void);
// 0x00000020 System.Void UnityEngine.WheelCollider::get_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)
extern void WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2 (void);
// 0x00000021 System.Void UnityEngine.WheelCollider::set_forwardFriction_Injected(UnityEngine.WheelFrictionCurve&)
extern void WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205 (void);
// 0x00000022 System.Void UnityEngine.WheelCollider::get_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)
extern void WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1 (void);
// 0x00000023 System.Void UnityEngine.WheelCollider::set_sidewaysFriction_Injected(UnityEngine.WheelFrictionCurve&)
extern void WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9 (void);
static Il2CppMethodPointer s_methodPointers[35] = 
{
	WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D,
	WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C,
	WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162,
	WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E,
	WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE,
	WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B,
	WheelCollider_get_radius_m68CC3DE301E0C6226E85F99D853365DA1244CD1F,
	WheelCollider_set_radius_m43AA4113465733E26DD8B01774C9AD6C32424184,
	WheelCollider_get_suspensionDistance_mD1EE97B03FB1E744BCF24DC988806F52AE98A55F,
	WheelCollider_set_suspensionDistance_mED5D19A954DD3C7030B4D336466BF0EA1BB20348,
	WheelCollider_get_suspensionSpring_m046582E587BF5AC228C2022D25A822BC4B4B9543,
	WheelCollider_set_suspensionSpring_m2E4ACAB8BE03081A58E217DAE84819CFA51A82B7,
	WheelCollider_set_forceAppPointDistance_m115F21BA7FB72B5006506B77F12DE97E4E497CDB,
	WheelCollider_set_mass_mD6C319FDA15773358ACC232179C404D16938468E,
	WheelCollider_set_wheelDampingRate_mAE9252CADFC480A8CCE089424C86B17261FC4505,
	WheelCollider_get_forwardFriction_mB0B8AB7668623F8646FFB1E6CD81E540B6EE51B0,
	WheelCollider_set_forwardFriction_m1932872F22DD4D5584C0D14FBDCD56848F111510,
	WheelCollider_get_sidewaysFriction_m7924ABBBB268B7F1FD8630733B6375CAFC2621E8,
	WheelCollider_set_sidewaysFriction_mB2062696F29B4275C7F5B2874FD333ECD2325DA8,
	WheelCollider_get_motorTorque_m2DDC12B29636466D0A818AE6C324E5FA586A253F,
	WheelCollider_set_motorTorque_m4958AAF7D867CF7570420F9BAFAF04DC904F02A8,
	WheelCollider_get_brakeTorque_mEE588755CF490EE5274CE0AD385D6F7CACB46FFF,
	WheelCollider_set_brakeTorque_mB9B216C57C275470907C7DB35185E2F192DC8DAB,
	WheelCollider_get_steerAngle_mF0A004554F1509F1DA233AC71A7A2C9C36CC9EE5,
	WheelCollider_set_steerAngle_m7BF83B27D8956355F873537939BE9F35CF3113C3,
	WheelCollider_get_isGrounded_mA24C86DA670A02E01ACE176B4AF30F801244F327,
	WheelCollider_get_rpm_m7AFAA813ED3965AE4B2A2E7CBC6FB2B26B68ED52,
	WheelCollider_GetWorldPose_m8C41FA2AE9ED543AB94A6E3226523A2FE83FA890,
	WheelCollider_GetGroundHit_mCB73878577BC5AAEBEA8572FA62326C4C71B3EF2,
	WheelCollider_get_suspensionSpring_Injected_mDFEF80392DFDA3DDCD0B19BFE18F34D229AE22FB,
	WheelCollider_set_suspensionSpring_Injected_m5A1DA63E7226CBB700642005FC0C23C40406482F,
	WheelCollider_get_forwardFriction_Injected_mB8145EF60E5E0CFB03E4374DA9622192F657D0F2,
	WheelCollider_set_forwardFriction_Injected_mB3FA9425FEC0B0217C2A3EB62FCBF4DBA278C205,
	WheelCollider_get_sidewaysFriction_Injected_m18A6CB2191D1CE62B131E11BFB5308F8A9C6FCD1,
	WheelCollider_set_sidewaysFriction_Injected_mFE019F748A809846714267E3AA9DB8C49616A1A9,
};
extern void WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk (void);
extern void WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk (void);
extern void WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk (void);
extern void WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk (void);
extern void WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk (void);
extern void WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[6] = 
{
	{ 0x06000001, WheelHit_get_collider_m6E4EC72042B62AE08340187AD82D6B8FEF8DE09D_AdjustorThunk },
	{ 0x06000002, WheelHit_get_point_m9F7E614D1E1C6BAAF3392D3E21FF98FB9C9E370C_AdjustorThunk },
	{ 0x06000003, WheelHit_get_normal_mD59C9DB64546E22ADE3CA24DEE638F6F8168F162_AdjustorThunk },
	{ 0x06000004, WheelHit_get_force_m682670758B8A3054D2A12AE5179625DC1899EA1E_AdjustorThunk },
	{ 0x06000005, WheelHit_get_forwardSlip_m1BA6AA2379368EFE2200B92AB4CA20F1AD537CCE_AdjustorThunk },
	{ 0x06000006, WheelHit_get_sidewaysSlip_m7E27E0B36C1CC096C56B8815B7C1E7D380D6627B_AdjustorThunk },
};
static const int32_t s_InvokerIndices[35] = 
{
	6204,
	6320,
	6320,
	6259,
	6259,
	6259,
	6259,
	5087,
	6259,
	5087,
	6185,
	5023,
	5087,
	5087,
	5087,
	6331,
	5149,
	6331,
	5149,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6108,
	6259,
	2345,
	3483,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_VehiclesModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_VehiclesModule_CodeGenModule = 
{
	"UnityEngine.VehiclesModule.dll",
	35,
	s_methodPointers,
	6,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
