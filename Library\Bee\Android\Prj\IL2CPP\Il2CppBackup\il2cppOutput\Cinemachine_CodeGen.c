﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void CinemachineCameraOffset::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineCameraOffset_PostPipelineStageCallback_m36D3DE5935DCBF3866D1A04DE6C86232F6870C94 (void);
// 0x00000002 System.Void CinemachineCameraOffset::.ctor()
extern void CinemachineCameraOffset__ctor_m48BC91DAD0744C7C8940F71C5645FA1AAE20E3D9 (void);
// 0x00000003 System.Void CinemachineRecomposer::Reset()
extern void CinemachineRecomposer_Reset_m721C26E1921CC614A0B9653470D76120DA4A1A88 (void);
// 0x00000004 System.Void CinemachineRecomposer::OnValidate()
extern void CinemachineRecomposer_OnValidate_m0118F16A3F380F1603D53C8ABAEAD111D9F3D45C (void);
// 0x00000005 System.Void CinemachineRecomposer::PrePipelineMutateCameraStateCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CameraState&,System.Single)
extern void CinemachineRecomposer_PrePipelineMutateCameraStateCallback_m0F3A514A395A6A72371C8D11120240BC780CBE0D (void);
// 0x00000006 System.Void CinemachineRecomposer::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineRecomposer_PostPipelineStageCallback_m44B330DFCC057842F064E3FFA08F5BCBC9BD728D (void);
// 0x00000007 System.Void CinemachineRecomposer::.ctor()
extern void CinemachineRecomposer__ctor_mEE6D1CF234B300888E4018BA0B9E907EDC2EB5A0 (void);
// 0x00000008 System.Void CinemachineTouchInputMapper::Start()
extern void CinemachineTouchInputMapper_Start_mCA2D226EC0ADDA714D6D5739643B42B426CD89C3 (void);
// 0x00000009 System.Single CinemachineTouchInputMapper::GetInputAxis(System.String)
extern void CinemachineTouchInputMapper_GetInputAxis_m31434BB01472E0F418BBA87C7EEFC67BC0286F93 (void);
// 0x0000000A System.Void CinemachineTouchInputMapper::.ctor()
extern void CinemachineTouchInputMapper__ctor_mB558B3890882BD7E91A6F7D6796471BDE8A1371F (void);
// 0x0000000B System.Void CinemachineMixer::OnPlayableDestroy(UnityEngine.Playables.Playable)
extern void CinemachineMixer_OnPlayableDestroy_m8EF4D80AF627D5197E3A56CFB5471851682A5B2A (void);
// 0x0000000C System.Void CinemachineMixer::PrepareFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData)
extern void CinemachineMixer_PrepareFrame_m81662AAB1B6192B14A843280FE2B175BB9E914C6 (void);
// 0x0000000D System.Void CinemachineMixer::ProcessFrame(UnityEngine.Playables.Playable,UnityEngine.Playables.FrameData,System.Object)
extern void CinemachineMixer_ProcessFrame_m418F0FEE9F0A0859D7E9FA0466BCBD86E8187BEC (void);
// 0x0000000E System.Single CinemachineMixer::GetDeltaTime(System.Single)
extern void CinemachineMixer_GetDeltaTime_mA23D38C7845030D33A6D94306FB6DC68D6E4C0B1 (void);
// 0x0000000F System.Void CinemachineMixer::.ctor()
extern void CinemachineMixer__ctor_m43226AD5B71D782F9A289DADDFA605F14EC5BAAB (void);
// 0x00000010 System.Void CinemachineMixer/MasterDirectorDelegate::.ctor(System.Object,System.IntPtr)
extern void MasterDirectorDelegate__ctor_m83539FCF0AC43523B96FD137E8AA5A42AB2B118F (void);
// 0x00000011 UnityEngine.Playables.PlayableDirector CinemachineMixer/MasterDirectorDelegate::Invoke()
extern void MasterDirectorDelegate_Invoke_mBC7EB8EBD0BDA2D2046F24D4980B6715F096AB5E (void);
// 0x00000012 System.IAsyncResult CinemachineMixer/MasterDirectorDelegate::BeginInvoke(System.AsyncCallback,System.Object)
extern void MasterDirectorDelegate_BeginInvoke_mB3C2C71E773F03BB818A8122FA2A6C0CB9EA4EB6 (void);
// 0x00000013 UnityEngine.Playables.PlayableDirector CinemachineMixer/MasterDirectorDelegate::EndInvoke(System.IAsyncResult)
extern void MasterDirectorDelegate_EndInvoke_mD3784473798E58BF357BE527A7F6344035ADF6B6 (void);
// 0x00000014 UnityEngine.Playables.Playable CinemachineShot::CreatePlayable(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject)
extern void CinemachineShot_CreatePlayable_m876B363449F4CBD92B79CF0D4098E86BCDCE1FB1 (void);
// 0x00000015 System.Void CinemachineShot::GatherProperties(UnityEngine.Playables.PlayableDirector,UnityEngine.Timeline.IPropertyCollector)
extern void CinemachineShot_GatherProperties_mC2D43CED0267E1DBCC60FB7612D7426FAB56EEF4 (void);
// 0x00000016 System.Void CinemachineShot::.ctor()
extern void CinemachineShot__ctor_m080F3FCCB80E98AD3F356CEB1792ADF91A6ECA58 (void);
// 0x00000017 System.Boolean CinemachineShotPlayable::get_IsValid()
extern void CinemachineShotPlayable_get_IsValid_m36F22868B393BA5DA3FC41EA6591F1D0A560AB4E (void);
// 0x00000018 System.Void CinemachineShotPlayable::.ctor()
extern void CinemachineShotPlayable__ctor_m87875C5233C927BCFE76D23359C6F2482862EE82 (void);
// 0x00000019 UnityEngine.Playables.Playable CinemachineTrack::CreateTrackMixer(UnityEngine.Playables.PlayableGraph,UnityEngine.GameObject,System.Int32)
extern void CinemachineTrack_CreateTrackMixer_m8EE1F8D1A8E629AE03F5E77C662398C071065C5F (void);
// 0x0000001A System.Void CinemachineTrack::.ctor()
extern void CinemachineTrack__ctor_mA1D06F056F153E3AE21729382FB666101343AC06 (void);
// 0x0000001B UnityEngine.Vector3 Cinemachine.Cinemachine3rdPersonAim::get_AimTarget()
extern void Cinemachine3rdPersonAim_get_AimTarget_m23E3401D3A53DAC3A76EE4A1352A14FC0DCA0AE5 (void);
// 0x0000001C System.Void Cinemachine.Cinemachine3rdPersonAim::set_AimTarget(UnityEngine.Vector3)
extern void Cinemachine3rdPersonAim_set_AimTarget_mE4F573A3F1927B24028F96164D800720DB5B68EC (void);
// 0x0000001D System.Void Cinemachine.Cinemachine3rdPersonAim::OnValidate()
extern void Cinemachine3rdPersonAim_OnValidate_m7BAF5D55CC15037CC1150E710297600BD4BE6BA0 (void);
// 0x0000001E System.Void Cinemachine.Cinemachine3rdPersonAim::Reset()
extern void Cinemachine3rdPersonAim_Reset_mCEF67490840ED3EFBFBB78C04D8E4C8099D0C29E (void);
// 0x0000001F System.Boolean Cinemachine.Cinemachine3rdPersonAim::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void Cinemachine3rdPersonAim_OnTransitionFromCamera_mFD1F710BCC846BB1AC464C7F620B93F6F665DF84 (void);
// 0x00000020 System.Void Cinemachine.Cinemachine3rdPersonAim::DrawReticle(Cinemachine.CinemachineBrain)
extern void Cinemachine3rdPersonAim_DrawReticle_m9464DCF9E9F904E35F13CD5AF9BEF2A291E3286F (void);
// 0x00000021 UnityEngine.Vector3 Cinemachine.Cinemachine3rdPersonAim::ComputeLookAtPoint(UnityEngine.Vector3,UnityEngine.Transform)
extern void Cinemachine3rdPersonAim_ComputeLookAtPoint_m08DB1A8A0EDC4CAB21C46784AEE17504D488EC37 (void);
// 0x00000022 UnityEngine.Vector3 Cinemachine.Cinemachine3rdPersonAim::ComputeAimTarget(UnityEngine.Vector3,UnityEngine.Transform)
extern void Cinemachine3rdPersonAim_ComputeAimTarget_mAD4E37AB5D0E30973FA3BFDD256B58047664EB3E (void);
// 0x00000023 System.Void Cinemachine.Cinemachine3rdPersonAim::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void Cinemachine3rdPersonAim_PostPipelineStageCallback_mB571F3E81E8927115D710714F1A29A6550684D39 (void);
// 0x00000024 System.Void Cinemachine.Cinemachine3rdPersonAim::.ctor()
extern void Cinemachine3rdPersonAim__ctor_m2525440B9C8CAA21C979B22C606725A0E785EB96 (void);
// 0x00000025 System.String Cinemachine.CinemachineBlendListCamera::get_Description()
extern void CinemachineBlendListCamera_get_Description_m0941C9392EECA1206B6BF679E780695C616E9B07 (void);
// 0x00000026 System.Void Cinemachine.CinemachineBlendListCamera::Reset()
extern void CinemachineBlendListCamera_Reset_mDD50F2FF236E2328EFBD97C6A99B8D2F4ECB0C83 (void);
// 0x00000027 Cinemachine.ICinemachineCamera Cinemachine.CinemachineBlendListCamera::get_LiveChild()
extern void CinemachineBlendListCamera_get_LiveChild_mE2463EEF9842BA071B0D5698CA98FFCF7F775BB5 (void);
// 0x00000028 System.Void Cinemachine.CinemachineBlendListCamera::set_LiveChild(Cinemachine.ICinemachineCamera)
extern void CinemachineBlendListCamera_set_LiveChild_m134EFC93CFB6638A8E089E722D07F95C783F3091 (void);
// 0x00000029 System.Boolean Cinemachine.CinemachineBlendListCamera::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineBlendListCamera_IsLiveChild_m71AB8ED658ACA24CF3C2D25C8027C63C01CA8D86 (void);
// 0x0000002A Cinemachine.CameraState Cinemachine.CinemachineBlendListCamera::get_State()
extern void CinemachineBlendListCamera_get_State_mFA582C59AC2C5C06679C57F2284D44A052BEE226 (void);
// 0x0000002B UnityEngine.Transform Cinemachine.CinemachineBlendListCamera::get_LookAt()
extern void CinemachineBlendListCamera_get_LookAt_m33F5E145090555ECC93D032BD4526D31AB77F017 (void);
// 0x0000002C System.Void Cinemachine.CinemachineBlendListCamera::set_LookAt(UnityEngine.Transform)
extern void CinemachineBlendListCamera_set_LookAt_m38A9835EAE1E1A4899FBABDCCC66ECAC58B9E5E4 (void);
// 0x0000002D UnityEngine.Transform Cinemachine.CinemachineBlendListCamera::get_Follow()
extern void CinemachineBlendListCamera_get_Follow_m8AA5DA6AF429EE8185E2740D944CCD175AFF4BD7 (void);
// 0x0000002E System.Void Cinemachine.CinemachineBlendListCamera::set_Follow(UnityEngine.Transform)
extern void CinemachineBlendListCamera_set_Follow_mF060900F24D08F87FF981547B8D3C87C3BDF2AEB (void);
// 0x0000002F System.Void Cinemachine.CinemachineBlendListCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineBlendListCamera_OnTargetObjectWarped_mA952BA4606E3780159339C519346BAB07AAD1ACA (void);
// 0x00000030 System.Void Cinemachine.CinemachineBlendListCamera::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineBlendListCamera_ForceCameraPosition_m6F04BCFD9AC1D8341BCFB7681C128A65BDEB1D69 (void);
// 0x00000031 System.Void Cinemachine.CinemachineBlendListCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineBlendListCamera_OnTransitionFromCamera_m7F58275919D731A8AA08A5C092E48CA007353DD5 (void);
// 0x00000032 System.Void Cinemachine.CinemachineBlendListCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineBlendListCamera_InternalUpdateCameraState_m40AC5E255043ECF1E3F1A298AE6DE61D4ABD922B (void);
// 0x00000033 System.Void Cinemachine.CinemachineBlendListCamera::OnEnable()
extern void CinemachineBlendListCamera_OnEnable_mB815DE4599D5FBB447020211EAB63AC68B93513B (void);
// 0x00000034 System.Void Cinemachine.CinemachineBlendListCamera::OnDisable()
extern void CinemachineBlendListCamera_OnDisable_m864D1836A3089997C125E1A2333A9C861236FC9B (void);
// 0x00000035 System.Void Cinemachine.CinemachineBlendListCamera::OnTransformChildrenChanged()
extern void CinemachineBlendListCamera_OnTransformChildrenChanged_m9669FDD4827F2174D9272D191D94CFA07B78E91F (void);
// 0x00000036 System.Void Cinemachine.CinemachineBlendListCamera::OnGuiHandler()
extern void CinemachineBlendListCamera_OnGuiHandler_mD5504D8F57DFD851B783D05F613921BBE4ECB2CF (void);
// 0x00000037 Cinemachine.CinemachineVirtualCameraBase[] Cinemachine.CinemachineBlendListCamera::get_ChildCameras()
extern void CinemachineBlendListCamera_get_ChildCameras_mD71F131580DF9BF8AF30C04A88411DCF72F42502 (void);
// 0x00000038 System.Boolean Cinemachine.CinemachineBlendListCamera::get_IsBlending()
extern void CinemachineBlendListCamera_get_IsBlending_m104363E79BFE66E0E0B31105DBC0A5E4084DC66B (void);
// 0x00000039 System.Void Cinemachine.CinemachineBlendListCamera::InvalidateListOfChildren()
extern void CinemachineBlendListCamera_InvalidateListOfChildren_m9C0D36C4AB842E080526AC77BADF7490C352DFCF (void);
// 0x0000003A System.Void Cinemachine.CinemachineBlendListCamera::UpdateListOfChildren()
extern void CinemachineBlendListCamera_UpdateListOfChildren_m999C8C3A9C7A0D9CE621661F76893F9A8678CA91 (void);
// 0x0000003B System.Void Cinemachine.CinemachineBlendListCamera::ValidateInstructions()
extern void CinemachineBlendListCamera_ValidateInstructions_mFCDD468A1417A70852DAE2FBF1EBB0FAAD34CC3B (void);
// 0x0000003C System.Void Cinemachine.CinemachineBlendListCamera::AdvanceCurrentInstruction(System.Single)
extern void CinemachineBlendListCamera_AdvanceCurrentInstruction_mC401379B7CD61F5220AAF2B0510CB2833CEBC99D (void);
// 0x0000003D System.Void Cinemachine.CinemachineBlendListCamera::.ctor()
extern void CinemachineBlendListCamera__ctor_m90660A98A6ED5C8ECFCDF652585F12EFA4D70172 (void);
// 0x0000003E System.Int32 Cinemachine.ICameraOverrideStack::SetCameraOverride(System.Int32,Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera,System.Single,System.Single)
// 0x0000003F System.Void Cinemachine.ICameraOverrideStack::ReleaseCameraOverride(System.Int32)
// 0x00000040 UnityEngine.Vector3 Cinemachine.ICameraOverrideStack::get_DefaultWorldUp()
// 0x00000041 UnityEngine.Camera Cinemachine.CinemachineBrain::get_OutputCamera()
extern void CinemachineBrain_get_OutputCamera_m1568A7744F9BB298C982829FC39B109309372C31 (void);
// 0x00000042 UnityEngine.GameObject Cinemachine.CinemachineBrain::get_ControlledObject()
extern void CinemachineBrain_get_ControlledObject_m4CD88A4899AEF4F49102BDB05CBA9B765359038A (void);
// 0x00000043 System.Void Cinemachine.CinemachineBrain::set_ControlledObject(UnityEngine.GameObject)
extern void CinemachineBrain_set_ControlledObject_mD9F1F9159DDE12F32E750A5C240E2A8A58DA8A6B (void);
// 0x00000044 Cinemachine.ICinemachineCamera Cinemachine.CinemachineBrain::get_SoloCamera()
extern void CinemachineBrain_get_SoloCamera_m67BF8A681B5B36B2CA6A805D658B5A820566B8D9 (void);
// 0x00000045 System.Void Cinemachine.CinemachineBrain::set_SoloCamera(Cinemachine.ICinemachineCamera)
extern void CinemachineBrain_set_SoloCamera_m36932C5D48A60ED8C5D09FA4BA122597F0EABE98 (void);
// 0x00000046 UnityEngine.Color Cinemachine.CinemachineBrain::GetSoloGUIColor()
extern void CinemachineBrain_GetSoloGUIColor_m52EAD28FC43A8FB8E68D9B81D31196203717746F (void);
// 0x00000047 UnityEngine.Vector3 Cinemachine.CinemachineBrain::get_DefaultWorldUp()
extern void CinemachineBrain_get_DefaultWorldUp_m6A39EB2B1E0E480FB48E43F5DB484146FEB8F2E5 (void);
// 0x00000048 System.Void Cinemachine.CinemachineBrain::OnEnable()
extern void CinemachineBrain_OnEnable_m1A8E38FFC7712CAFEBF612C52AB10D1252DBDC54 (void);
// 0x00000049 System.Void Cinemachine.CinemachineBrain::OnDisable()
extern void CinemachineBrain_OnDisable_m87D60FA550ECBAE892D61B0AD4C5C90CE921A271 (void);
// 0x0000004A System.Void Cinemachine.CinemachineBrain::OnSceneLoaded(UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode)
extern void CinemachineBrain_OnSceneLoaded_m941252E4FC98544162388E3A6E68CAECE069650B (void);
// 0x0000004B System.Void Cinemachine.CinemachineBrain::OnSceneUnloaded(UnityEngine.SceneManagement.Scene)
extern void CinemachineBrain_OnSceneUnloaded_m08B136F6DC08111E8DC3AFD55ABF060E29EAD520 (void);
// 0x0000004C System.Void Cinemachine.CinemachineBrain::Awake()
extern void CinemachineBrain_Awake_m872E4DAC67A4E15E61766E50B54D977BC83FDE02 (void);
// 0x0000004D System.Void Cinemachine.CinemachineBrain::Start()
extern void CinemachineBrain_Start_m6105C964B1E34A10A420525C978E5A012A053171 (void);
// 0x0000004E System.Void Cinemachine.CinemachineBrain::OnGuiHandler()
extern void CinemachineBrain_OnGuiHandler_m3505267B18EB9CE74F62F6B14552A5F2182743FD (void);
// 0x0000004F System.Collections.IEnumerator Cinemachine.CinemachineBrain::AfterPhysics()
extern void CinemachineBrain_AfterPhysics_mA5199823EA8CCF0C5391E2D4A8AA46A93853E68A (void);
// 0x00000050 System.Void Cinemachine.CinemachineBrain::LateUpdate()
extern void CinemachineBrain_LateUpdate_mEAC5D1D033ECDBA15E1E018E7727943FD7D890EB (void);
// 0x00000051 System.Void Cinemachine.CinemachineBrain::ManualUpdate()
extern void CinemachineBrain_ManualUpdate_m0AAF1ECD02C1B65B0768D85DD4D505CD5C3A41CF (void);
// 0x00000052 System.Single Cinemachine.CinemachineBrain::GetEffectiveDeltaTime(System.Boolean)
extern void CinemachineBrain_GetEffectiveDeltaTime_mDCB1FDB9DB624E5511DD9F5298794C5753977ED9 (void);
// 0x00000053 System.Void Cinemachine.CinemachineBrain::UpdateVirtualCameras(Cinemachine.CinemachineCore/UpdateFilter,System.Single)
extern void CinemachineBrain_UpdateVirtualCameras_m209254C78DF1F06E11EA4CF30C7F953BDCED0218 (void);
// 0x00000054 Cinemachine.ICinemachineCamera Cinemachine.CinemachineBrain::get_ActiveVirtualCamera()
extern void CinemachineBrain_get_ActiveVirtualCamera_m0AEE8E6E46E3235D9ADE864DA6BE7BB57F2D86D7 (void);
// 0x00000055 Cinemachine.ICinemachineCamera Cinemachine.CinemachineBrain::DeepCamBFromBlend(Cinemachine.CinemachineBlend)
extern void CinemachineBrain_DeepCamBFromBlend_mA590E041B46933535A30A3F007BA2B83E7BE40CD (void);
// 0x00000056 System.Boolean Cinemachine.CinemachineBrain::IsLiveInBlend(Cinemachine.ICinemachineCamera)
extern void CinemachineBrain_IsLiveInBlend_m2A0E5AD2048B66FA82E7CB01A83335907B1941CB (void);
// 0x00000057 System.Boolean Cinemachine.CinemachineBrain::get_IsBlending()
extern void CinemachineBrain_get_IsBlending_m028DE1EDFAD04881374A33196A718E7673E7B2F3 (void);
// 0x00000058 Cinemachine.CinemachineBlend Cinemachine.CinemachineBrain::get_ActiveBlend()
extern void CinemachineBrain_get_ActiveBlend_mC6FF3504F32F7C30500A98F22BD906D468406ED1 (void);
// 0x00000059 System.Void Cinemachine.CinemachineBrain::set_ActiveBlend(Cinemachine.CinemachineBlend)
extern void CinemachineBrain_set_ActiveBlend_mE00036C78A72075BDEC47F0BA1AB052317ACCCB3 (void);
// 0x0000005A System.Int32 Cinemachine.CinemachineBrain::GetBrainFrame(System.Int32)
extern void CinemachineBrain_GetBrainFrame_mFEAD91A91F349B785E8FC4B5A1C5F500EC9DEA40 (void);
// 0x0000005B System.Int32 Cinemachine.CinemachineBrain::SetCameraOverride(System.Int32,Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera,System.Single,System.Single)
extern void CinemachineBrain_SetCameraOverride_m9A267D3B457F1C7681D972FD090F4DCCA00E49B8 (void);
// 0x0000005C System.Void Cinemachine.CinemachineBrain::ReleaseCameraOverride(System.Int32)
extern void CinemachineBrain_ReleaseCameraOverride_m40840EAC3788CBA1D998861D7F670EB7F79D1730 (void);
// 0x0000005D System.Void Cinemachine.CinemachineBrain::ProcessActiveCamera(System.Single)
extern void CinemachineBrain_ProcessActiveCamera_m6DD3871A02BB3BADC96C9D9DAB459C5F95D53104 (void);
// 0x0000005E System.Void Cinemachine.CinemachineBrain::UpdateFrame0(System.Single)
extern void CinemachineBrain_UpdateFrame0_mAA20AD58FBC4C024898A7074C3E4A0BE84DD53BC (void);
// 0x0000005F System.Void Cinemachine.CinemachineBrain::ComputeCurrentBlend(Cinemachine.CinemachineBlend&,System.Int32)
extern void CinemachineBrain_ComputeCurrentBlend_mA8E475FA0505785CB41BB62A5074FE09F3352E27 (void);
// 0x00000060 System.Boolean Cinemachine.CinemachineBrain::IsLive(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineBrain_IsLive_mC5661731D686A2BD2E926BE5F872C8F3033D3058 (void);
// 0x00000061 Cinemachine.CameraState Cinemachine.CinemachineBrain::get_CurrentCameraState()
extern void CinemachineBrain_get_CurrentCameraState_m4FD443F016CFCA5FCDFFF17E95A93162D18847C2 (void);
// 0x00000062 System.Void Cinemachine.CinemachineBrain::set_CurrentCameraState(Cinemachine.CameraState)
extern void CinemachineBrain_set_CurrentCameraState_mFAA3A0AB30A002E3B9CC0BA83F9FF119428693DA (void);
// 0x00000063 Cinemachine.ICinemachineCamera Cinemachine.CinemachineBrain::TopCameraFromPriorityQueue()
extern void CinemachineBrain_TopCameraFromPriorityQueue_m1AE36BA00968869CC730BA006AF4409471F96B62 (void);
// 0x00000064 Cinemachine.CinemachineBlendDefinition Cinemachine.CinemachineBrain::LookupBlend(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera)
extern void CinemachineBrain_LookupBlend_m4C4810AA410AE7A9E3D55E8B1E1DE5478AAE3398 (void);
// 0x00000065 System.Void Cinemachine.CinemachineBrain::PushStateToUnityCamera(Cinemachine.CameraState&)
extern void CinemachineBrain_PushStateToUnityCamera_m052D86E4AADB90FC7816637DCE4375D86C44B0D8 (void);
// 0x00000066 System.Void Cinemachine.CinemachineBrain::.ctor()
extern void CinemachineBrain__ctor_m516DC64F86617E7E269C399E53E69F06BF176690 (void);
// 0x00000067 System.Void Cinemachine.CinemachineBrain::.cctor()
extern void CinemachineBrain__cctor_m0922906BE096A29914CAFDDE952DE34C0F5A208E (void);
// 0x00000068 System.Void Cinemachine.CinemachineBrain/BrainEvent::.ctor()
extern void BrainEvent__ctor_mCAE49277912EB5AFFBA23BBBD84697BC258A56D7 (void);
// 0x00000069 System.Void Cinemachine.CinemachineBrain/VcamActivatedEvent::.ctor()
extern void VcamActivatedEvent__ctor_m4917789E0C570A5217E093044D8E26D8AD1F7068 (void);
// 0x0000006A System.Boolean Cinemachine.CinemachineBrain/BrainFrame::get_Active()
extern void BrainFrame_get_Active_m6EB1C7313FF391608D67E752A0AD3541B3D5D5E0 (void);
// 0x0000006B System.Void Cinemachine.CinemachineBrain/BrainFrame::.ctor()
extern void BrainFrame__ctor_mC0DB41887D1ED757D4F74BF031E2AAF607F7BADA (void);
// 0x0000006C System.Void Cinemachine.CinemachineBrain/<AfterPhysics>d__38::.ctor(System.Int32)
extern void U3CAfterPhysicsU3Ed__38__ctor_mE033F15A46D3FD0237ABADFEB3D782ADE681BD4E (void);
// 0x0000006D System.Void Cinemachine.CinemachineBrain/<AfterPhysics>d__38::System.IDisposable.Dispose()
extern void U3CAfterPhysicsU3Ed__38_System_IDisposable_Dispose_m8703F2ECD5260EF844C340F13AD765F936A3C8E3 (void);
// 0x0000006E System.Boolean Cinemachine.CinemachineBrain/<AfterPhysics>d__38::MoveNext()
extern void U3CAfterPhysicsU3Ed__38_MoveNext_m6056E41EEF22CA8302EE1A09F74147B962477299 (void);
// 0x0000006F System.Object Cinemachine.CinemachineBrain/<AfterPhysics>d__38::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAfterPhysicsU3Ed__38_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD8D086ECFE9EF92884C38C6F9339DB9B074347E3 (void);
// 0x00000070 System.Void Cinemachine.CinemachineBrain/<AfterPhysics>d__38::System.Collections.IEnumerator.Reset()
extern void U3CAfterPhysicsU3Ed__38_System_Collections_IEnumerator_Reset_m9F3C31823E4B368049D2F255C5705D54FE9B06D3 (void);
// 0x00000071 System.Object Cinemachine.CinemachineBrain/<AfterPhysics>d__38::System.Collections.IEnumerator.get_Current()
extern void U3CAfterPhysicsU3Ed__38_System_Collections_IEnumerator_get_Current_mAC87E4A1BB6BB77033032B82B7B6308F8BFD94C8 (void);
// 0x00000072 System.String Cinemachine.CinemachineClearShot::get_Description()
extern void CinemachineClearShot_get_Description_m830D661CB2C22A60CCCA38A64CFCB64759660EFD (void);
// 0x00000073 Cinemachine.ICinemachineCamera Cinemachine.CinemachineClearShot::get_LiveChild()
extern void CinemachineClearShot_get_LiveChild_m68AB5E861C65827DA91695C012A8C5EF2F6CA49C (void);
// 0x00000074 System.Void Cinemachine.CinemachineClearShot::set_LiveChild(Cinemachine.ICinemachineCamera)
extern void CinemachineClearShot_set_LiveChild_m3BB96A8921B39DCCFDDF2091E780AD0BE9A211B1 (void);
// 0x00000075 Cinemachine.CameraState Cinemachine.CinemachineClearShot::get_State()
extern void CinemachineClearShot_get_State_m643D23A83988EAD2D9CB4B9979CEBF1716E9D2F0 (void);
// 0x00000076 System.Boolean Cinemachine.CinemachineClearShot::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineClearShot_IsLiveChild_mA941A1F0A4D4D73CAAE6D2331F5B36B28BEF6F5F (void);
// 0x00000077 UnityEngine.Transform Cinemachine.CinemachineClearShot::get_LookAt()
extern void CinemachineClearShot_get_LookAt_m324E396C55C0E70D186F124BBD66D099896ADD35 (void);
// 0x00000078 System.Void Cinemachine.CinemachineClearShot::set_LookAt(UnityEngine.Transform)
extern void CinemachineClearShot_set_LookAt_mF45391B864BFF22B5C2D6A717F2D5D5AF9671941 (void);
// 0x00000079 UnityEngine.Transform Cinemachine.CinemachineClearShot::get_Follow()
extern void CinemachineClearShot_get_Follow_mA100668F5F7EB1E9E458369281641609393742C5 (void);
// 0x0000007A System.Void Cinemachine.CinemachineClearShot::set_Follow(UnityEngine.Transform)
extern void CinemachineClearShot_set_Follow_mE6584C1C0517D5FF4743A6191968B43EDA7E6222 (void);
// 0x0000007B System.Void Cinemachine.CinemachineClearShot::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineClearShot_OnTargetObjectWarped_m9D58039F25A57EA74EB96A763BB698DE9B3165FF (void);
// 0x0000007C System.Void Cinemachine.CinemachineClearShot::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineClearShot_ForceCameraPosition_m2CC2714CBC099553603FB2FAAA2FE749BA9D67C3 (void);
// 0x0000007D System.Void Cinemachine.CinemachineClearShot::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineClearShot_InternalUpdateCameraState_m21E11A40459CC3BAD2FC423929BA502004BB063E (void);
// 0x0000007E System.Void Cinemachine.CinemachineClearShot::OnEnable()
extern void CinemachineClearShot_OnEnable_mE4470ED652FFFC8F50A2D1AD1A0B9442DBB1C5A0 (void);
// 0x0000007F System.Void Cinemachine.CinemachineClearShot::OnDisable()
extern void CinemachineClearShot_OnDisable_m83BB1AD09642334F2723A7A378DBCFB08BA0031F (void);
// 0x00000080 System.Void Cinemachine.CinemachineClearShot::OnTransformChildrenChanged()
extern void CinemachineClearShot_OnTransformChildrenChanged_mD3CDAD6B945F552A33D22047282151F8BEC23ECE (void);
// 0x00000081 System.Void Cinemachine.CinemachineClearShot::OnGuiHandler()
extern void CinemachineClearShot_OnGuiHandler_m3278977602C7A7D938A92BDA13782A57AFB32799 (void);
// 0x00000082 System.Boolean Cinemachine.CinemachineClearShot::get_IsBlending()
extern void CinemachineClearShot_get_IsBlending_m23069403051CC3F2737C0DE0C23A47360C8562CF (void);
// 0x00000083 Cinemachine.CinemachineBlend Cinemachine.CinemachineClearShot::get_ActiveBlend()
extern void CinemachineClearShot_get_ActiveBlend_mFB3601D76976133C2A35D860F7E54A6B0FD8661F (void);
// 0x00000084 Cinemachine.CinemachineVirtualCameraBase[] Cinemachine.CinemachineClearShot::get_ChildCameras()
extern void CinemachineClearShot_get_ChildCameras_mF4017037F2C43479CB50DC87BA28BB5436C2BF61 (void);
// 0x00000085 System.Void Cinemachine.CinemachineClearShot::InvalidateListOfChildren()
extern void CinemachineClearShot_InvalidateListOfChildren_mFE7676A3C7D11C5BC7F204BC437D6F31692B81DE (void);
// 0x00000086 System.Void Cinemachine.CinemachineClearShot::ResetRandomization()
extern void CinemachineClearShot_ResetRandomization_mA4C0B56EE654F56A3E6219A4BDB8835E30A3510A (void);
// 0x00000087 System.Void Cinemachine.CinemachineClearShot::UpdateListOfChildren()
extern void CinemachineClearShot_UpdateListOfChildren_m3BCC28E6A250B389B8FE737800409B3B6E235253 (void);
// 0x00000088 Cinemachine.ICinemachineCamera Cinemachine.CinemachineClearShot::ChooseCurrentCamera(UnityEngine.Vector3)
extern void CinemachineClearShot_ChooseCurrentCamera_mE99EB0A510F96CBBF70A18692ACC962927641265 (void);
// 0x00000089 Cinemachine.CinemachineVirtualCameraBase[] Cinemachine.CinemachineClearShot::Randomize(Cinemachine.CinemachineVirtualCameraBase[])
extern void CinemachineClearShot_Randomize_m827ED70BADD0E80B15B02B3DEB15D5BFFE0F0726 (void);
// 0x0000008A Cinemachine.CinemachineBlendDefinition Cinemachine.CinemachineClearShot::LookupBlend(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera)
extern void CinemachineClearShot_LookupBlend_m0DA62E97306A03CF6C1A0CADF68A0D3E7F45BD26 (void);
// 0x0000008B System.Void Cinemachine.CinemachineClearShot::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineClearShot_OnTransitionFromCamera_mCD2B8D5BB280F63D0E7E26A32E9C9D8C8DA2DCBF (void);
// 0x0000008C System.Void Cinemachine.CinemachineClearShot::.ctor()
extern void CinemachineClearShot__ctor_m08FE4B603DA5147A26D2F588496C2D29B4CA4407 (void);
// 0x0000008D System.Void Cinemachine.CinemachineClearShot/<>c::.cctor()
extern void U3CU3Ec__cctor_m862E9E374DE10AA80B867C44F2270190CAE8FB00 (void);
// 0x0000008E System.Void Cinemachine.CinemachineClearShot/<>c::.ctor()
extern void U3CU3Ec__ctor_m9BE881E5BAC3662017282D2C1215496CB6A6088F (void);
// 0x0000008F System.Int32 Cinemachine.CinemachineClearShot/<>c::<Randomize>b__49_0(Cinemachine.CinemachineClearShot/Pair,Cinemachine.CinemachineClearShot/Pair)
extern void U3CU3Ec_U3CRandomizeU3Eb__49_0_m60CCAD65D9F7AF30D47B1D1371FE7D708D009FFD (void);
// 0x00000090 System.Boolean Cinemachine.CinemachineCollider::IsTargetObscured(Cinemachine.ICinemachineCamera)
extern void CinemachineCollider_IsTargetObscured_m96234E8EC6FBE238EF157CD24CEC943D0BCB5867 (void);
// 0x00000091 System.Boolean Cinemachine.CinemachineCollider::CameraWasDisplaced(Cinemachine.ICinemachineCamera)
extern void CinemachineCollider_CameraWasDisplaced_m5ED18CC78ED65A1CD9D46C1305102A66FB5EFA2E (void);
// 0x00000092 System.Single Cinemachine.CinemachineCollider::GetCameraDisplacementDistance(Cinemachine.ICinemachineCamera)
extern void CinemachineCollider_GetCameraDisplacementDistance_m2D776E08DF6628DE5D5DDAFE5854D28A2E4C36B6 (void);
// 0x00000093 System.Void Cinemachine.CinemachineCollider::OnValidate()
extern void CinemachineCollider_OnValidate_m44D1FA5E930E6EDEF78DD0AA50B0C0A31214EEF3 (void);
// 0x00000094 System.Void Cinemachine.CinemachineCollider::OnDestroy()
extern void CinemachineCollider_OnDestroy_m9F8D62EC841589145D4216849773715675EC67EA (void);
// 0x00000095 System.Collections.Generic.List`1<System.Collections.Generic.List`1<UnityEngine.Vector3>> Cinemachine.CinemachineCollider::get_DebugPaths()
extern void CinemachineCollider_get_DebugPaths_m75A49F8F027244F2D5421164CA8588670F3628D0 (void);
// 0x00000096 System.Single Cinemachine.CinemachineCollider::GetMaxDampTime()
extern void CinemachineCollider_GetMaxDampTime_m5CDDC02FB1FEB4B82A51BA9745D61DE0EFA23BF1 (void);
// 0x00000097 System.Void Cinemachine.CinemachineCollider::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineCollider_OnTargetObjectWarped_mF64F1DDBA4302BF278BE4555C9718F8B2795A7DE (void);
// 0x00000098 System.Void Cinemachine.CinemachineCollider::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineCollider_PostPipelineStageCallback_m809856B0A1F1112AA1610C853672E5B9FEFBCD2F (void);
// 0x00000099 UnityEngine.Vector3 Cinemachine.CinemachineCollider::PreserveLineOfSight(Cinemachine.CameraState&,Cinemachine.CinemachineCollider/VcamExtraState&)
extern void CinemachineCollider_PreserveLineOfSight_m15D0A6B0A018DAD34254B7E091CAD3D3E0C1EF84 (void);
// 0x0000009A UnityEngine.Vector3 Cinemachine.CinemachineCollider::PullCameraInFrontOfNearestObstacle(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32,UnityEngine.RaycastHit&)
extern void CinemachineCollider_PullCameraInFrontOfNearestObstacle_mC6EE17778ED949B252ECEEF01262A6391180D176 (void);
// 0x0000009B UnityEngine.Vector3 Cinemachine.CinemachineCollider::PushCameraBack(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit,UnityEngine.Vector3,UnityEngine.Plane,System.Single,System.Int32,Cinemachine.CinemachineCollider/VcamExtraState&)
extern void CinemachineCollider_PushCameraBack_mA652E6C1E69285E05F18DA6C4EEFBF6FFF150ED5 (void);
// 0x0000009C System.Boolean Cinemachine.CinemachineCollider::GetWalkingDirection(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit,UnityEngine.Vector3&)
extern void CinemachineCollider_GetWalkingDirection_mC5607290A0F1FB8EDAC4AE401280FCAE25DA56D7 (void);
// 0x0000009D System.Single Cinemachine.CinemachineCollider::GetPushBackDistance(UnityEngine.Ray,UnityEngine.Plane,System.Single,UnityEngine.Vector3)
extern void CinemachineCollider_GetPushBackDistance_m327096D12B295C2A3D536767D22302B0D90722F2 (void);
// 0x0000009E System.Single Cinemachine.CinemachineCollider::ClampRayToBounds(UnityEngine.Ray,System.Single,UnityEngine.Bounds)
extern void CinemachineCollider_ClampRayToBounds_m1ECEE60359130905CFE336780028F27C584C223C (void);
// 0x0000009F UnityEngine.Vector3 Cinemachine.CinemachineCollider::RespectCameraRadius(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineCollider_RespectCameraRadius_mCB35CD04F62EDCBE0A154B3EC455D33029F380C6 (void);
// 0x000000A0 System.Boolean Cinemachine.CinemachineCollider::CheckForTargetObstructions(Cinemachine.CameraState)
extern void CinemachineCollider_CheckForTargetObstructions_mBE0D7797C4F714F6532ECF0428225FCC7717EBE9 (void);
// 0x000000A1 System.Boolean Cinemachine.CinemachineCollider::IsTargetOffscreen(Cinemachine.CameraState)
extern void CinemachineCollider_IsTargetOffscreen_m92E26E85E88FE62A1C918DEF9728DE9FB46D5A7D (void);
// 0x000000A2 System.Void Cinemachine.CinemachineCollider::.ctor()
extern void CinemachineCollider__ctor_m444FCEC0E8391FB73605BDF0C844292EA81BDF12 (void);
// 0x000000A3 System.Void Cinemachine.CinemachineCollider::.cctor()
extern void CinemachineCollider__cctor_m5A26E23CA4A900A4A0C35BFB46F4EBD893459106 (void);
// 0x000000A4 System.Void Cinemachine.CinemachineCollider/VcamExtraState::AddPointToDebugPath(UnityEngine.Vector3)
extern void VcamExtraState_AddPointToDebugPath_m5787D51C699C1557B53987E5134067F98C6B8296 (void);
// 0x000000A5 System.Single Cinemachine.CinemachineCollider/VcamExtraState::ApplyDistanceSmoothing(System.Single,System.Single)
extern void VcamExtraState_ApplyDistanceSmoothing_m0BB6D66F3143716C66AAC3F7DD85103C7E6589E3 (void);
// 0x000000A6 System.Void Cinemachine.CinemachineCollider/VcamExtraState::UpdateDistanceSmoothing(System.Single)
extern void VcamExtraState_UpdateDistanceSmoothing_m585CE40738CF2D7E9EC0BE89C357AD84A40320AC (void);
// 0x000000A7 System.Void Cinemachine.CinemachineCollider/VcamExtraState::ResetDistanceSmoothing(System.Single)
extern void VcamExtraState_ResetDistanceSmoothing_mC71B210379ABEBC812529240E983852865E535B0 (void);
// 0x000000A8 System.Void Cinemachine.CinemachineCollider/VcamExtraState::.ctor()
extern void VcamExtraState__ctor_m1453192FF3335F6FCB28CBF592949C721553225E (void);
// 0x000000A9 System.Boolean Cinemachine.CinemachineConfiner::CameraWasDisplaced(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineConfiner_CameraWasDisplaced_m4B54B58BECF591B468EFB069B776C6A647CAFCD0 (void);
// 0x000000AA System.Single Cinemachine.CinemachineConfiner::GetCameraDisplacementDistance(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineConfiner_GetCameraDisplacementDistance_m8DF8F6DB8A7FB9E1C6FF59F90CB2EAA54FFE5B54 (void);
// 0x000000AB System.Void Cinemachine.CinemachineConfiner::OnValidate()
extern void CinemachineConfiner_OnValidate_mD68867689E0CCB06D68F538F50CA7E8279520E1F (void);
// 0x000000AC System.Void Cinemachine.CinemachineConfiner::ConnectToVcam(System.Boolean)
extern void CinemachineConfiner_ConnectToVcam_mFFA1FD919F341B7EEF2D2C6B6D9CEC32B3E98E55 (void);
// 0x000000AD System.Boolean Cinemachine.CinemachineConfiner::get_IsValid()
extern void CinemachineConfiner_get_IsValid_mE3539CF8E20004A17B9F2EFA432008AB6AFB47BE (void);
// 0x000000AE System.Single Cinemachine.CinemachineConfiner::GetMaxDampTime()
extern void CinemachineConfiner_GetMaxDampTime_m0131BF6E586F7B2A942C73860AF613239799C219 (void);
// 0x000000AF System.Void Cinemachine.CinemachineConfiner::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineConfiner_PostPipelineStageCallback_m9610D6CFAFCBAD9960646A4019F9D81C0BCCC374 (void);
// 0x000000B0 System.Void Cinemachine.CinemachineConfiner::InvalidatePathCache()
extern void CinemachineConfiner_InvalidatePathCache_mFA907955B4C61CBAA3ACD9A7AED13D621FA5A11F (void);
// 0x000000B1 System.Boolean Cinemachine.CinemachineConfiner::ValidatePathCache()
extern void CinemachineConfiner_ValidatePathCache_mDAF15DF5F5AACDBC66DCFFFAEF2B6051F37BC9D0 (void);
// 0x000000B2 UnityEngine.Vector3 Cinemachine.CinemachineConfiner::ConfinePoint(UnityEngine.Vector3)
extern void CinemachineConfiner_ConfinePoint_m0A14A9BF85C02366E350E1EC81A1DE148DC80B86 (void);
// 0x000000B3 UnityEngine.Vector3 Cinemachine.CinemachineConfiner::ConfineScreenEdges(Cinemachine.CameraState&)
extern void CinemachineConfiner_ConfineScreenEdges_mE8CC4E3F1B7174138AE889D6D30FEA594A8FDBD6 (void);
// 0x000000B4 System.Void Cinemachine.CinemachineConfiner::.ctor()
extern void CinemachineConfiner__ctor_m9ECC25FEE5601874435238F3ECECCEFA7D81DCBC (void);
// 0x000000B5 System.Void Cinemachine.CinemachineConfiner/VcamExtraState::.ctor()
extern void VcamExtraState__ctor_m48E5BAB2FDF7BAD3C985A2748834A2E817A845CE (void);
// 0x000000B6 System.Void Cinemachine.CinemachineConfiner2D::InvalidateCache()
extern void CinemachineConfiner2D_InvalidateCache_m23A7A98BCB8232C84FF3D926CF349507FE2AB1F5 (void);
// 0x000000B7 System.Boolean Cinemachine.CinemachineConfiner2D::ValidateCache(System.Single)
extern void CinemachineConfiner2D_ValidateCache_m8FB8D402430AAABEB978E0556BC38A0256749E21 (void);
// 0x000000B8 System.Void Cinemachine.CinemachineConfiner2D::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineConfiner2D_PostPipelineStageCallback_m47AE9484EB1757CF28AB748E796712689A65EFB6 (void);
// 0x000000B9 System.Single Cinemachine.CinemachineConfiner2D::CalculateHalfFrustumHeight(Cinemachine.CameraState&,System.Single&)
extern void CinemachineConfiner2D_CalculateHalfFrustumHeight_mC47DB1A291F62784AB03D741DD0DB4ECF8B3A182 (void);
// 0x000000BA System.Void Cinemachine.CinemachineConfiner2D::OnValidate()
extern void CinemachineConfiner2D_OnValidate_m0FB17BA22FDE2D9BC32275280FFC68C162700025 (void);
// 0x000000BB System.Void Cinemachine.CinemachineConfiner2D::Reset()
extern void CinemachineConfiner2D_Reset_m074BE83016F529CFFD82ED0F34FA9B33DF1210B4 (void);
// 0x000000BC System.Void Cinemachine.CinemachineConfiner2D::.ctor()
extern void CinemachineConfiner2D__ctor_m9235E19358FC51F49B2068BA645C74BD8F4B2A66 (void);
// 0x000000BD System.Void Cinemachine.CinemachineConfiner2D/VcamExtraState::.ctor()
extern void VcamExtraState__ctor_mEEA50920B90E79DAFDCAAF5EAFD9837371AC1CE1 (void);
// 0x000000BE System.Void Cinemachine.CinemachineConfiner2D/ShapeCache::Invalidate()
extern void ShapeCache_Invalidate_m5D7E303E5E807F2388F06AD1054E5CCD8D36D6C3 (void);
// 0x000000BF System.Boolean Cinemachine.CinemachineConfiner2D/ShapeCache::ValidateCache(UnityEngine.Collider2D,System.Single,System.Single,System.Single,System.Boolean&)
extern void ShapeCache_ValidateCache_mD71A58F6BBBAC79F5BB6560704C33935C77A76DD (void);
// 0x000000C0 System.Boolean Cinemachine.CinemachineConfiner2D/ShapeCache::IsValid(UnityEngine.Collider2D&,System.Single&,System.Single&,System.Single&)
extern void ShapeCache_IsValid_m2AA4B5B68785B4D264FB2D8DECD0163D6FAAC0D9 (void);
// 0x000000C1 System.Void Cinemachine.CinemachineConfiner2D/ShapeCache::CalculateDeltaTransformationMatrix()
extern void ShapeCache_CalculateDeltaTransformationMatrix_m74CA864B0DDA47E56B35B3CA1380C35FEA603784 (void);
// 0x000000C2 System.Void Cinemachine.CinemachineDollyCart::FixedUpdate()
extern void CinemachineDollyCart_FixedUpdate_mB5D80C62F5DF944DE98DAA79F23E9E677216D9AE (void);
// 0x000000C3 System.Void Cinemachine.CinemachineDollyCart::Update()
extern void CinemachineDollyCart_Update_mF2C2A034A4659BCAE045E6D59E910F623A8B69A4 (void);
// 0x000000C4 System.Void Cinemachine.CinemachineDollyCart::LateUpdate()
extern void CinemachineDollyCart_LateUpdate_mE77BE2CAB8C334BF046D4C691F65C8DC591708BB (void);
// 0x000000C5 System.Void Cinemachine.CinemachineDollyCart::SetCartPosition(System.Single)
extern void CinemachineDollyCart_SetCartPosition_m8D2EB52AEFEE154042511DC15A0CB6CDDDC15C71 (void);
// 0x000000C6 System.Void Cinemachine.CinemachineDollyCart::.ctor()
extern void CinemachineDollyCart__ctor_m4FEC8B62A80B5094F3115C0F4B41F86D6EF11B3D (void);
// 0x000000C7 Cinemachine.CameraState Cinemachine.CinemachineExternalCamera::get_State()
extern void CinemachineExternalCamera_get_State_mBABCF116AACDF9F7B1EC287AE7C1635528E7EED3 (void);
// 0x000000C8 UnityEngine.Transform Cinemachine.CinemachineExternalCamera::get_LookAt()
extern void CinemachineExternalCamera_get_LookAt_mF881F376FB11ACA576E57B715D4034745816C915 (void);
// 0x000000C9 System.Void Cinemachine.CinemachineExternalCamera::set_LookAt(UnityEngine.Transform)
extern void CinemachineExternalCamera_set_LookAt_mD0250A3A73E2CC5944FAEAE32A26483770A5518B (void);
// 0x000000CA UnityEngine.Transform Cinemachine.CinemachineExternalCamera::get_Follow()
extern void CinemachineExternalCamera_get_Follow_m2916C53ED295FB44E3CE86AECE2307E65F19C069 (void);
// 0x000000CB System.Void Cinemachine.CinemachineExternalCamera::set_Follow(UnityEngine.Transform)
extern void CinemachineExternalCamera_set_Follow_m9E81314AF50F6886533FB8C8150FBE511EF3956F (void);
// 0x000000CC System.Void Cinemachine.CinemachineExternalCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineExternalCamera_InternalUpdateCameraState_m0F6CD90D9B9C26B6EF5DB147F76C860ACED29EE5 (void);
// 0x000000CD System.Void Cinemachine.CinemachineExternalCamera::.ctor()
extern void CinemachineExternalCamera__ctor_mC0CAEDCB42F896DA0DA3428A0EF7833DFC5FC19B (void);
// 0x000000CE System.Void Cinemachine.CinemachineFollowZoom::OnValidate()
extern void CinemachineFollowZoom_OnValidate_m13A1978C9D7F5EFC9BF01222449A20EB0E9093CF (void);
// 0x000000CF System.Single Cinemachine.CinemachineFollowZoom::GetMaxDampTime()
extern void CinemachineFollowZoom_GetMaxDampTime_mF2A9EFE150660B8EDD7A77C17AE91E1A038AE161 (void);
// 0x000000D0 System.Void Cinemachine.CinemachineFollowZoom::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineFollowZoom_PostPipelineStageCallback_mA40A25C1B4DCDD0B6A379F79E51A301EB3E31455 (void);
// 0x000000D1 System.Void Cinemachine.CinemachineFollowZoom::.ctor()
extern void CinemachineFollowZoom__ctor_mC5DAA40532992DFCA0F91904996B0B3897F1FB99 (void);
// 0x000000D2 System.Void Cinemachine.CinemachineFollowZoom/VcamExtraState::.ctor()
extern void VcamExtraState__ctor_m0FB88B41FA5F8BD8063E9D409580F36352C45C1E (void);
// 0x000000D3 System.Void Cinemachine.CinemachineFreeLook::OnValidate()
extern void CinemachineFreeLook_OnValidate_m363CFBDA6195290F8CA67B2217CF1CF0F080AF08 (void);
// 0x000000D4 Cinemachine.CinemachineVirtualCamera Cinemachine.CinemachineFreeLook::GetRig(System.Int32)
extern void CinemachineFreeLook_GetRig_mC818384734A05604CCA51B5D6EA87FD91692CE01 (void);
// 0x000000D5 System.Boolean Cinemachine.CinemachineFreeLook::get_RigsAreCreated()
extern void CinemachineFreeLook_get_RigsAreCreated_m0EF51109842812390E9FD4D4DA9539CAF415DC03 (void);
// 0x000000D6 System.String[] Cinemachine.CinemachineFreeLook::get_RigNames()
extern void CinemachineFreeLook_get_RigNames_m2D9317645B40626F534D57764C904CB434416D66 (void);
// 0x000000D7 System.Void Cinemachine.CinemachineFreeLook::OnEnable()
extern void CinemachineFreeLook_OnEnable_m5751340BED71741D4B1ED9EB5D613DC8CCEEEBB9 (void);
// 0x000000D8 System.Void Cinemachine.CinemachineFreeLook::UpdateInputAxisProvider()
extern void CinemachineFreeLook_UpdateInputAxisProvider_m3A81D0027CC71494F03A9F7719377DB881FD5606 (void);
// 0x000000D9 System.Void Cinemachine.CinemachineFreeLook::OnDestroy()
extern void CinemachineFreeLook_OnDestroy_m5C53E6195807FE9AEF38B2BF06B88B0F9CCEE566 (void);
// 0x000000DA System.Void Cinemachine.CinemachineFreeLook::OnTransformChildrenChanged()
extern void CinemachineFreeLook_OnTransformChildrenChanged_mE19235181CB5C85CECE97956BBA126F38F348C95 (void);
// 0x000000DB System.Void Cinemachine.CinemachineFreeLook::Reset()
extern void CinemachineFreeLook_Reset_mAC8C0893352644CE2C0C70389AB32E8D20C6EB2C (void);
// 0x000000DC System.Boolean Cinemachine.CinemachineFreeLook::get_PreviousStateIsValid()
extern void CinemachineFreeLook_get_PreviousStateIsValid_m4CAC7225F4A310446049C315D2BA4905DEFA6D91 (void);
// 0x000000DD System.Void Cinemachine.CinemachineFreeLook::set_PreviousStateIsValid(System.Boolean)
extern void CinemachineFreeLook_set_PreviousStateIsValid_m07EFCE7F6A806488A86EF327582638ADA4255EC6 (void);
// 0x000000DE Cinemachine.CameraState Cinemachine.CinemachineFreeLook::get_State()
extern void CinemachineFreeLook_get_State_m13B7AD3940C466FC4C874CD5F1A9CAA4B6A321F4 (void);
// 0x000000DF UnityEngine.Transform Cinemachine.CinemachineFreeLook::get_LookAt()
extern void CinemachineFreeLook_get_LookAt_mE9065E51E77ACA8FE7CB8788F5D91F250DC5E0AC (void);
// 0x000000E0 System.Void Cinemachine.CinemachineFreeLook::set_LookAt(UnityEngine.Transform)
extern void CinemachineFreeLook_set_LookAt_m4DB3D49CF03F62C0585D34F051D06F2C8A13929D (void);
// 0x000000E1 UnityEngine.Transform Cinemachine.CinemachineFreeLook::get_Follow()
extern void CinemachineFreeLook_get_Follow_mB0013712098265B408811C0EA19C35179F1E4F65 (void);
// 0x000000E2 System.Void Cinemachine.CinemachineFreeLook::set_Follow(UnityEngine.Transform)
extern void CinemachineFreeLook_set_Follow_mC3D18085B595FAEE0549F5FB957E380B96ADD041 (void);
// 0x000000E3 System.Boolean Cinemachine.CinemachineFreeLook::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineFreeLook_IsLiveChild_m693A2AC305EAD54F31A5F3FA0494D242D1C66F65 (void);
// 0x000000E4 System.Void Cinemachine.CinemachineFreeLook::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineFreeLook_OnTargetObjectWarped_mA77D864BEC0A4204CBE065D077904941C29ED3CA (void);
// 0x000000E5 System.Void Cinemachine.CinemachineFreeLook::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineFreeLook_ForceCameraPosition_m4C9B3A953CD78C0151638A6EB22E5723E2E2F18C (void);
// 0x000000E6 System.Void Cinemachine.CinemachineFreeLook::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineFreeLook_InternalUpdateCameraState_mBC6EFA124C0307703CC6F169A3A4C3AEDA6B8C16 (void);
// 0x000000E7 System.Void Cinemachine.CinemachineFreeLook::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineFreeLook_OnTransitionFromCamera_mAD0C40F31D468A93C2B98EC909B8DD942A0EA21F (void);
// 0x000000E8 System.Boolean Cinemachine.CinemachineFreeLook::RequiresUserInput()
extern void CinemachineFreeLook_RequiresUserInput_m3A9F28FA422DC8CECCB7C9B948DB1413177C8F0E (void);
// 0x000000E9 System.Single Cinemachine.CinemachineFreeLook::GetYAxisClosestValue(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineFreeLook_GetYAxisClosestValue_mF6FAD00F83ABD48C48ED1B601960B78EE1D515FA (void);
// 0x000000EA System.Single Cinemachine.CinemachineFreeLook::SteepestDescent(UnityEngine.Vector3)
extern void CinemachineFreeLook_SteepestDescent_mD0C5E94C99FEB356B454D145AE4DC24BDC3E285C (void);
// 0x000000EB System.Void Cinemachine.CinemachineFreeLook::InvalidateRigCache()
extern void CinemachineFreeLook_InvalidateRigCache_m32D43BE156C871062C4AB803167753025541915F (void);
// 0x000000EC System.Void Cinemachine.CinemachineFreeLook::DestroyRigs()
extern void CinemachineFreeLook_DestroyRigs_mB5D2ACF7E82A9274864492651FF1E89F52CEF61F (void);
// 0x000000ED Cinemachine.CinemachineVirtualCamera[] Cinemachine.CinemachineFreeLook::CreateRigs(Cinemachine.CinemachineVirtualCamera[])
extern void CinemachineFreeLook_CreateRigs_m7F1B92C30B61F914D287F9C21204E4D43D9CF695 (void);
// 0x000000EE System.Boolean Cinemachine.CinemachineFreeLook::UpdateRigCache()
extern void CinemachineFreeLook_UpdateRigCache_m00120D105154A191F750E816B03B96E7F536465D (void);
// 0x000000EF System.Collections.Generic.List`1<Cinemachine.CinemachineVirtualCamera> Cinemachine.CinemachineFreeLook::LocateExistingRigs(System.Boolean)
extern void CinemachineFreeLook_LocateExistingRigs_m52CEC6667817F4620E9DD2E8C3B5FF455AACFDB6 (void);
// 0x000000F0 System.Single Cinemachine.CinemachineFreeLook::UpdateXAxisHeading(Cinemachine.CinemachineOrbitalTransposer,System.Single,UnityEngine.Vector3)
extern void CinemachineFreeLook_UpdateXAxisHeading_m0B3B9A759E52F9B1315784D2D69144BF6290F23C (void);
// 0x000000F1 System.Void Cinemachine.CinemachineFreeLook::PushSettingsToRigs()
extern void CinemachineFreeLook_PushSettingsToRigs_mB5BABAC99655EF23A3C9A0C7AF8F5AE0736F4764 (void);
// 0x000000F2 System.Single Cinemachine.CinemachineFreeLook::GetYAxisValue()
extern void CinemachineFreeLook_GetYAxisValue_m3006D2D07A4A8D430C3FD2CBD51B74B8D75D8E77 (void);
// 0x000000F3 Cinemachine.CameraState Cinemachine.CinemachineFreeLook::CalculateNewState(UnityEngine.Vector3,System.Single)
extern void CinemachineFreeLook_CalculateNewState_m1F68499B410474DAD5C866BAE393F9184DC3C59D (void);
// 0x000000F4 UnityEngine.Vector3 Cinemachine.CinemachineFreeLook::GetLocalPositionForCameraFromInput(System.Single)
extern void CinemachineFreeLook_GetLocalPositionForCameraFromInput_mEF44FF027954B4E5392A082E9EE82879B8489062 (void);
// 0x000000F5 System.Void Cinemachine.CinemachineFreeLook::UpdateCachedSpline()
extern void CinemachineFreeLook_UpdateCachedSpline_mC36FEF90EF6D07349BF33559D9B9B84DB7004473 (void);
// 0x000000F6 System.Void Cinemachine.CinemachineFreeLook::OnBeforeSerialize()
extern void CinemachineFreeLook_OnBeforeSerialize_mEEEFD1F60C989CF81C9D7C057FCA2CA187892B07 (void);
// 0x000000F7 System.Void Cinemachine.CinemachineFreeLook::.ctor()
extern void CinemachineFreeLook__ctor_mA79F3B7F572AFECE180B79550161A5CA8F99D2BA (void);
// 0x000000F8 System.Single Cinemachine.CinemachineFreeLook::<SteepestDescent>g__AngleFunction|47_0(System.Single,Cinemachine.CinemachineFreeLook/<>c__DisplayClass47_0&)
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__AngleFunctionU7C47_0_mCFC89952A9D256117954C562141B92F4A2864CFD (void);
// 0x000000F9 System.Single Cinemachine.CinemachineFreeLook::<SteepestDescent>g__SlopeOfAngleFunction|47_1(System.Single,Cinemachine.CinemachineFreeLook/<>c__DisplayClass47_0&)
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__SlopeOfAngleFunctionU7C47_1_mDF48B916ABBB4ED512EFFC07EB1192E7046FFF78 (void);
// 0x000000FA System.Single Cinemachine.CinemachineFreeLook::<SteepestDescent>g__InitialGuess|47_2(Cinemachine.CinemachineFreeLook/<>c__DisplayClass47_0&)
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__InitialGuessU7C47_2_m8E7D6612A2BAB6E1E8378B3DBC739BCAA0408924 (void);
// 0x000000FB System.Void Cinemachine.CinemachineFreeLook::<SteepestDescent>g__ChooseBestAngle|47_3(System.Single,Cinemachine.CinemachineFreeLook/<>c__DisplayClass47_0&,Cinemachine.CinemachineFreeLook/<>c__DisplayClass47_1&)
extern void CinemachineFreeLook_U3CSteepestDescentU3Eg__ChooseBestAngleU7C47_3_m0B66CC7A9760FA805C041D04C7A526B0CADBA8EB (void);
// 0x000000FC System.Void Cinemachine.CinemachineFreeLook/Orbit::.ctor(System.Single,System.Single)
extern void Orbit__ctor_mEB696AB489A6AE5AB5E7D9A366CC741BD8CA23A1 (void);
// 0x000000FD System.Void Cinemachine.CinemachineFreeLook/CreateRigDelegate::.ctor(System.Object,System.IntPtr)
extern void CreateRigDelegate__ctor_m6F6CFEC727ADF8F761C0056334415C9ED1549E89 (void);
// 0x000000FE Cinemachine.CinemachineVirtualCamera Cinemachine.CinemachineFreeLook/CreateRigDelegate::Invoke(Cinemachine.CinemachineFreeLook,System.String,Cinemachine.CinemachineVirtualCamera)
extern void CreateRigDelegate_Invoke_mD0C181884C0203123DE69968919E4E757D0891A9 (void);
// 0x000000FF System.IAsyncResult Cinemachine.CinemachineFreeLook/CreateRigDelegate::BeginInvoke(Cinemachine.CinemachineFreeLook,System.String,Cinemachine.CinemachineVirtualCamera,System.AsyncCallback,System.Object)
extern void CreateRigDelegate_BeginInvoke_m4EAA983AAAB1A3D011BAD83EE4C2F78C0FD3F292 (void);
// 0x00000100 Cinemachine.CinemachineVirtualCamera Cinemachine.CinemachineFreeLook/CreateRigDelegate::EndInvoke(System.IAsyncResult)
extern void CreateRigDelegate_EndInvoke_m3E5499B7F9B4885E2EB7C8B47E3D6AD1CB8A0814 (void);
// 0x00000101 System.Void Cinemachine.CinemachineFreeLook/DestroyRigDelegate::.ctor(System.Object,System.IntPtr)
extern void DestroyRigDelegate__ctor_m780F29C1662666F2141E7006CFB0C8F0652675F6 (void);
// 0x00000102 System.Void Cinemachine.CinemachineFreeLook/DestroyRigDelegate::Invoke(UnityEngine.GameObject)
extern void DestroyRigDelegate_Invoke_m0123F51EB7D308EDBBA8C97D1766CA6644C0BB26 (void);
// 0x00000103 System.IAsyncResult Cinemachine.CinemachineFreeLook/DestroyRigDelegate::BeginInvoke(UnityEngine.GameObject,System.AsyncCallback,System.Object)
extern void DestroyRigDelegate_BeginInvoke_m79C7D4F44884457F4626AF19E71F7C4E4D012B21 (void);
// 0x00000104 System.Void Cinemachine.CinemachineFreeLook/DestroyRigDelegate::EndInvoke(System.IAsyncResult)
extern void DestroyRigDelegate_EndInvoke_m7903709A25979E37D5542712A29559AB5984345B (void);
// 0x00000105 System.Single Cinemachine.CinemachineMixingCamera::GetWeight(System.Int32)
extern void CinemachineMixingCamera_GetWeight_mB558B38DFD8EBE6D7ADA9E8ADEE2693E64FF5D42 (void);
// 0x00000106 System.Void Cinemachine.CinemachineMixingCamera::SetWeight(System.Int32,System.Single)
extern void CinemachineMixingCamera_SetWeight_mC5610C703FF2805EBEA1F154BBCE444DDD242194 (void);
// 0x00000107 System.Single Cinemachine.CinemachineMixingCamera::GetWeight(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineMixingCamera_GetWeight_m83BF99075E8598CBDCFE55582A0A7FC77B9A9104 (void);
// 0x00000108 System.Void Cinemachine.CinemachineMixingCamera::SetWeight(Cinemachine.CinemachineVirtualCameraBase,System.Single)
extern void CinemachineMixingCamera_SetWeight_m2BF760E1983299AE8F5F5B9D01DAB3EE1332061E (void);
// 0x00000109 Cinemachine.ICinemachineCamera Cinemachine.CinemachineMixingCamera::get_LiveChild()
extern void CinemachineMixingCamera_get_LiveChild_m26D809A14332652C435B82A94EE0282E0561F817 (void);
// 0x0000010A System.Void Cinemachine.CinemachineMixingCamera::set_LiveChild(Cinemachine.ICinemachineCamera)
extern void CinemachineMixingCamera_set_LiveChild_mD28EB775418289F251C97828FD7E5BF55F39C81F (void);
// 0x0000010B Cinemachine.CameraState Cinemachine.CinemachineMixingCamera::get_State()
extern void CinemachineMixingCamera_get_State_m44E23B7EEA1FD0097519789D7DA2AE7626D8F955 (void);
// 0x0000010C UnityEngine.Transform Cinemachine.CinemachineMixingCamera::get_LookAt()
extern void CinemachineMixingCamera_get_LookAt_m960F48555B39A69B413D665CD807CA2DE57DA7AD (void);
// 0x0000010D System.Void Cinemachine.CinemachineMixingCamera::set_LookAt(UnityEngine.Transform)
extern void CinemachineMixingCamera_set_LookAt_m6C2A62904E3B6AC74AFD6B0990F9B79F3F015C57 (void);
// 0x0000010E UnityEngine.Transform Cinemachine.CinemachineMixingCamera::get_Follow()
extern void CinemachineMixingCamera_get_Follow_m54220CF80B21E7625F10D7EBCF3B2F552C055049 (void);
// 0x0000010F System.Void Cinemachine.CinemachineMixingCamera::set_Follow(UnityEngine.Transform)
extern void CinemachineMixingCamera_set_Follow_m71453E2740CEF75552DEAB376B40AD533A8C8FF3 (void);
// 0x00000110 System.Void Cinemachine.CinemachineMixingCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineMixingCamera_OnTargetObjectWarped_m5762D3BDA766EB34FD869C5C858FF6C03CF935E0 (void);
// 0x00000111 System.Void Cinemachine.CinemachineMixingCamera::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineMixingCamera_ForceCameraPosition_m5DEFC312F29680754ED0F18260B4834F546F77B9 (void);
// 0x00000112 System.Void Cinemachine.CinemachineMixingCamera::OnEnable()
extern void CinemachineMixingCamera_OnEnable_mA1CA5ED5B17241E0E896B3DA0A016B5A748A8C05 (void);
// 0x00000113 System.Void Cinemachine.CinemachineMixingCamera::OnTransformChildrenChanged()
extern void CinemachineMixingCamera_OnTransformChildrenChanged_m450976DCA651635EA5D669F4E5CCBF384A5FB61B (void);
// 0x00000114 System.Void Cinemachine.CinemachineMixingCamera::OnValidate()
extern void CinemachineMixingCamera_OnValidate_m2CA0AF2221650AE7A37D2709A554CE12B4456E0F (void);
// 0x00000115 System.Boolean Cinemachine.CinemachineMixingCamera::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineMixingCamera_IsLiveChild_m91A57CA82BB9F6EF14A0E42839E3365C390B0BD4 (void);
// 0x00000116 Cinemachine.CinemachineVirtualCameraBase[] Cinemachine.CinemachineMixingCamera::get_ChildCameras()
extern void CinemachineMixingCamera_get_ChildCameras_m7F3535370ECD871E57E965197E774E2F12247D16 (void);
// 0x00000117 System.Void Cinemachine.CinemachineMixingCamera::InvalidateListOfChildren()
extern void CinemachineMixingCamera_InvalidateListOfChildren_m51313DE550C0BB626467A05BEDF34B7120874BD5 (void);
// 0x00000118 System.Void Cinemachine.CinemachineMixingCamera::ValidateListOfChildren()
extern void CinemachineMixingCamera_ValidateListOfChildren_m57026964CD2CE933128EACC1EB1C2550316D938D (void);
// 0x00000119 System.Void Cinemachine.CinemachineMixingCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineMixingCamera_OnTransitionFromCamera_m276EA6CE8376BE27DDCF3EF7DF89BE4A492FF505 (void);
// 0x0000011A System.Void Cinemachine.CinemachineMixingCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineMixingCamera_InternalUpdateCameraState_mED94FAACA5FA2E23DFD1A925B176E69C6825C35D (void);
// 0x0000011B System.Void Cinemachine.CinemachineMixingCamera::.ctor()
extern void CinemachineMixingCamera__ctor_m0B1F309D2D31EB2803EB13DE6CAFABB66865E54C (void);
// 0x0000011C System.Single Cinemachine.CinemachinePath::get_MinPos()
extern void CinemachinePath_get_MinPos_mBA8D84B82E247993FE9B877B1AEC594E1E3D7058 (void);
// 0x0000011D System.Single Cinemachine.CinemachinePath::get_MaxPos()
extern void CinemachinePath_get_MaxPos_m83D806FD19414D91B55CF8D96CBF6408090CB1BD (void);
// 0x0000011E System.Boolean Cinemachine.CinemachinePath::get_Looped()
extern void CinemachinePath_get_Looped_mAF77015A018DD14449E5798FB2EECC608465F947 (void);
// 0x0000011F System.Void Cinemachine.CinemachinePath::Reset()
extern void CinemachinePath_Reset_m3D879DC9DC1148FDFB117998B7DF784D559D4241 (void);
// 0x00000120 System.Void Cinemachine.CinemachinePath::OnValidate()
extern void CinemachinePath_OnValidate_m09378574E3D52E520FF10A675E41753CEF20610B (void);
// 0x00000121 System.Int32 Cinemachine.CinemachinePath::get_DistanceCacheSampleStepsPerSegment()
extern void CinemachinePath_get_DistanceCacheSampleStepsPerSegment_m7B01348994DB96DD02D21782AA1EC501F1022248 (void);
// 0x00000122 System.Single Cinemachine.CinemachinePath::GetBoundingIndices(System.Single,System.Int32&,System.Int32&)
extern void CinemachinePath_GetBoundingIndices_m75531D6E4BE5271966BCBE5719272BDD52D7D31A (void);
// 0x00000123 UnityEngine.Vector3 Cinemachine.CinemachinePath::EvaluateLocalPosition(System.Single)
extern void CinemachinePath_EvaluateLocalPosition_mFA1EE82F5816919E5938FF33F05B85A831E0E470 (void);
// 0x00000124 UnityEngine.Vector3 Cinemachine.CinemachinePath::EvaluateLocalTangent(System.Single)
extern void CinemachinePath_EvaluateLocalTangent_mF16B5FCDFFE4FAF04E2F3BB061F6CD54E7A814E3 (void);
// 0x00000125 UnityEngine.Quaternion Cinemachine.CinemachinePath::EvaluateLocalOrientation(System.Single)
extern void CinemachinePath_EvaluateLocalOrientation_m9DD2FF1FCA7A124F47616F83E3349395E7EC2BA6 (void);
// 0x00000126 System.Single Cinemachine.CinemachinePath::GetRoll(System.Int32,System.Int32,System.Single)
extern void CinemachinePath_GetRoll_mF083C5544E3986F2E8BF9E2B2B264A46041B387E (void);
// 0x00000127 UnityEngine.Quaternion Cinemachine.CinemachinePath::RollAroundForward(System.Single)
extern void CinemachinePath_RollAroundForward_mAB9F97C90959DCC810F5A0373DED357E9FCAFBF3 (void);
// 0x00000128 System.Void Cinemachine.CinemachinePath::.ctor()
extern void CinemachinePath__ctor_m2AC66AF8FA31A0F8CA731812E8BF30FDFD33E90F (void);
// 0x00000129 System.Void Cinemachine.CinemachinePipeline::.ctor()
extern void CinemachinePipeline__ctor_m88F828229691C91F2DFF546084A80215324B9BF2 (void);
// 0x0000012A System.Void Cinemachine.CinemachinePixelPerfect::.ctor()
extern void CinemachinePixelPerfect__ctor_m4422D053361DF8CD626CB21B60EF4507600365B6 (void);
// 0x0000012B System.Single Cinemachine.CinemachineSmoothPath::get_MinPos()
extern void CinemachineSmoothPath_get_MinPos_m0EB8C61879F4C2829CDC4B29F5B9B87D6C142F2D (void);
// 0x0000012C System.Single Cinemachine.CinemachineSmoothPath::get_MaxPos()
extern void CinemachineSmoothPath_get_MaxPos_mD2A934FC5741EEF3E529BEAEB4D7D8ADC458B462 (void);
// 0x0000012D System.Boolean Cinemachine.CinemachineSmoothPath::get_Looped()
extern void CinemachineSmoothPath_get_Looped_mA3CAB564800423C040DF4685BDB585FEA28E069E (void);
// 0x0000012E System.Int32 Cinemachine.CinemachineSmoothPath::get_DistanceCacheSampleStepsPerSegment()
extern void CinemachineSmoothPath_get_DistanceCacheSampleStepsPerSegment_m8167360E02FD34C85F80758C7F7654A53294A239 (void);
// 0x0000012F System.Void Cinemachine.CinemachineSmoothPath::OnValidate()
extern void CinemachineSmoothPath_OnValidate_m8F7D11F9B015CCE4B6C1AB859AE8ACB0488FDACD (void);
// 0x00000130 System.Void Cinemachine.CinemachineSmoothPath::Reset()
extern void CinemachineSmoothPath_Reset_mD9A09F954797E3D506B9F44DA87ECD9AA6BD69E2 (void);
// 0x00000131 System.Void Cinemachine.CinemachineSmoothPath::InvalidateDistanceCache()
extern void CinemachineSmoothPath_InvalidateDistanceCache_mFB359B4FB36D7FA2636769A2091C9C63E9E929A1 (void);
// 0x00000132 System.Void Cinemachine.CinemachineSmoothPath::UpdateControlPoints()
extern void CinemachineSmoothPath_UpdateControlPoints_mBF744AB3B5D5E83B6DBC4769188A8D9615CFAF23 (void);
// 0x00000133 System.Single Cinemachine.CinemachineSmoothPath::GetBoundingIndices(System.Single,System.Int32&,System.Int32&)
extern void CinemachineSmoothPath_GetBoundingIndices_m5ABF4523E4FEAEC313961640BBD3776823458AA0 (void);
// 0x00000134 UnityEngine.Vector3 Cinemachine.CinemachineSmoothPath::EvaluateLocalPosition(System.Single)
extern void CinemachineSmoothPath_EvaluateLocalPosition_m56B739A27AF201BED9E37E3FE54E4E5057FED809 (void);
// 0x00000135 UnityEngine.Vector3 Cinemachine.CinemachineSmoothPath::EvaluateLocalTangent(System.Single)
extern void CinemachineSmoothPath_EvaluateLocalTangent_m4C16F43B9455B1496F9881369FE3A5A06413B0B7 (void);
// 0x00000136 UnityEngine.Quaternion Cinemachine.CinemachineSmoothPath::EvaluateLocalOrientation(System.Single)
extern void CinemachineSmoothPath_EvaluateLocalOrientation_m692FFED8959B97DE3C889F5FB2CD0D1CB505120E (void);
// 0x00000137 UnityEngine.Quaternion Cinemachine.CinemachineSmoothPath::RollAroundForward(System.Single)
extern void CinemachineSmoothPath_RollAroundForward_m793878EE596319781A69AED4641503616F6BD70A (void);
// 0x00000138 System.Void Cinemachine.CinemachineSmoothPath::.ctor()
extern void CinemachineSmoothPath__ctor_m803C3FD43A12B6D618F661D748EFB66D7D8D5CE9 (void);
// 0x00000139 UnityEngine.Vector4 Cinemachine.CinemachineSmoothPath/Waypoint::get_AsVector4()
extern void Waypoint_get_AsVector4_mA3935DAA3EBC69BEF2DDD1AA4DA077A0B520FA97 (void);
// 0x0000013A Cinemachine.CinemachineSmoothPath/Waypoint Cinemachine.CinemachineSmoothPath/Waypoint::FromVector4(UnityEngine.Vector4)
extern void Waypoint_FromVector4_m4BC6E89C093AE2B43B656C0B016927580FA0523A (void);
// 0x0000013B System.String Cinemachine.CinemachineStateDrivenCamera::get_Description()
extern void CinemachineStateDrivenCamera_get_Description_m7DAF9ACF241B7726B73AE384C54F69B9D544C898 (void);
// 0x0000013C Cinemachine.ICinemachineCamera Cinemachine.CinemachineStateDrivenCamera::get_LiveChild()
extern void CinemachineStateDrivenCamera_get_LiveChild_m516C9074D2AD76558E48C1D237B0C4598325EA21 (void);
// 0x0000013D System.Void Cinemachine.CinemachineStateDrivenCamera::set_LiveChild(Cinemachine.ICinemachineCamera)
extern void CinemachineStateDrivenCamera_set_LiveChild_mE87CE48A20127BE305780929D56EEA8824967D2B (void);
// 0x0000013E System.Boolean Cinemachine.CinemachineStateDrivenCamera::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineStateDrivenCamera_IsLiveChild_m5ED62A5DD53C1F2896B79CC1D92B66DF9A1035D8 (void);
// 0x0000013F Cinemachine.CameraState Cinemachine.CinemachineStateDrivenCamera::get_State()
extern void CinemachineStateDrivenCamera_get_State_m022C21ACA96103A7D7EDA5166BBE692E66B5C7C1 (void);
// 0x00000140 UnityEngine.Transform Cinemachine.CinemachineStateDrivenCamera::get_LookAt()
extern void CinemachineStateDrivenCamera_get_LookAt_m2EF675C4940B5DC5027AEE6FCE781E6616B7D12E (void);
// 0x00000141 System.Void Cinemachine.CinemachineStateDrivenCamera::set_LookAt(UnityEngine.Transform)
extern void CinemachineStateDrivenCamera_set_LookAt_m7288F2C8A0E2817CC2602A6D8C4CF47684828F1B (void);
// 0x00000142 UnityEngine.Transform Cinemachine.CinemachineStateDrivenCamera::get_Follow()
extern void CinemachineStateDrivenCamera_get_Follow_mEB2BEBF5DD846A0E8C9043878CA65756D994A31B (void);
// 0x00000143 System.Void Cinemachine.CinemachineStateDrivenCamera::set_Follow(UnityEngine.Transform)
extern void CinemachineStateDrivenCamera_set_Follow_m68B6BA289A4F82A12CDC5C90607E0B199929688C (void);
// 0x00000144 System.Void Cinemachine.CinemachineStateDrivenCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineStateDrivenCamera_OnTargetObjectWarped_m5A48D9430DE68881A8461861C6C328C409642B81 (void);
// 0x00000145 System.Void Cinemachine.CinemachineStateDrivenCamera::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineStateDrivenCamera_ForceCameraPosition_m8CD7FA67FC7F315DBA29E55B232BA0CF58D65766 (void);
// 0x00000146 System.Void Cinemachine.CinemachineStateDrivenCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineStateDrivenCamera_OnTransitionFromCamera_m0274DB41D0267BF7F98485C8237D3DA5DE33A9B0 (void);
// 0x00000147 System.Void Cinemachine.CinemachineStateDrivenCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineStateDrivenCamera_InternalUpdateCameraState_m228EA511EF58839BF7D568D5ABBFA798C66E7BC3 (void);
// 0x00000148 System.Void Cinemachine.CinemachineStateDrivenCamera::OnEnable()
extern void CinemachineStateDrivenCamera_OnEnable_m9A483CA25DF77C94320EBE910B68510CBF3BA028 (void);
// 0x00000149 System.Void Cinemachine.CinemachineStateDrivenCamera::OnDisable()
extern void CinemachineStateDrivenCamera_OnDisable_m59B90104BF9A354FB8E0AE420256DA254369372F (void);
// 0x0000014A System.Void Cinemachine.CinemachineStateDrivenCamera::OnTransformChildrenChanged()
extern void CinemachineStateDrivenCamera_OnTransformChildrenChanged_mD5BAF106BBAA3C3486F27E3033B7E239F9C64F4F (void);
// 0x0000014B System.Void Cinemachine.CinemachineStateDrivenCamera::OnGuiHandler()
extern void CinemachineStateDrivenCamera_OnGuiHandler_m3B5FB8E32B3DD5117D5CF526C60954FE3362C094 (void);
// 0x0000014C Cinemachine.CinemachineVirtualCameraBase[] Cinemachine.CinemachineStateDrivenCamera::get_ChildCameras()
extern void CinemachineStateDrivenCamera_get_ChildCameras_mD8D60F4EA1F55C19391664B0AAFB7C52069A5495 (void);
// 0x0000014D System.Boolean Cinemachine.CinemachineStateDrivenCamera::get_IsBlending()
extern void CinemachineStateDrivenCamera_get_IsBlending_mFCDE3B87EE0174789D932F463D24D14DF7439487 (void);
// 0x0000014E Cinemachine.CinemachineBlend Cinemachine.CinemachineStateDrivenCamera::get_ActiveBlend()
extern void CinemachineStateDrivenCamera_get_ActiveBlend_mBF5C9CD7F163E2B80AB71D95F698E8B8CF0BD551 (void);
// 0x0000014F System.Int32 Cinemachine.CinemachineStateDrivenCamera::CreateFakeHash(System.Int32,UnityEngine.AnimationClip)
extern void CinemachineStateDrivenCamera_CreateFakeHash_mAF4399836F328EB3C463A7B2A7770A5AB2139438 (void);
// 0x00000150 System.Int32 Cinemachine.CinemachineStateDrivenCamera::LookupFakeHash(System.Int32,UnityEngine.AnimationClip)
extern void CinemachineStateDrivenCamera_LookupFakeHash_mBF568073EFB952D5BA865BAE8276784223E1A0E6 (void);
// 0x00000151 System.Void Cinemachine.CinemachineStateDrivenCamera::InvalidateListOfChildren()
extern void CinemachineStateDrivenCamera_InvalidateListOfChildren_m07ED390483719F9053DB9117792E820F275B0FA0 (void);
// 0x00000152 System.Void Cinemachine.CinemachineStateDrivenCamera::UpdateListOfChildren()
extern void CinemachineStateDrivenCamera_UpdateListOfChildren_m6CC5B12DF863B54139D694443F28397107605FEE (void);
// 0x00000153 System.Void Cinemachine.CinemachineStateDrivenCamera::ValidateInstructions()
extern void CinemachineStateDrivenCamera_ValidateInstructions_mA7871412AAEBC15A8FA1C8773FEC36D353819FEB (void);
// 0x00000154 Cinemachine.CinemachineVirtualCameraBase Cinemachine.CinemachineStateDrivenCamera::ChooseCurrentCamera()
extern void CinemachineStateDrivenCamera_ChooseCurrentCamera_m903D1018B149F87E1D4A04BEC484B48167198C0F (void);
// 0x00000155 System.Int32 Cinemachine.CinemachineStateDrivenCamera::GetClipHash(System.Int32,System.Collections.Generic.List`1<UnityEngine.AnimatorClipInfo>)
extern void CinemachineStateDrivenCamera_GetClipHash_mB11C513BC9DB5F594476FC4344DE0CD9DB24CE43 (void);
// 0x00000156 Cinemachine.CinemachineBlendDefinition Cinemachine.CinemachineStateDrivenCamera::LookupBlend(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera)
extern void CinemachineStateDrivenCamera_LookupBlend_m93E3D457BD2927474EC7CCB819AD0066D68E20A0 (void);
// 0x00000157 System.Void Cinemachine.CinemachineStateDrivenCamera::.ctor()
extern void CinemachineStateDrivenCamera__ctor_mDD326FD6ABFBC350851FAF6B34E15AFB379E9301 (void);
// 0x00000158 System.Void Cinemachine.CinemachineStateDrivenCamera/ParentHash::.ctor(System.Int32,System.Int32)
extern void ParentHash__ctor_m6CD157CE916B29AC191604A0283607464CCC0DDD (void);
// 0x00000159 System.Void Cinemachine.CinemachineStoryboard::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineStoryboard_PostPipelineStageCallback_m4C51937C12AC6ECFD2B3F9EA412A70C67E9D43EE (void);
// 0x0000015A System.Void Cinemachine.CinemachineStoryboard::UpdateRenderCanvas()
extern void CinemachineStoryboard_UpdateRenderCanvas_m7353541228B6BD3B22EA978086F8733413B78984 (void);
// 0x0000015B System.Void Cinemachine.CinemachineStoryboard::ConnectToVcam(System.Boolean)
extern void CinemachineStoryboard_ConnectToVcam_m53BF8C555BCDBF2898BBEC9BAFA6A5FFF04FBF79 (void);
// 0x0000015C System.String Cinemachine.CinemachineStoryboard::get_CanvasName()
extern void CinemachineStoryboard_get_CanvasName_m677394CD1576D23EFA8E63B4CB5782B65BA44D00 (void);
// 0x0000015D System.Void Cinemachine.CinemachineStoryboard::CameraUpdatedCallback(Cinemachine.CinemachineBrain)
extern void CinemachineStoryboard_CameraUpdatedCallback_m96F15CE066264D8F7AB1C2046C416FAC345E234B (void);
// 0x0000015E Cinemachine.CinemachineStoryboard/CanvasInfo Cinemachine.CinemachineStoryboard::LocateMyCanvas(Cinemachine.CinemachineBrain,System.Boolean)
extern void CinemachineStoryboard_LocateMyCanvas_mAB2D252AAA3C187099D16446889579B184E8AABD (void);
// 0x0000015F System.Void Cinemachine.CinemachineStoryboard::CreateCanvas(Cinemachine.CinemachineStoryboard/CanvasInfo)
extern void CinemachineStoryboard_CreateCanvas_mE4DFBBDB8F034263F315169F6026BF4292026F6C (void);
// 0x00000160 System.Void Cinemachine.CinemachineStoryboard::DestroyCanvas()
extern void CinemachineStoryboard_DestroyCanvas_m809C169B130220231748965F4BFB3FBC51338FC2 (void);
// 0x00000161 System.Void Cinemachine.CinemachineStoryboard::PlaceImage(Cinemachine.CinemachineStoryboard/CanvasInfo,System.Single)
extern void CinemachineStoryboard_PlaceImage_mC8B2650D936D682C2C031D89FF260DCD4F92FBDB (void);
// 0x00000162 System.Void Cinemachine.CinemachineStoryboard::StaticBlendingHandler(Cinemachine.CinemachineBrain)
extern void CinemachineStoryboard_StaticBlendingHandler_mCB24F0CFB83E0D0E42A6BEE8EA45681FDA02336A (void);
// 0x00000163 System.Void Cinemachine.CinemachineStoryboard::InitializeModule()
extern void CinemachineStoryboard_InitializeModule_mB460164515FB890392CD51A78F9D6FC1029571F7 (void);
// 0x00000164 System.Void Cinemachine.CinemachineStoryboard::.ctor()
extern void CinemachineStoryboard__ctor_m13A6C495D5EE7EE171921D6A313B6D95083ED19E (void);
// 0x00000165 System.Void Cinemachine.CinemachineStoryboard/CanvasInfo::.ctor()
extern void CanvasInfo__ctor_m350FBA5D640FFCC2883C94DD425CBBA36DD30449 (void);
// 0x00000166 UnityEngine.Transform Cinemachine.ICinemachineTargetGroup::get_Transform()
// 0x00000167 UnityEngine.Bounds Cinemachine.ICinemachineTargetGroup::get_BoundingBox()
// 0x00000168 UnityEngine.BoundingSphere Cinemachine.ICinemachineTargetGroup::get_Sphere()
// 0x00000169 System.Boolean Cinemachine.ICinemachineTargetGroup::get_IsEmpty()
// 0x0000016A UnityEngine.Bounds Cinemachine.ICinemachineTargetGroup::GetViewSpaceBoundingBox(UnityEngine.Matrix4x4)
// 0x0000016B System.Void Cinemachine.ICinemachineTargetGroup::GetViewSpaceAngularBounds(UnityEngine.Matrix4x4,UnityEngine.Vector2&,UnityEngine.Vector2&,UnityEngine.Vector2&)
// 0x0000016C System.Void Cinemachine.CinemachineTargetGroup::OnValidate()
extern void CinemachineTargetGroup_OnValidate_m4F3F25B43C64B450CB8B2D2B493D969168B15805 (void);
// 0x0000016D System.Void Cinemachine.CinemachineTargetGroup::Reset()
extern void CinemachineTargetGroup_Reset_mFEEC71DBC62A2D72609BE4AAE08D8535BD86CD57 (void);
// 0x0000016E UnityEngine.Transform Cinemachine.CinemachineTargetGroup::get_Transform()
extern void CinemachineTargetGroup_get_Transform_mEE90686C94ECD8C321A1949D991608A1D6B3B4FA (void);
// 0x0000016F UnityEngine.Bounds Cinemachine.CinemachineTargetGroup::get_BoundingBox()
extern void CinemachineTargetGroup_get_BoundingBox_m9DAA8563350BEF0A06952DBCABCEB9C76A4192BF (void);
// 0x00000170 System.Void Cinemachine.CinemachineTargetGroup::set_BoundingBox(UnityEngine.Bounds)
extern void CinemachineTargetGroup_set_BoundingBox_m6A764BCB851DEB901428606CB4C6E35A422E40A9 (void);
// 0x00000171 UnityEngine.BoundingSphere Cinemachine.CinemachineTargetGroup::get_Sphere()
extern void CinemachineTargetGroup_get_Sphere_mF3A2B9159CB6C6D430B73BF4BB54259FFF077CD5 (void);
// 0x00000172 System.Void Cinemachine.CinemachineTargetGroup::set_Sphere(UnityEngine.BoundingSphere)
extern void CinemachineTargetGroup_set_Sphere_m7247E4BA3C06380A2A36EDD6871D5959E00D8F1B (void);
// 0x00000173 System.Boolean Cinemachine.CinemachineTargetGroup::get_IsEmpty()
extern void CinemachineTargetGroup_get_IsEmpty_m306811C02A8D1B747E2CECE27ED6C81779034BF8 (void);
// 0x00000174 System.Void Cinemachine.CinemachineTargetGroup::AddMember(UnityEngine.Transform,System.Single,System.Single)
extern void CinemachineTargetGroup_AddMember_m35E32FE8C1D431B8E9F9D926401426C712EAA157 (void);
// 0x00000175 System.Void Cinemachine.CinemachineTargetGroup::RemoveMember(UnityEngine.Transform)
extern void CinemachineTargetGroup_RemoveMember_mEE444661E6C4AE23B5E5632CE4F1C806CEDA3063 (void);
// 0x00000176 System.Int32 Cinemachine.CinemachineTargetGroup::FindMember(UnityEngine.Transform)
extern void CinemachineTargetGroup_FindMember_m763EA250DE5BFE070C2CA5751B99CAE734925683 (void);
// 0x00000177 UnityEngine.BoundingSphere Cinemachine.CinemachineTargetGroup::GetWeightedBoundsForMember(System.Int32)
extern void CinemachineTargetGroup_GetWeightedBoundsForMember_m66E0FB67C1A1A678A206DDA7869E48533264A185 (void);
// 0x00000178 UnityEngine.Bounds Cinemachine.CinemachineTargetGroup::GetViewSpaceBoundingBox(UnityEngine.Matrix4x4)
extern void CinemachineTargetGroup_GetViewSpaceBoundingBox_m0DEE6D2517C2D3A0030E2EEEC44A71A08B0275E2 (void);
// 0x00000179 System.Boolean Cinemachine.CinemachineTargetGroup::get_CachedCountIsValid()
extern void CinemachineTargetGroup_get_CachedCountIsValid_m954A5F1908D564DF36ECDB6A00285A6C30EB68BA (void);
// 0x0000017A System.Boolean Cinemachine.CinemachineTargetGroup::IndexIsValid(System.Int32)
extern void CinemachineTargetGroup_IndexIsValid_m1681AC168E9FE07272BCFEDD0C530597E98D56F3 (void);
// 0x0000017B UnityEngine.BoundingSphere Cinemachine.CinemachineTargetGroup::WeightedMemberBoundsForValidMember(Cinemachine.CinemachineTargetGroup/Target&,UnityEngine.Vector3,System.Single)
extern void CinemachineTargetGroup_WeightedMemberBoundsForValidMember_m733C92D75CDABB957D2F636E8AC7D928A4A0D63A (void);
// 0x0000017C System.Void Cinemachine.CinemachineTargetGroup::DoUpdate()
extern void CinemachineTargetGroup_DoUpdate_m9DD03ECB2BB8D0D2FF159EE022D977BCE8F60D7E (void);
// 0x0000017D System.Void Cinemachine.CinemachineTargetGroup::UpdateMemberValidity()
extern void CinemachineTargetGroup_UpdateMemberValidity_mB3DC6E7119136DEB47DF38BE9847D85A0945A173 (void);
// 0x0000017E UnityEngine.Vector3 Cinemachine.CinemachineTargetGroup::CalculateAveragePosition()
extern void CinemachineTargetGroup_CalculateAveragePosition_m53F12641C1D35197937BB9E57BD78B33A706B666 (void);
// 0x0000017F UnityEngine.Bounds Cinemachine.CinemachineTargetGroup::CalculateBoundingBox()
extern void CinemachineTargetGroup_CalculateBoundingBox_mFA34E575D2243D685DE7594EC4C1559F872C6F32 (void);
// 0x00000180 UnityEngine.BoundingSphere Cinemachine.CinemachineTargetGroup::CalculateBoundingSphere()
extern void CinemachineTargetGroup_CalculateBoundingSphere_m0BD49CCCE6992F00F263C15922CDEA35665E748D (void);
// 0x00000181 UnityEngine.Quaternion Cinemachine.CinemachineTargetGroup::CalculateAverageOrientation()
extern void CinemachineTargetGroup_CalculateAverageOrientation_m118DA64E33B831FF711D8A7CE25FC4E43AE02FA3 (void);
// 0x00000182 System.Void Cinemachine.CinemachineTargetGroup::FixedUpdate()
extern void CinemachineTargetGroup_FixedUpdate_mF769342842AD70E39A9FC717F2D240FB0726E2A4 (void);
// 0x00000183 System.Void Cinemachine.CinemachineTargetGroup::Update()
extern void CinemachineTargetGroup_Update_m724F76214501E5240D28C641F8661B84F4FD556D (void);
// 0x00000184 System.Void Cinemachine.CinemachineTargetGroup::LateUpdate()
extern void CinemachineTargetGroup_LateUpdate_m17DFCC2B7BB2F3EF24CB037253AB862A8C11D1E7 (void);
// 0x00000185 System.Void Cinemachine.CinemachineTargetGroup::GetViewSpaceAngularBounds(UnityEngine.Matrix4x4,UnityEngine.Vector2&,UnityEngine.Vector2&,UnityEngine.Vector2&)
extern void CinemachineTargetGroup_GetViewSpaceAngularBounds_m59037A85730646005B794AA40134C42CBA905421 (void);
// 0x00000186 System.Void Cinemachine.CinemachineTargetGroup::.ctor()
extern void CinemachineTargetGroup__ctor_m79E577054443EB393BF0D415CB21EB12EC87AF35 (void);
// 0x00000187 Cinemachine.CameraState Cinemachine.CinemachineVirtualCamera::get_State()
extern void CinemachineVirtualCamera_get_State_mD9A4B9A276A896CE61CF4A3175C0C50AFAAFA281 (void);
// 0x00000188 UnityEngine.Transform Cinemachine.CinemachineVirtualCamera::get_LookAt()
extern void CinemachineVirtualCamera_get_LookAt_mC71D170A52A0C7EBB8B512B41EEE9F67175440A8 (void);
// 0x00000189 System.Void Cinemachine.CinemachineVirtualCamera::set_LookAt(UnityEngine.Transform)
extern void CinemachineVirtualCamera_set_LookAt_mA999C37E7B4936DF8F7EDD13D48E568000B20212 (void);
// 0x0000018A UnityEngine.Transform Cinemachine.CinemachineVirtualCamera::get_Follow()
extern void CinemachineVirtualCamera_get_Follow_mE5A461072B1C46F33736F19CB47080FEB3A2008B (void);
// 0x0000018B System.Void Cinemachine.CinemachineVirtualCamera::set_Follow(UnityEngine.Transform)
extern void CinemachineVirtualCamera_set_Follow_m66CCC0DB5243B92AB976471EA1D2265E1D8B9011 (void);
// 0x0000018C System.Single Cinemachine.CinemachineVirtualCamera::GetMaxDampTime()
extern void CinemachineVirtualCamera_GetMaxDampTime_m059DBDCA5F7F954957F7FA5A86BAC04D81DB2079 (void);
// 0x0000018D System.Void Cinemachine.CinemachineVirtualCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCamera_InternalUpdateCameraState_m2158DF36FDDDCC24EE79D9CF019BBAAAE5445597 (void);
// 0x0000018E System.Void Cinemachine.CinemachineVirtualCamera::OnEnable()
extern void CinemachineVirtualCamera_OnEnable_m62FDA7C6D4F84F0514F758F0B1EF40D9B84F8AF4 (void);
// 0x0000018F System.Void Cinemachine.CinemachineVirtualCamera::OnDestroy()
extern void CinemachineVirtualCamera_OnDestroy_mAA93044AA45B8EE2242D23DABDBF77A727ACA0E6 (void);
// 0x00000190 System.Void Cinemachine.CinemachineVirtualCamera::OnValidate()
extern void CinemachineVirtualCamera_OnValidate_mD0D0029F130143E4B37AA96EA068A32FC5CB94C1 (void);
// 0x00000191 System.Void Cinemachine.CinemachineVirtualCamera::OnTransformChildrenChanged()
extern void CinemachineVirtualCamera_OnTransformChildrenChanged_mD904BF65BA30DF6149DD53BE8F3AC2F64DD6DA43 (void);
// 0x00000192 System.Void Cinemachine.CinemachineVirtualCamera::Reset()
extern void CinemachineVirtualCamera_Reset_mDD3327DD7A2EEA32324A1EE66AD1395825FC4D11 (void);
// 0x00000193 System.Void Cinemachine.CinemachineVirtualCamera::DestroyPipeline()
extern void CinemachineVirtualCamera_DestroyPipeline_mA8791466E5B9F0C01B2B79FC4587EBE54BFF408A (void);
// 0x00000194 UnityEngine.Transform Cinemachine.CinemachineVirtualCamera::CreatePipeline(Cinemachine.CinemachineVirtualCamera)
extern void CinemachineVirtualCamera_CreatePipeline_m2D6EBA76ABACA143FD32240A9F2EFA3E61112021 (void);
// 0x00000195 System.Void Cinemachine.CinemachineVirtualCamera::InvalidateComponentPipeline()
extern void CinemachineVirtualCamera_InvalidateComponentPipeline_mF6DF2D4F33B01A989AE3BD1CFAFC51BF35C97C4C (void);
// 0x00000196 UnityEngine.Transform Cinemachine.CinemachineVirtualCamera::GetComponentOwner()
extern void CinemachineVirtualCamera_GetComponentOwner_mB144AE753EBD703CCC5A868925DAC4A76B889DD7 (void);
// 0x00000197 Cinemachine.CinemachineComponentBase[] Cinemachine.CinemachineVirtualCamera::GetComponentPipeline()
extern void CinemachineVirtualCamera_GetComponentPipeline_m500E721EB58184151E203BF7BB83104586BE7923 (void);
// 0x00000198 Cinemachine.CinemachineComponentBase Cinemachine.CinemachineVirtualCamera::GetCinemachineComponent(Cinemachine.CinemachineCore/Stage)
extern void CinemachineVirtualCamera_GetCinemachineComponent_m26F31BE8B9E3891C9E889F884FCA94FF3B6A2C38 (void);
// 0x00000199 T Cinemachine.CinemachineVirtualCamera::GetCinemachineComponent()
// 0x0000019A T Cinemachine.CinemachineVirtualCamera::AddCinemachineComponent()
// 0x0000019B System.Void Cinemachine.CinemachineVirtualCamera::DestroyCinemachineComponent()
// 0x0000019C System.Void Cinemachine.CinemachineVirtualCamera::UpdateComponentPipeline()
extern void CinemachineVirtualCamera_UpdateComponentPipeline_mDC1BF9D6E71CA7CEFEB8539E13BAD7E12DAB11A1 (void);
// 0x0000019D System.Void Cinemachine.CinemachineVirtualCamera::SetFlagsForHiddenChild(UnityEngine.GameObject)
extern void CinemachineVirtualCamera_SetFlagsForHiddenChild_mE805AF7C54CF7D4517F230702B793D142F667C8B (void);
// 0x0000019E Cinemachine.CameraState Cinemachine.CinemachineVirtualCamera::CalculateNewState(UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCamera_CalculateNewState_mEAC1B5563F4C5BD1868E591DBE70EC359C27AE31 (void);
// 0x0000019F System.Void Cinemachine.CinemachineVirtualCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineVirtualCamera_OnTargetObjectWarped_m562D2944CECA26CF64C38DC4A59CA72CB67E033C (void);
// 0x000001A0 System.Void Cinemachine.CinemachineVirtualCamera::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineVirtualCamera_ForceCameraPosition_m24428AB9DFFF32E9A93DB9B9CCCE6E25B7637C1F (void);
// 0x000001A1 System.Void Cinemachine.CinemachineVirtualCamera::SetStateRawPosition(UnityEngine.Vector3)
extern void CinemachineVirtualCamera_SetStateRawPosition_m580BC430C8985CAD9750499F2E2597AA37F33623 (void);
// 0x000001A2 System.Void Cinemachine.CinemachineVirtualCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCamera_OnTransitionFromCamera_m8D77970ED45B1FF23674FB4090483AE87A5A62FC (void);
// 0x000001A3 System.Boolean Cinemachine.CinemachineVirtualCamera::RequiresUserInput()
extern void CinemachineVirtualCamera_RequiresUserInput_mB1354A1D7D4C6D4F7914127C3BFE8E2AF20C8763 (void);
// 0x000001A4 System.Void Cinemachine.CinemachineVirtualCamera::OnBeforeSerialize()
extern void CinemachineVirtualCamera_OnBeforeSerialize_mBA1994115C29336108E879C25AD723F8D9CD90A4 (void);
// 0x000001A5 System.Void Cinemachine.CinemachineVirtualCamera::.ctor()
extern void CinemachineVirtualCamera__ctor_m3D68CEFD2C0746D59587AA63E8DD2E436CAA008E (void);
// 0x000001A6 System.Void Cinemachine.CinemachineVirtualCamera/CreatePipelineDelegate::.ctor(System.Object,System.IntPtr)
extern void CreatePipelineDelegate__ctor_m1418B88041BF669A6692C2B815A8913C01EA7895 (void);
// 0x000001A7 UnityEngine.Transform Cinemachine.CinemachineVirtualCamera/CreatePipelineDelegate::Invoke(Cinemachine.CinemachineVirtualCamera,System.String,Cinemachine.CinemachineComponentBase[])
extern void CreatePipelineDelegate_Invoke_m64652CFF99A748B459CC4B834CE86FF147616188 (void);
// 0x000001A8 System.IAsyncResult Cinemachine.CinemachineVirtualCamera/CreatePipelineDelegate::BeginInvoke(Cinemachine.CinemachineVirtualCamera,System.String,Cinemachine.CinemachineComponentBase[],System.AsyncCallback,System.Object)
extern void CreatePipelineDelegate_BeginInvoke_mB61683EAF5CD48DD6AFE3080DBD1738BD0180656 (void);
// 0x000001A9 UnityEngine.Transform Cinemachine.CinemachineVirtualCamera/CreatePipelineDelegate::EndInvoke(System.IAsyncResult)
extern void CreatePipelineDelegate_EndInvoke_m5CAEF82C0E0E204CDFDEB4E6A99BAA098B0050B3 (void);
// 0x000001AA System.Void Cinemachine.CinemachineVirtualCamera/DestroyPipelineDelegate::.ctor(System.Object,System.IntPtr)
extern void DestroyPipelineDelegate__ctor_m33BC3713FE7D6659FDF1BB0BAF060F70032EBF60 (void);
// 0x000001AB System.Void Cinemachine.CinemachineVirtualCamera/DestroyPipelineDelegate::Invoke(UnityEngine.GameObject)
extern void DestroyPipelineDelegate_Invoke_mE4428F322828BD410B9C74A0358DF87D3A1983F9 (void);
// 0x000001AC System.IAsyncResult Cinemachine.CinemachineVirtualCamera/DestroyPipelineDelegate::BeginInvoke(UnityEngine.GameObject,System.AsyncCallback,System.Object)
extern void DestroyPipelineDelegate_BeginInvoke_m2902F0B20085FB754D94A373177CBE6D9E7E42E5 (void);
// 0x000001AD System.Void Cinemachine.CinemachineVirtualCamera/DestroyPipelineDelegate::EndInvoke(System.IAsyncResult)
extern void DestroyPipelineDelegate_EndInvoke_mBCC8462D17B7FDB6058C446202AAEBBDB9515D46 (void);
// 0x000001AE System.Void Cinemachine.CinemachineVirtualCamera/<>c::.cctor()
extern void U3CU3Ec__cctor_m1637EA016B94B271146109003881DB288843D058 (void);
// 0x000001AF System.Void Cinemachine.CinemachineVirtualCamera/<>c::.ctor()
extern void U3CU3Ec__ctor_m0BD6B99048AA4888057E840317CE80F3789BBE8D (void);
// 0x000001B0 System.Int32 Cinemachine.CinemachineVirtualCamera/<>c::<UpdateComponentPipeline>b__37_0(Cinemachine.CinemachineComponentBase,Cinemachine.CinemachineComponentBase)
extern void U3CU3Ec_U3CUpdateComponentPipelineU3Eb__37_0_mA4D694C9F9805040B7715B6A1FDD6B1A2D17F499 (void);
// 0x000001B1 System.Boolean Cinemachine.CinemachineVirtualCamera/<>c::<RequiresUserInput>b__46_0(Cinemachine.CinemachineComponentBase)
extern void U3CU3Ec_U3CRequiresUserInputU3Eb__46_0_mC341C64060ED18E061411546F35522CB18FA77A6 (void);
// 0x000001B2 System.Void Cinemachine.Cinemachine3rdPersonFollow::OnValidate()
extern void Cinemachine3rdPersonFollow_OnValidate_m34565B92F078C01A788E839FD887B50F4043CE84 (void);
// 0x000001B3 System.Void Cinemachine.Cinemachine3rdPersonFollow::Reset()
extern void Cinemachine3rdPersonFollow_Reset_m61AD95A9B447BCC760EDB5F6AF6EE9AFD23F065B (void);
// 0x000001B4 System.Void Cinemachine.Cinemachine3rdPersonFollow::OnDestroy()
extern void Cinemachine3rdPersonFollow_OnDestroy_mE3EBAEDC7F1108559BFB0207EAE6E3605DEAAEAF (void);
// 0x000001B5 System.Boolean Cinemachine.Cinemachine3rdPersonFollow::get_IsValid()
extern void Cinemachine3rdPersonFollow_get_IsValid_m3DA263484276CC7C240C2C3170966CB74597861B (void);
// 0x000001B6 Cinemachine.CinemachineCore/Stage Cinemachine.Cinemachine3rdPersonFollow::get_Stage()
extern void Cinemachine3rdPersonFollow_get_Stage_m8932622C583CBBDE9A4CF4614D622F76E0880CFC (void);
// 0x000001B7 System.Single Cinemachine.Cinemachine3rdPersonFollow::GetMaxDampTime()
extern void Cinemachine3rdPersonFollow_GetMaxDampTime_mE928D264574DE70999AB305FA793D028936D6BC2 (void);
// 0x000001B8 System.Void Cinemachine.Cinemachine3rdPersonFollow::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void Cinemachine3rdPersonFollow_MutateCameraState_mEE2F997F216076C29851BA224DD63B0CC3F47C42 (void);
// 0x000001B9 System.Void Cinemachine.Cinemachine3rdPersonFollow::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void Cinemachine3rdPersonFollow_OnTargetObjectWarped_mD0303D4A6D9EA4D13150C8C5E93AC843F04B0919 (void);
// 0x000001BA System.Void Cinemachine.Cinemachine3rdPersonFollow::PositionCamera(Cinemachine.CameraState&,System.Single)
extern void Cinemachine3rdPersonFollow_PositionCamera_m13334AE8E5681B0F83EB4DC65607CCDEBBE7BC4A (void);
// 0x000001BB System.Void Cinemachine.Cinemachine3rdPersonFollow::GetRigPositions(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Cinemachine3rdPersonFollow_GetRigPositions_m030DC36FC5FC04F030AAE5DD1DDE3C586F73C534 (void);
// 0x000001BC UnityEngine.Quaternion Cinemachine.Cinemachine3rdPersonFollow::GetHeading(UnityEngine.Quaternion,UnityEngine.Vector3)
extern void Cinemachine3rdPersonFollow_GetHeading_m4D3086385158A48B3E83FD7960931CF2EFD52535 (void);
// 0x000001BD System.Void Cinemachine.Cinemachine3rdPersonFollow::GetRawRigPositions(UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Quaternion,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Cinemachine3rdPersonFollow_GetRawRigPositions_mDE2296B2034978F905A1C9CBAA202EFB174CB1D5 (void);
// 0x000001BE UnityEngine.Vector3 Cinemachine.Cinemachine3rdPersonFollow::ResolveCollisions(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Single,System.Single&)
extern void Cinemachine3rdPersonFollow_ResolveCollisions_m0803F98237E6C6D08D13173E1FECBDD506860BA4 (void);
// 0x000001BF System.Void Cinemachine.Cinemachine3rdPersonFollow::.ctor()
extern void Cinemachine3rdPersonFollow__ctor_m239FDBE524C3BED33399C39822BBB7A63EFE95E9 (void);
// 0x000001C0 System.Boolean Cinemachine.CinemachineBasicMultiChannelPerlin::get_IsValid()
extern void CinemachineBasicMultiChannelPerlin_get_IsValid_m8330E1B15909306345A589EFD1BA1A9AA223E0F3 (void);
// 0x000001C1 Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineBasicMultiChannelPerlin::get_Stage()
extern void CinemachineBasicMultiChannelPerlin_get_Stage_mF7751E50B715EDFACEE2CE2A56999B0B3F40F024 (void);
// 0x000001C2 System.Void Cinemachine.CinemachineBasicMultiChannelPerlin::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineBasicMultiChannelPerlin_MutateCameraState_mD65925FD4858E9650DB79BFB8CBD75D0E2224CC4 (void);
// 0x000001C3 System.Void Cinemachine.CinemachineBasicMultiChannelPerlin::ReSeed()
extern void CinemachineBasicMultiChannelPerlin_ReSeed_m386B1BA6AEFB26B878A7431484D412F9FB2E9696 (void);
// 0x000001C4 System.Void Cinemachine.CinemachineBasicMultiChannelPerlin::Initialize()
extern void CinemachineBasicMultiChannelPerlin_Initialize_m1ADAFB3D2CAFBEBC0018D71B44BDCD24074EAEC2 (void);
// 0x000001C5 System.Void Cinemachine.CinemachineBasicMultiChannelPerlin::.ctor()
extern void CinemachineBasicMultiChannelPerlin__ctor_mEE8548285D383571E5AC2DB0740D8FD906BF8A50 (void);
// 0x000001C6 System.Boolean Cinemachine.CinemachineComposer::get_IsValid()
extern void CinemachineComposer_get_IsValid_mF1833F36F4B8823131C599CADEB5EE0A3CFCF062 (void);
// 0x000001C7 Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineComposer::get_Stage()
extern void CinemachineComposer_get_Stage_mF5E0C634D954BFFAB5A2EBFB4B4DA326D6853A6F (void);
// 0x000001C8 UnityEngine.Vector3 Cinemachine.CinemachineComposer::get_TrackedPoint()
extern void CinemachineComposer_get_TrackedPoint_m164861743F7BD7E49747B46076F228CBD8785F33 (void);
// 0x000001C9 System.Void Cinemachine.CinemachineComposer::set_TrackedPoint(UnityEngine.Vector3)
extern void CinemachineComposer_set_TrackedPoint_mC2806265609C1BADBE1F83DD18F800BDA064D5A6 (void);
// 0x000001CA UnityEngine.Vector3 Cinemachine.CinemachineComposer::GetLookAtPointAndSetTrackedPoint(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void CinemachineComposer_GetLookAtPointAndSetTrackedPoint_m5810F9F4FEC4860FE749CB7260E78E8BEE41E671 (void);
// 0x000001CB System.Void Cinemachine.CinemachineComposer::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineComposer_OnTargetObjectWarped_m14DFF01ED1173B5902E80C9A55AD2C1998481789 (void);
// 0x000001CC System.Void Cinemachine.CinemachineComposer::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineComposer_ForceCameraPosition_m190442A4F145C4B298B785DAE08EC8358B924B70 (void);
// 0x000001CD System.Single Cinemachine.CinemachineComposer::GetMaxDampTime()
extern void CinemachineComposer_GetMaxDampTime_m1D830B2C6BDB743F6C546C27AA62A60704BC4CA0 (void);
// 0x000001CE System.Void Cinemachine.CinemachineComposer::PrePipelineMutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineComposer_PrePipelineMutateCameraState_m6A2121831D76E0CD191FC63A7C63167AB917190B (void);
// 0x000001CF System.Void Cinemachine.CinemachineComposer::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineComposer_MutateCameraState_m50DD037C33A1BF4956C47F8ADA6F6CBADDDA4B3A (void);
// 0x000001D0 UnityEngine.Rect Cinemachine.CinemachineComposer::get_SoftGuideRect()
extern void CinemachineComposer_get_SoftGuideRect_mFFE86E73B085263B4B15F2E5BD8053F8C033E8E1 (void);
// 0x000001D1 System.Void Cinemachine.CinemachineComposer::set_SoftGuideRect(UnityEngine.Rect)
extern void CinemachineComposer_set_SoftGuideRect_mF24C9DED070606ED93AC69CC0F2AB72BB55A1ADA (void);
// 0x000001D2 UnityEngine.Rect Cinemachine.CinemachineComposer::get_HardGuideRect()
extern void CinemachineComposer_get_HardGuideRect_mA2B70FA82432B7D2874E5213E3F9086CC152E69F (void);
// 0x000001D3 System.Void Cinemachine.CinemachineComposer::set_HardGuideRect(UnityEngine.Rect)
extern void CinemachineComposer_set_HardGuideRect_m868567C4C94ED2BE86B092E4F69C548F98B167A5 (void);
// 0x000001D4 System.Void Cinemachine.CinemachineComposer::RotateToScreenBounds(Cinemachine.CameraState&,UnityEngine.Rect,UnityEngine.Vector3,UnityEngine.Quaternion&,System.Single,System.Single,System.Single)
extern void CinemachineComposer_RotateToScreenBounds_m01D1A38D82DF6AE50EFF13027781D15DED32D7EF (void);
// 0x000001D5 System.Boolean Cinemachine.CinemachineComposer::ClampVerticalBounds(UnityEngine.Rect&,UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void CinemachineComposer_ClampVerticalBounds_m65C191E116F577A8F7F1383C99875779254B934C (void);
// 0x000001D6 System.Void Cinemachine.CinemachineComposer::.ctor()
extern void CinemachineComposer__ctor_m90D1EE7F962886981F03D129849E4214A106DCD8 (void);
// 0x000001D7 System.Void Cinemachine.CinemachineComposer/FovCache::UpdateCache(Cinemachine.LensSettings,UnityEngine.Rect,UnityEngine.Rect,System.Single)
extern void FovCache_UpdateCache_m3462592E7672B43BEB32686E0F62B7C17F0E2999 (void);
// 0x000001D8 UnityEngine.Rect Cinemachine.CinemachineComposer/FovCache::ScreenToFOV(UnityEngine.Rect,System.Single,System.Single,System.Single)
extern void FovCache_ScreenToFOV_m84AEDE8D18A7CE6A911AB93E622316E126980056 (void);
// 0x000001D9 UnityEngine.Rect Cinemachine.CinemachineFramingTransposer::get_SoftGuideRect()
extern void CinemachineFramingTransposer_get_SoftGuideRect_mCDC60214B6A81FBD8AAF9F6DECAEC86A562C504A (void);
// 0x000001DA System.Void Cinemachine.CinemachineFramingTransposer::set_SoftGuideRect(UnityEngine.Rect)
extern void CinemachineFramingTransposer_set_SoftGuideRect_mEEE1DEC1C703C7C8D54A3C8388EB659E32B30F23 (void);
// 0x000001DB UnityEngine.Rect Cinemachine.CinemachineFramingTransposer::get_HardGuideRect()
extern void CinemachineFramingTransposer_get_HardGuideRect_m83469B076C3529941A2FD36E35FFE410EA3D7BA5 (void);
// 0x000001DC System.Void Cinemachine.CinemachineFramingTransposer::set_HardGuideRect(UnityEngine.Rect)
extern void CinemachineFramingTransposer_set_HardGuideRect_m215B19AF350146BA8E7C394D75EAD67C46BEF10E (void);
// 0x000001DD System.Void Cinemachine.CinemachineFramingTransposer::OnValidate()
extern void CinemachineFramingTransposer_OnValidate_m28F166F10297E84E587FC092E2E5DAB42A821AF8 (void);
// 0x000001DE System.Boolean Cinemachine.CinemachineFramingTransposer::get_IsValid()
extern void CinemachineFramingTransposer_get_IsValid_mDE0B8E801C5BDDA9643075A935B8FF10151C11CE (void);
// 0x000001DF Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineFramingTransposer::get_Stage()
extern void CinemachineFramingTransposer_get_Stage_m406D870FC51C1E3D0F463CD3F8124D4C13A78302 (void);
// 0x000001E0 System.Boolean Cinemachine.CinemachineFramingTransposer::get_BodyAppliesAfterAim()
extern void CinemachineFramingTransposer_get_BodyAppliesAfterAim_m29E5668CF169FFABBB9CEEB03E9D733EAE1C693B (void);
// 0x000001E1 UnityEngine.Vector3 Cinemachine.CinemachineFramingTransposer::get_TrackedPoint()
extern void CinemachineFramingTransposer_get_TrackedPoint_m893C86296D7D0C01FCD28D85D14B38124F9AFB52 (void);
// 0x000001E2 System.Void Cinemachine.CinemachineFramingTransposer::set_TrackedPoint(UnityEngine.Vector3)
extern void CinemachineFramingTransposer_set_TrackedPoint_m32FD1D5F85F4BDBFC3BF6DBF5CBC7A8D1DB44FDD (void);
// 0x000001E3 System.Void Cinemachine.CinemachineFramingTransposer::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineFramingTransposer_OnTargetObjectWarped_mAD4EE7D5CD54543EF73BF8D8DCD1781A57A6CCB4 (void);
// 0x000001E4 System.Void Cinemachine.CinemachineFramingTransposer::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineFramingTransposer_ForceCameraPosition_m7D0A6F764D394716B8F6700367A8F6DA53076546 (void);
// 0x000001E5 System.Single Cinemachine.CinemachineFramingTransposer::GetMaxDampTime()
extern void CinemachineFramingTransposer_GetMaxDampTime_m581B0A2F6493CCF2C3F1B0E68E8F0180EEB51B85 (void);
// 0x000001E6 System.Boolean Cinemachine.CinemachineFramingTransposer::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single,Cinemachine.CinemachineVirtualCameraBase/TransitionParams&)
extern void CinemachineFramingTransposer_OnTransitionFromCamera_m88B1E40E14D5612AE78AEFF401BC71442DF761AE (void);
// 0x000001E7 UnityEngine.Rect Cinemachine.CinemachineFramingTransposer::ScreenToOrtho(UnityEngine.Rect,System.Single,System.Single)
extern void CinemachineFramingTransposer_ScreenToOrtho_m07AF0DD2BFAEF10102EFEDBB9D87F31EAFA35D41 (void);
// 0x000001E8 UnityEngine.Vector3 Cinemachine.CinemachineFramingTransposer::OrthoOffsetToScreenBounds(UnityEngine.Vector3,UnityEngine.Rect)
extern void CinemachineFramingTransposer_OrthoOffsetToScreenBounds_mB27FBC07BF36E7BBACD39AAE05C8D7D3B62A8A4E (void);
// 0x000001E9 UnityEngine.Bounds Cinemachine.CinemachineFramingTransposer::get_LastBounds()
extern void CinemachineFramingTransposer_get_LastBounds_m6D98D46A49E2196A98E2B7E76C0061AC8310B45B (void);
// 0x000001EA System.Void Cinemachine.CinemachineFramingTransposer::set_LastBounds(UnityEngine.Bounds)
extern void CinemachineFramingTransposer_set_LastBounds_m42F030170155BAC06C2B040E44F4FCB25251EF93 (void);
// 0x000001EB UnityEngine.Matrix4x4 Cinemachine.CinemachineFramingTransposer::get_LastBoundsMatrix()
extern void CinemachineFramingTransposer_get_LastBoundsMatrix_mB1296133E5C0BDD6B9C0879888C468C559BE95BB (void);
// 0x000001EC System.Void Cinemachine.CinemachineFramingTransposer::set_LastBoundsMatrix(UnityEngine.Matrix4x4)
extern void CinemachineFramingTransposer_set_LastBoundsMatrix_m13FAE68552F3910750A134D22AE4AF6845C0301D (void);
// 0x000001ED System.Void Cinemachine.CinemachineFramingTransposer::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineFramingTransposer_MutateCameraState_mCF6C11F8E364980D95EFFEDCE1BDC11FD1877734 (void);
// 0x000001EE System.Single Cinemachine.CinemachineFramingTransposer::GetTargetHeight(UnityEngine.Vector2)
extern void CinemachineFramingTransposer_GetTargetHeight_m5CD0304B16E7442B6BA592E7915FE7C2F57D4A64 (void);
// 0x000001EF UnityEngine.Vector3 Cinemachine.CinemachineFramingTransposer::ComputeGroupBounds(Cinemachine.ICinemachineTargetGroup,Cinemachine.CameraState&)
extern void CinemachineFramingTransposer_ComputeGroupBounds_mD7044C4EFA049F1BD91607D7EB5FE2F26E7A78D2 (void);
// 0x000001F0 UnityEngine.Bounds Cinemachine.CinemachineFramingTransposer::GetScreenSpaceGroupBoundingBox(Cinemachine.ICinemachineTargetGroup,UnityEngine.Vector3&,UnityEngine.Quaternion)
extern void CinemachineFramingTransposer_GetScreenSpaceGroupBoundingBox_mFA7FD0E686444062F3A538809B553D27B1C292AF (void);
// 0x000001F1 System.Void Cinemachine.CinemachineFramingTransposer::.ctor()
extern void CinemachineFramingTransposer__ctor_mDC6AE4489F2CBA1B667DDE193E2C1D1C3D3332D5 (void);
// 0x000001F2 System.Void Cinemachine.CinemachineGroupComposer::OnValidate()
extern void CinemachineGroupComposer_OnValidate_m4F578A19AB48C00C385A8AB096DFD5E8C8991D77 (void);
// 0x000001F3 UnityEngine.Bounds Cinemachine.CinemachineGroupComposer::get_LastBounds()
extern void CinemachineGroupComposer_get_LastBounds_mC2ABA5C693EB4C5AC2676461601D5F9DC5615623 (void);
// 0x000001F4 System.Void Cinemachine.CinemachineGroupComposer::set_LastBounds(UnityEngine.Bounds)
extern void CinemachineGroupComposer_set_LastBounds_mE2FCF71321530F97627893A8BA652B959D19110C (void);
// 0x000001F5 UnityEngine.Matrix4x4 Cinemachine.CinemachineGroupComposer::get_LastBoundsMatrix()
extern void CinemachineGroupComposer_get_LastBoundsMatrix_m67F9243F621C6474E2090615DDE98B6E69B81E52 (void);
// 0x000001F6 System.Void Cinemachine.CinemachineGroupComposer::set_LastBoundsMatrix(UnityEngine.Matrix4x4)
extern void CinemachineGroupComposer_set_LastBoundsMatrix_m917FDDE19382BCDA1626CF4BB5E118E43C1D13A3 (void);
// 0x000001F7 System.Single Cinemachine.CinemachineGroupComposer::GetMaxDampTime()
extern void CinemachineGroupComposer_GetMaxDampTime_mED0FCE86105021DEFD27DC6546387EE1AEBEAFA0 (void);
// 0x000001F8 System.Void Cinemachine.CinemachineGroupComposer::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineGroupComposer_MutateCameraState_mBA96192C982AF7399B01AD3FCE14D48F6C27373A (void);
// 0x000001F9 System.Single Cinemachine.CinemachineGroupComposer::GetTargetHeight(UnityEngine.Vector2)
extern void CinemachineGroupComposer_GetTargetHeight_mE81E9435860ADF221E7DD164A4ADF411AB4C740A (void);
// 0x000001FA UnityEngine.Bounds Cinemachine.CinemachineGroupComposer::GetScreenSpaceGroupBoundingBox(Cinemachine.ICinemachineTargetGroup,UnityEngine.Matrix4x4,UnityEngine.Vector3&)
extern void CinemachineGroupComposer_GetScreenSpaceGroupBoundingBox_m23D17E79BBF0D8E56FFD0269BC4C5C8F33BD251A (void);
// 0x000001FB System.Void Cinemachine.CinemachineGroupComposer::.ctor()
extern void CinemachineGroupComposer__ctor_m971E14E2A389C00A5DB8E27648BC6143D96CDFAC (void);
// 0x000001FC System.Boolean Cinemachine.CinemachineHardLockToTarget::get_IsValid()
extern void CinemachineHardLockToTarget_get_IsValid_m3283683207CBE04A66BDE3CC3731D04AD4E11D7F (void);
// 0x000001FD Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineHardLockToTarget::get_Stage()
extern void CinemachineHardLockToTarget_get_Stage_m67CC2097CE1F227F1A4080108D68E8C9D6E21896 (void);
// 0x000001FE System.Single Cinemachine.CinemachineHardLockToTarget::GetMaxDampTime()
extern void CinemachineHardLockToTarget_GetMaxDampTime_mCC6C1B1C21332DD63B0CC7F435280080B0B76B70 (void);
// 0x000001FF System.Void Cinemachine.CinemachineHardLockToTarget::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineHardLockToTarget_MutateCameraState_m511263D61277FC8FF50CEE06B367F0B75CCA8D52 (void);
// 0x00000200 System.Void Cinemachine.CinemachineHardLockToTarget::.ctor()
extern void CinemachineHardLockToTarget__ctor_m905CAEB127D9D192FEAAA8F014F2C096F450F4C8 (void);
// 0x00000201 System.Boolean Cinemachine.CinemachineHardLookAt::get_IsValid()
extern void CinemachineHardLookAt_get_IsValid_m2281D65323EDD1BFA20E167912C262447A29D901 (void);
// 0x00000202 Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineHardLookAt::get_Stage()
extern void CinemachineHardLookAt_get_Stage_mC4BD7DB16560529621EEAEFFA65006A030832907 (void);
// 0x00000203 System.Void Cinemachine.CinemachineHardLookAt::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineHardLookAt_MutateCameraState_mE2A0FF4E4AE1F96D12E53368316B7F14ACD71E2B (void);
// 0x00000204 System.Void Cinemachine.CinemachineHardLookAt::.ctor()
extern void CinemachineHardLookAt__ctor_m673A99093725D7083A4C47B1C2328BB05647B70B (void);
// 0x00000205 System.Void Cinemachine.CinemachineOrbitalTransposer::OnValidate()
extern void CinemachineOrbitalTransposer_OnValidate_m3782FFE204D5E142643EC56897EE4EDD4CD91100 (void);
// 0x00000206 System.Single Cinemachine.CinemachineOrbitalTransposer::UpdateHeading(System.Single,UnityEngine.Vector3,Cinemachine.AxisState&)
extern void CinemachineOrbitalTransposer_UpdateHeading_m237761CC9CA559C83FA849BA7FB15661911A953A (void);
// 0x00000207 System.Single Cinemachine.CinemachineOrbitalTransposer::UpdateHeading(System.Single,UnityEngine.Vector3,Cinemachine.AxisState&,Cinemachine.AxisState/Recentering&,System.Boolean)
extern void CinemachineOrbitalTransposer_UpdateHeading_m8718BA600DA5134C0E38C8646DBC2506AB4472AB (void);
// 0x00000208 System.Void Cinemachine.CinemachineOrbitalTransposer::OnEnable()
extern void CinemachineOrbitalTransposer_OnEnable_m6C7E95C1EAE2BACB03E324BBE303DBFFE14CDAF6 (void);
// 0x00000209 System.Void Cinemachine.CinemachineOrbitalTransposer::UpdateInputAxisProvider()
extern void CinemachineOrbitalTransposer_UpdateInputAxisProvider_m2FA2059A198A20A0730E6BCAC2D572005513971D (void);
// 0x0000020A System.Void Cinemachine.CinemachineOrbitalTransposer::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineOrbitalTransposer_OnTargetObjectWarped_mE2BFEBB6D56EB26F27F01CBF307D1EBF4B060B5E (void);
// 0x0000020B System.Void Cinemachine.CinemachineOrbitalTransposer::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineOrbitalTransposer_ForceCameraPosition_m58355A8C31130A765A8D0B8E03CFFAC74A375195 (void);
// 0x0000020C System.Boolean Cinemachine.CinemachineOrbitalTransposer::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single,Cinemachine.CinemachineVirtualCameraBase/TransitionParams&)
extern void CinemachineOrbitalTransposer_OnTransitionFromCamera_m20B42EEE01538F55F944E042459D5FC87B6CC204 (void);
// 0x0000020D System.Single Cinemachine.CinemachineOrbitalTransposer::GetAxisClosestValue(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineOrbitalTransposer_GetAxisClosestValue_m12E53A2B675F5EF62F5FC89AD55A3F398C005AFF (void);
// 0x0000020E System.Void Cinemachine.CinemachineOrbitalTransposer::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineOrbitalTransposer_MutateCameraState_m1AB5EA636D64DC31FBC22AA18878307B645514C1 (void);
// 0x0000020F UnityEngine.Vector3 Cinemachine.CinemachineOrbitalTransposer::GetTargetCameraPosition(UnityEngine.Vector3)
extern void CinemachineOrbitalTransposer_GetTargetCameraPosition_m67992ACDFA01B5C8150D7AC9488086FABF473652 (void);
// 0x00000210 System.Boolean Cinemachine.CinemachineOrbitalTransposer::get_RequiresUserInput()
extern void CinemachineOrbitalTransposer_get_RequiresUserInput_m4B493CC95DFD622F1389A7C11ABD68041B216448 (void);
// 0x00000211 System.Single Cinemachine.CinemachineOrbitalTransposer::GetTargetHeading(System.Single,UnityEngine.Quaternion)
extern void CinemachineOrbitalTransposer_GetTargetHeading_m7CDCBC39F6AF29C82492EC52B529A3936CFD6219 (void);
// 0x00000212 System.Void Cinemachine.CinemachineOrbitalTransposer::.ctor()
extern void CinemachineOrbitalTransposer__ctor_m8BD1ED063A460BEF9B0A489B63769DA9CD1511FC (void);
// 0x00000213 System.Void Cinemachine.CinemachineOrbitalTransposer/Heading::.ctor(Cinemachine.CinemachineOrbitalTransposer/Heading/HeadingDefinition,System.Int32,System.Single)
extern void Heading__ctor_m8BA2E53862E9957B1942EF8A55E5C8284ACDAAAB (void);
// 0x00000214 System.Void Cinemachine.CinemachineOrbitalTransposer/UpdateHeadingDelegate::.ctor(System.Object,System.IntPtr)
extern void UpdateHeadingDelegate__ctor_m60911D320DFD3CDA2C31C8CC7E030A3B47EFF3F6 (void);
// 0x00000215 System.Single Cinemachine.CinemachineOrbitalTransposer/UpdateHeadingDelegate::Invoke(Cinemachine.CinemachineOrbitalTransposer,System.Single,UnityEngine.Vector3)
extern void UpdateHeadingDelegate_Invoke_mD63AFD811D3492ECF335D17B0B858E3655D8019A (void);
// 0x00000216 System.IAsyncResult Cinemachine.CinemachineOrbitalTransposer/UpdateHeadingDelegate::BeginInvoke(Cinemachine.CinemachineOrbitalTransposer,System.Single,UnityEngine.Vector3,System.AsyncCallback,System.Object)
extern void UpdateHeadingDelegate_BeginInvoke_mF9371D7AA17A9372F2FAB2891F8E66CA67FE5AAE (void);
// 0x00000217 System.Single Cinemachine.CinemachineOrbitalTransposer/UpdateHeadingDelegate::EndInvoke(System.IAsyncResult)
extern void UpdateHeadingDelegate_EndInvoke_mCF8E24E08925233FAA0FB6E5AFAFEFCF67FBE8CF (void);
// 0x00000218 System.Void Cinemachine.CinemachineOrbitalTransposer/<>c::.cctor()
extern void U3CU3Ec__cctor_mDB41F389E9ACDC6A49924D0DDF6BF908E627676B (void);
// 0x00000219 System.Void Cinemachine.CinemachineOrbitalTransposer/<>c::.ctor()
extern void U3CU3Ec__ctor_m86741AB1B49B0E3932CA01086C2B7FAFC221C361 (void);
// 0x0000021A System.Single Cinemachine.CinemachineOrbitalTransposer/<>c::<.ctor>b__30_0(Cinemachine.CinemachineOrbitalTransposer,System.Single,UnityEngine.Vector3)
extern void U3CU3Ec_U3C_ctorU3Eb__30_0_m9216ED998310150D666FF45C1BD6868BF4BF02DD (void);
// 0x0000021B System.Boolean Cinemachine.CinemachinePOV::get_IsValid()
extern void CinemachinePOV_get_IsValid_m05C868F4435523397654A39A1BF8593CF0F59ECF (void);
// 0x0000021C Cinemachine.CinemachineCore/Stage Cinemachine.CinemachinePOV::get_Stage()
extern void CinemachinePOV_get_Stage_mFE8B3BB72F863545A8347D3CA587EE97D9A9EA5D (void);
// 0x0000021D System.Void Cinemachine.CinemachinePOV::OnValidate()
extern void CinemachinePOV_OnValidate_m016AFFEFBEFECF40D1507E5AF33A6C0E1013D228 (void);
// 0x0000021E System.Void Cinemachine.CinemachinePOV::OnEnable()
extern void CinemachinePOV_OnEnable_m3A517E2080784B6C7A31A3227796D3B994FF647B (void);
// 0x0000021F System.Void Cinemachine.CinemachinePOV::UpdateInputAxisProvider()
extern void CinemachinePOV_UpdateInputAxisProvider_m061C1326E834985C26CA2D74F90D2E52C590FC4D (void);
// 0x00000220 System.Void Cinemachine.CinemachinePOV::PrePipelineMutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachinePOV_PrePipelineMutateCameraState_mBA43F716320C330EE8502DC1F49CD30512D8DF0B (void);
// 0x00000221 System.Void Cinemachine.CinemachinePOV::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachinePOV_MutateCameraState_m7D3F0F0979A4D487630A47A0BDB8B6C01F58A4EE (void);
// 0x00000222 UnityEngine.Vector2 Cinemachine.CinemachinePOV::GetRecenterTarget()
extern void CinemachinePOV_GetRecenterTarget_m222F334C80D4ABBD48B9284A6EFCF6C0B853460A (void);
// 0x00000223 System.Single Cinemachine.CinemachinePOV::NormalizeAngle(System.Single)
extern void CinemachinePOV_NormalizeAngle_m5A686B3609FF6019E1B4BBC07F1A374FD1B17B4A (void);
// 0x00000224 System.Void Cinemachine.CinemachinePOV::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachinePOV_ForceCameraPosition_m454958C55A58DD989A25D0443138AADBF608BB52 (void);
// 0x00000225 System.Boolean Cinemachine.CinemachinePOV::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single,Cinemachine.CinemachineVirtualCameraBase/TransitionParams&)
extern void CinemachinePOV_OnTransitionFromCamera_m491BDC05FF82D94CD9F0F5E381FABD26B836D32F (void);
// 0x00000226 System.Boolean Cinemachine.CinemachinePOV::get_RequiresUserInput()
extern void CinemachinePOV_get_RequiresUserInput_mF3866C5A3BF1A75C3EBF06998987999FC37A558B (void);
// 0x00000227 System.Void Cinemachine.CinemachinePOV::SetAxesForRotation(UnityEngine.Quaternion)
extern void CinemachinePOV_SetAxesForRotation_mDBC52583D2371432C6CE2DFE61689D7C906710BC (void);
// 0x00000228 System.Void Cinemachine.CinemachinePOV::.ctor()
extern void CinemachinePOV__ctor_m362B77E97F02F0B022F654161A5FA5120BD0DD17 (void);
// 0x00000229 System.Boolean Cinemachine.CinemachineSameAsFollowTarget::get_IsValid()
extern void CinemachineSameAsFollowTarget_get_IsValid_mC6D1503DFD8DC214605C36C1CAE502935D15BFEA (void);
// 0x0000022A Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineSameAsFollowTarget::get_Stage()
extern void CinemachineSameAsFollowTarget_get_Stage_mC807B568193EE1879B9A384DD9867E7FB1FFEA48 (void);
// 0x0000022B System.Single Cinemachine.CinemachineSameAsFollowTarget::GetMaxDampTime()
extern void CinemachineSameAsFollowTarget_GetMaxDampTime_m043AF23A9A983ECE05922C0472DB7FC1BF8542FB (void);
// 0x0000022C System.Void Cinemachine.CinemachineSameAsFollowTarget::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineSameAsFollowTarget_MutateCameraState_mE752F9E346D7A3380316A3446D5210988467B4F0 (void);
// 0x0000022D System.Void Cinemachine.CinemachineSameAsFollowTarget::.ctor()
extern void CinemachineSameAsFollowTarget__ctor_m4CB9434EC6F54E7AB3287343A9C5416C8079BD09 (void);
// 0x0000022E System.Boolean Cinemachine.CinemachineTrackedDolly::get_IsValid()
extern void CinemachineTrackedDolly_get_IsValid_m280F6EE398F406E248920DAEC9A2FB4C0A78CD20 (void);
// 0x0000022F Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineTrackedDolly::get_Stage()
extern void CinemachineTrackedDolly_get_Stage_m71C0F0AEBCCACD06836E5186C06F8E157DEB44D4 (void);
// 0x00000230 System.Single Cinemachine.CinemachineTrackedDolly::GetMaxDampTime()
extern void CinemachineTrackedDolly_GetMaxDampTime_m8387A78C47A4689A44BC60168DAD135BF2F14E20 (void);
// 0x00000231 System.Void Cinemachine.CinemachineTrackedDolly::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineTrackedDolly_MutateCameraState_m520EE451BF883F43059C628A4FEED072C6ACFECE (void);
// 0x00000232 UnityEngine.Quaternion Cinemachine.CinemachineTrackedDolly::GetCameraOrientationAtPathPoint(UnityEngine.Quaternion,UnityEngine.Vector3)
extern void CinemachineTrackedDolly_GetCameraOrientationAtPathPoint_m8F4DB6F44E986BE7FC8C2C55FCC1556995DB4D54 (void);
// 0x00000233 UnityEngine.Vector3 Cinemachine.CinemachineTrackedDolly::get_AngularDamping()
extern void CinemachineTrackedDolly_get_AngularDamping_m5ED59BCFD88587E5AF232BB5D779B3FE03832DE9 (void);
// 0x00000234 System.Void Cinemachine.CinemachineTrackedDolly::.ctor()
extern void CinemachineTrackedDolly__ctor_m632C7211074603AA91B9313A426A224C1E9490ED (void);
// 0x00000235 System.Void Cinemachine.CinemachineTrackedDolly/AutoDolly::.ctor(System.Boolean,System.Single,System.Int32,System.Int32)
extern void AutoDolly__ctor_m8DEA29EE4AE5C67F12B07FB0C51EEC0810FDDF20 (void);
// 0x00000236 System.Void Cinemachine.CinemachineTransposer::OnValidate()
extern void CinemachineTransposer_OnValidate_mFC57EE74F157499D7CAC4D30CC1D7A04ED6FC33E (void);
// 0x00000237 System.Boolean Cinemachine.CinemachineTransposer::get_HideOffsetInInspector()
extern void CinemachineTransposer_get_HideOffsetInInspector_mD7DBED85FE7830CDCD7BD3782022D88EC77F7774 (void);
// 0x00000238 System.Void Cinemachine.CinemachineTransposer::set_HideOffsetInInspector(System.Boolean)
extern void CinemachineTransposer_set_HideOffsetInInspector_m9D1049D2BCA245506F7768F1D1CDF53548FE528F (void);
// 0x00000239 UnityEngine.Vector3 Cinemachine.CinemachineTransposer::get_EffectiveOffset()
extern void CinemachineTransposer_get_EffectiveOffset_mF79BE447AD9A91A1829011B346B5AF18F6E1CE25 (void);
// 0x0000023A System.Boolean Cinemachine.CinemachineTransposer::get_IsValid()
extern void CinemachineTransposer_get_IsValid_m700545C70F86F2083F9FD2C1E97DC68FB8FC98C1 (void);
// 0x0000023B Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineTransposer::get_Stage()
extern void CinemachineTransposer_get_Stage_mAD7ABE84591669BA748174CDB9880821BB0A132C (void);
// 0x0000023C System.Single Cinemachine.CinemachineTransposer::GetMaxDampTime()
extern void CinemachineTransposer_GetMaxDampTime_m91977B2D8B63655ABA75BE4E9EFE6C68A0A5A094 (void);
// 0x0000023D System.Void Cinemachine.CinemachineTransposer::MutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineTransposer_MutateCameraState_m5B36F2ACE48727E2893C57FFEAD3162A6ECCAF65 (void);
// 0x0000023E System.Void Cinemachine.CinemachineTransposer::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineTransposer_OnTargetObjectWarped_m9E0D9DA06D752FF81CB08EDE999759FF47DEF741 (void);
// 0x0000023F System.Void Cinemachine.CinemachineTransposer::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineTransposer_ForceCameraPosition_m8E10E86DEDAF9FE53266FDB72F53E6D2083965B4 (void);
// 0x00000240 System.Void Cinemachine.CinemachineTransposer::InitPrevFrameStateInfo(Cinemachine.CameraState&,System.Single)
extern void CinemachineTransposer_InitPrevFrameStateInfo_m5640D1D85D4260B279D374618B009740EF6EC260 (void);
// 0x00000241 System.Void Cinemachine.CinemachineTransposer::TrackTarget(System.Single,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void CinemachineTransposer_TrackTarget_m509CF4F1D4319A21D55CEAA20802DA09B46E2AC5 (void);
// 0x00000242 UnityEngine.Vector3 Cinemachine.CinemachineTransposer::GetOffsetForMinimumTargetDistance(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineTransposer_GetOffsetForMinimumTargetDistance_m3AF6061743759E9C4BF3280862AA8841449A3172 (void);
// 0x00000243 UnityEngine.Vector3 Cinemachine.CinemachineTransposer::get_Damping()
extern void CinemachineTransposer_get_Damping_m0BD9EBB7534A2DB4AB31AEB2BBAC3DF1D01BF366 (void);
// 0x00000244 UnityEngine.Vector3 Cinemachine.CinemachineTransposer::get_AngularDamping()
extern void CinemachineTransposer_get_AngularDamping_m489A52D7C6AFD2B34710F4E97299EC2A18E5CDBE (void);
// 0x00000245 UnityEngine.Vector3 Cinemachine.CinemachineTransposer::GetTargetCameraPosition(UnityEngine.Vector3)
extern void CinemachineTransposer_GetTargetCameraPosition_m504AE0BA123B7A208257661232FF2A40AB408B92 (void);
// 0x00000246 UnityEngine.Quaternion Cinemachine.CinemachineTransposer::GetReferenceOrientation(UnityEngine.Vector3)
extern void CinemachineTransposer_GetReferenceOrientation_m3CBF0CBBB1639E68901C407E2A6A739D079915AE (void);
// 0x00000247 System.Void Cinemachine.CinemachineTransposer::.ctor()
extern void CinemachineTransposer__ctor_m66F1121D2339FDEDC9743EC432749AFB3CA846BC (void);
// 0x00000248 System.Void Cinemachine.AxisState::.ctor(System.Single,System.Single,System.Boolean,System.Boolean,System.Single,System.Single,System.Single,System.String,System.Boolean)
extern void AxisState__ctor_m09348C6ABBA887484BF7D3961D4FB582C0E5A4F6 (void);
// 0x00000249 System.Void Cinemachine.AxisState::Validate()
extern void AxisState_Validate_m1245D61F6D9A031C27F75F4B49E78A52AA91BDE5 (void);
// 0x0000024A System.Void Cinemachine.AxisState::Reset()
extern void AxisState_Reset_m329065EBC9963460CD7733144EC5F47D107967C9 (void);
// 0x0000024B System.Void Cinemachine.AxisState::SetInputAxisProvider(System.Int32,Cinemachine.AxisState/IInputAxisProvider)
extern void AxisState_SetInputAxisProvider_m9FBC0D9C885EDF31C4FFDA8A70029C5FC9089C85 (void);
// 0x0000024C System.Boolean Cinemachine.AxisState::get_HasInputProvider()
extern void AxisState_get_HasInputProvider_mD82DACE6E188BCFE1B0B5FCB1328BF8FA738B091 (void);
// 0x0000024D System.Boolean Cinemachine.AxisState::Update(System.Single)
extern void AxisState_Update_mE86F039B78105160E5C13153B456E3A988AF28B4 (void);
// 0x0000024E System.Single Cinemachine.AxisState::ClampValue(System.Single)
extern void AxisState_ClampValue_m2985D75E8FF57E3F88BF31B24CC719511507837F (void);
// 0x0000024F System.Boolean Cinemachine.AxisState::MaxSpeedUpdate(System.Single,System.Single)
extern void AxisState_MaxSpeedUpdate_m59BC1A91869A0D4A07E53DA4ED4172D5FBBF1DBD (void);
// 0x00000250 System.Single Cinemachine.AxisState::GetMaxSpeed()
extern void AxisState_GetMaxSpeed_m323DC3125D2C40B79B0C041CBE7F5F126329E489 (void);
// 0x00000251 System.Boolean Cinemachine.AxisState::get_ValueRangeLocked()
extern void AxisState_get_ValueRangeLocked_m25A67A9600BCC5AFD35CA1A2C57AE0CFCB76E6B1 (void);
// 0x00000252 System.Void Cinemachine.AxisState::set_ValueRangeLocked(System.Boolean)
extern void AxisState_set_ValueRangeLocked_m367AD65F7E97A0DFF0DE1CA0C74AEEBCCC36D000 (void);
// 0x00000253 System.Boolean Cinemachine.AxisState::get_HasRecentering()
extern void AxisState_get_HasRecentering_m24F7A4CEF751588924C04AAB32BD1B59389BA4DC (void);
// 0x00000254 System.Void Cinemachine.AxisState::set_HasRecentering(System.Boolean)
extern void AxisState_set_HasRecentering_m978B18A62A74813CC75078114997E708B6877D85 (void);
// 0x00000255 System.Single Cinemachine.AxisState/IInputAxisProvider::GetAxisValue(System.Int32)
// 0x00000256 System.Void Cinemachine.AxisState/Recentering::.ctor(System.Boolean,System.Single,System.Single)
extern void Recentering__ctor_mD885C396DC27C43D79A1FAA42F5ADD7D05CF2476 (void);
// 0x00000257 System.Void Cinemachine.AxisState/Recentering::Validate()
extern void Recentering_Validate_m3F5EE15AE52BB8FF2B69E3963851CEE2600340D3 (void);
// 0x00000258 System.Void Cinemachine.AxisState/Recentering::CopyStateFrom(Cinemachine.AxisState/Recentering&)
extern void Recentering_CopyStateFrom_m1DB1F919E2F17C4913D1F2605E71630004138D89 (void);
// 0x00000259 System.Void Cinemachine.AxisState/Recentering::CancelRecentering()
extern void Recentering_CancelRecentering_mB79FB4BE6A929EA524224E11C885AFBA1C212D90 (void);
// 0x0000025A System.Void Cinemachine.AxisState/Recentering::RecenterNow()
extern void Recentering_RecenterNow_m0A012C8E8ABA1B3D00765C8C0FDC3A96C3DB102C (void);
// 0x0000025B System.Void Cinemachine.AxisState/Recentering::DoRecentering(Cinemachine.AxisState&,System.Single,System.Single)
extern void Recentering_DoRecentering_m7B1730622484A958AF9FD87F2056A388D96EA01A (void);
// 0x0000025C System.Boolean Cinemachine.AxisState/Recentering::LegacyUpgrade(System.Int32&,System.Int32&)
extern void Recentering_LegacyUpgrade_m17A3ED97851377053B2385331ED85BE3DA3D4D7D (void);
// 0x0000025D System.Boolean Cinemachine.CameraState::get_HasLookAt()
extern void CameraState_get_HasLookAt_m2581CDE02E0998E65DF1AA58B170AAB84CBFD0AC (void);
// 0x0000025E UnityEngine.Vector3 Cinemachine.CameraState::get_CorrectedPosition()
extern void CameraState_get_CorrectedPosition_m2F96F0F6D3AE57BCEDE566FCE49D1488CA057089 (void);
// 0x0000025F UnityEngine.Quaternion Cinemachine.CameraState::get_CorrectedOrientation()
extern void CameraState_get_CorrectedOrientation_m04987B71E708B14A28973FFF81645C8834FD04E8 (void);
// 0x00000260 UnityEngine.Vector3 Cinemachine.CameraState::get_FinalPosition()
extern void CameraState_get_FinalPosition_m4D482D1F3E008068C2151FC24FD85CB6F603AE12 (void);
// 0x00000261 UnityEngine.Quaternion Cinemachine.CameraState::get_FinalOrientation()
extern void CameraState_get_FinalOrientation_m65D23E9A3C9264408AB177483C74FD609EFAB4B3 (void);
// 0x00000262 Cinemachine.CameraState Cinemachine.CameraState::get_Default()
extern void CameraState_get_Default_m21CC49BBB9A1FF0D582E3CEEC9C1F63C3F068DF8 (void);
// 0x00000263 System.Int32 Cinemachine.CameraState::get_NumCustomBlendables()
extern void CameraState_get_NumCustomBlendables_mA7FC428A3F135FA88769EC45E2C5521F2D1169DB (void);
// 0x00000264 System.Void Cinemachine.CameraState::set_NumCustomBlendables(System.Int32)
extern void CameraState_set_NumCustomBlendables_m599C74DAA99E17F8B5EF87CFD0A6238A81D05AD3 (void);
// 0x00000265 Cinemachine.CameraState/CustomBlendable Cinemachine.CameraState::GetCustomBlendable(System.Int32)
extern void CameraState_GetCustomBlendable_mE19B33F6CEC1B42ACAEB34A0601E48A80577498E (void);
// 0x00000266 System.Int32 Cinemachine.CameraState::FindCustomBlendable(UnityEngine.Object)
extern void CameraState_FindCustomBlendable_m141410A5E7FF4B985E2D3979D72BF80F398DE57C (void);
// 0x00000267 System.Void Cinemachine.CameraState::AddCustomBlendable(Cinemachine.CameraState/CustomBlendable)
extern void CameraState_AddCustomBlendable_m1DA24CB5A397752C33B6A1773CFF38F02505AD3C (void);
// 0x00000268 Cinemachine.CameraState Cinemachine.CameraState::Lerp(Cinemachine.CameraState,Cinemachine.CameraState,System.Single)
extern void CameraState_Lerp_m0BAAA9D6CC903E2656ACE05FDE2C930FE24D3E98 (void);
// 0x00000269 System.Single Cinemachine.CameraState::InterpolateFOV(System.Single,System.Single,System.Single,System.Single,System.Single)
extern void CameraState_InterpolateFOV_mB963D73A017B25550381245B5976BBE7DE77502F (void);
// 0x0000026A UnityEngine.Vector3 Cinemachine.CameraState::ApplyPosBlendHint(UnityEngine.Vector3,Cinemachine.CameraState/BlendHintValue,UnityEngine.Vector3,Cinemachine.CameraState/BlendHintValue,UnityEngine.Vector3,UnityEngine.Vector3)
extern void CameraState_ApplyPosBlendHint_m61F39F5911D34F49768E601B72C17CDBFFB26D1C (void);
// 0x0000026B UnityEngine.Quaternion Cinemachine.CameraState::ApplyRotBlendHint(UnityEngine.Quaternion,Cinemachine.CameraState/BlendHintValue,UnityEngine.Quaternion,Cinemachine.CameraState/BlendHintValue,UnityEngine.Quaternion,UnityEngine.Quaternion)
extern void CameraState_ApplyRotBlendHint_m25B6966D775F36A71929C4EA0404328034EBA09E (void);
// 0x0000026C UnityEngine.Vector3 Cinemachine.CameraState::InterpolatePosition(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void CameraState_InterpolatePosition_m0754A646434C49674356B584F9BDBB67B0D4F707 (void);
// 0x0000026D System.Void Cinemachine.CameraState::.cctor()
extern void CameraState__cctor_m53E682EB8F41BE2DA635516A8A6DCEF8EEF8DA0F (void);
// 0x0000026E System.Void Cinemachine.CameraState/CustomBlendable::.ctor(UnityEngine.Object,System.Single)
extern void CustomBlendable__ctor_mF38BF574AF05E415A01A2A46E506DE6B5086B303 (void);
// 0x0000026F System.Single Cinemachine.CinemachineBlend::get_BlendWeight()
extern void CinemachineBlend_get_BlendWeight_m0FFFD553C4A1176490E443AF34DC8AB87F0763A7 (void);
// 0x00000270 System.Boolean Cinemachine.CinemachineBlend::get_IsValid()
extern void CinemachineBlend_get_IsValid_m3C10BCF867EF0AA96AAF0A70FF0990808FB7C81C (void);
// 0x00000271 System.Boolean Cinemachine.CinemachineBlend::get_IsComplete()
extern void CinemachineBlend_get_IsComplete_m927128CEC49DCADF02A6258F8D636B0957446686 (void);
// 0x00000272 System.String Cinemachine.CinemachineBlend::get_Description()
extern void CinemachineBlend_get_Description_mC4378A79CCE5E2FF0FA5A175B6AB3DF7E6A6374C (void);
// 0x00000273 System.Boolean Cinemachine.CinemachineBlend::Uses(Cinemachine.ICinemachineCamera)
extern void CinemachineBlend_Uses_m7EC8B1160B3D24C5609684B486D485B2DD806A26 (void);
// 0x00000274 System.Void Cinemachine.CinemachineBlend::.ctor(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera,UnityEngine.AnimationCurve,System.Single,System.Single)
extern void CinemachineBlend__ctor_m36DEF2F2190A7392298D71CDC78C6A032FC8FC1D (void);
// 0x00000275 System.Void Cinemachine.CinemachineBlend::UpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineBlend_UpdateCameraState_m07AC58D1D550924255FC4B13BF6BBDC903B44493 (void);
// 0x00000276 Cinemachine.CameraState Cinemachine.CinemachineBlend::get_State()
extern void CinemachineBlend_get_State_m6667F2BD63E27F3A1FD5130CD23FA9CA11BA5DDC (void);
// 0x00000277 System.Single Cinemachine.CinemachineBlendDefinition::get_BlendTime()
extern void CinemachineBlendDefinition_get_BlendTime_m05485E3F00A40AD789C1A1C457153C8EEF13EF45 (void);
// 0x00000278 System.Void Cinemachine.CinemachineBlendDefinition::.ctor(Cinemachine.CinemachineBlendDefinition/Style,System.Single)
extern void CinemachineBlendDefinition__ctor_m24EFAC96EEDA53F43590F285C0B637771E6C947D (void);
// 0x00000279 System.Void Cinemachine.CinemachineBlendDefinition::CreateStandardCurves()
extern void CinemachineBlendDefinition_CreateStandardCurves_mC0C71CFA64286A2ED1DC190CFC2C9FAE37E9E2CF (void);
// 0x0000027A UnityEngine.AnimationCurve Cinemachine.CinemachineBlendDefinition::get_BlendCurve()
extern void CinemachineBlendDefinition_get_BlendCurve_mC33A778E56621A57C341B1882DE76D85CEBF82C0 (void);
// 0x0000027B System.Void Cinemachine.StaticPointVirtualCamera::.ctor(Cinemachine.CameraState,System.String)
extern void StaticPointVirtualCamera__ctor_m1F7B42796DD737014056877CA31EB5A1218A4112 (void);
// 0x0000027C System.Void Cinemachine.StaticPointVirtualCamera::SetState(Cinemachine.CameraState)
extern void StaticPointVirtualCamera_SetState_mDCA6D489E0E6B7811CF02DDB0B0E27C22BED4207 (void);
// 0x0000027D System.String Cinemachine.StaticPointVirtualCamera::get_Name()
extern void StaticPointVirtualCamera_get_Name_m747211FC8B4092AE0A9B06A94BF68E2427A15810 (void);
// 0x0000027E System.Void Cinemachine.StaticPointVirtualCamera::set_Name(System.String)
extern void StaticPointVirtualCamera_set_Name_m3894662EFE90B15664D4935B05CA90BF4EC5D530 (void);
// 0x0000027F System.String Cinemachine.StaticPointVirtualCamera::get_Description()
extern void StaticPointVirtualCamera_get_Description_m904B81566906FBE32DA4542531EEC6F7E1FD9FEA (void);
// 0x00000280 System.Int32 Cinemachine.StaticPointVirtualCamera::get_Priority()
extern void StaticPointVirtualCamera_get_Priority_mB670BDA879230CC102785CD19F621B796BB449A9 (void);
// 0x00000281 System.Void Cinemachine.StaticPointVirtualCamera::set_Priority(System.Int32)
extern void StaticPointVirtualCamera_set_Priority_m5EE3522D9AF59085624CD6D3530064088D4C9848 (void);
// 0x00000282 UnityEngine.Transform Cinemachine.StaticPointVirtualCamera::get_LookAt()
extern void StaticPointVirtualCamera_get_LookAt_m30E0403A476E774BDB4B19F78BFE56D95811307C (void);
// 0x00000283 System.Void Cinemachine.StaticPointVirtualCamera::set_LookAt(UnityEngine.Transform)
extern void StaticPointVirtualCamera_set_LookAt_m10EA988D83BCF4732C4FFE0FB4591A32827FD3EB (void);
// 0x00000284 UnityEngine.Transform Cinemachine.StaticPointVirtualCamera::get_Follow()
extern void StaticPointVirtualCamera_get_Follow_m20B11900677A6D382CDF3F43AC9BCCEDB5DF9914 (void);
// 0x00000285 System.Void Cinemachine.StaticPointVirtualCamera::set_Follow(UnityEngine.Transform)
extern void StaticPointVirtualCamera_set_Follow_m63241367CE4DCB42050FBB0399C9BBBEEC40509D (void);
// 0x00000286 Cinemachine.CameraState Cinemachine.StaticPointVirtualCamera::get_State()
extern void StaticPointVirtualCamera_get_State_m8E219127A5A4308451758CACE2920C0641729419 (void);
// 0x00000287 System.Void Cinemachine.StaticPointVirtualCamera::set_State(Cinemachine.CameraState)
extern void StaticPointVirtualCamera_set_State_mF717B76100CB091E5B0B5A182D8AED92043F5B1E (void);
// 0x00000288 UnityEngine.GameObject Cinemachine.StaticPointVirtualCamera::get_VirtualCameraGameObject()
extern void StaticPointVirtualCamera_get_VirtualCameraGameObject_mCE0937D2A4C7F11ABEEEAFAF3E61BDC4B7D94726 (void);
// 0x00000289 System.Boolean Cinemachine.StaticPointVirtualCamera::get_IsValid()
extern void StaticPointVirtualCamera_get_IsValid_mDCEB5041AF5D5AFE66C6DAC9CC660494EEFC468F (void);
// 0x0000028A Cinemachine.ICinemachineCamera Cinemachine.StaticPointVirtualCamera::get_ParentCamera()
extern void StaticPointVirtualCamera_get_ParentCamera_m96007C431649F6BC9C26EB8963F3888A567F182F (void);
// 0x0000028B System.Boolean Cinemachine.StaticPointVirtualCamera::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void StaticPointVirtualCamera_IsLiveChild_mE43BF024220758FDAB88C4C479E4BC0B40AA971D (void);
// 0x0000028C System.Void Cinemachine.StaticPointVirtualCamera::UpdateCameraState(UnityEngine.Vector3,System.Single)
extern void StaticPointVirtualCamera_UpdateCameraState_m63CB470789BF6A60C8764B6B1E11895D22E60D94 (void);
// 0x0000028D System.Void Cinemachine.StaticPointVirtualCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void StaticPointVirtualCamera_InternalUpdateCameraState_m54647D9C3B4FDA845188A9B53B059CF9415557C7 (void);
// 0x0000028E System.Void Cinemachine.StaticPointVirtualCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void StaticPointVirtualCamera_OnTransitionFromCamera_mA48D18E6527B557E81D69175371AC3A37915B02C (void);
// 0x0000028F System.Void Cinemachine.StaticPointVirtualCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void StaticPointVirtualCamera_OnTargetObjectWarped_m6E8C515C93A349DE5598C2FE5D06B26E335A1DF7 (void);
// 0x00000290 System.Void Cinemachine.BlendSourceVirtualCamera::.ctor(Cinemachine.CinemachineBlend)
extern void BlendSourceVirtualCamera__ctor_m260866EC0863C4B16331D7539AA3852E90DEEC5C (void);
// 0x00000291 Cinemachine.CinemachineBlend Cinemachine.BlendSourceVirtualCamera::get_Blend()
extern void BlendSourceVirtualCamera_get_Blend_mAEA739F5A13237AF89E38325902ECA8316FC5719 (void);
// 0x00000292 System.Void Cinemachine.BlendSourceVirtualCamera::set_Blend(Cinemachine.CinemachineBlend)
extern void BlendSourceVirtualCamera_set_Blend_m08A91575E7F63681916FD0FDB9093DE271A71AFA (void);
// 0x00000293 System.String Cinemachine.BlendSourceVirtualCamera::get_Name()
extern void BlendSourceVirtualCamera_get_Name_m0CCAC6597DBED7F0C14E55AE6BD3AE1C0FFC4EBA (void);
// 0x00000294 System.String Cinemachine.BlendSourceVirtualCamera::get_Description()
extern void BlendSourceVirtualCamera_get_Description_m66034400A8D85B99EC18607B71A094C0242E072A (void);
// 0x00000295 System.Int32 Cinemachine.BlendSourceVirtualCamera::get_Priority()
extern void BlendSourceVirtualCamera_get_Priority_m965C7A7F7D4DB371F4008904E49F66AF3BDE8636 (void);
// 0x00000296 System.Void Cinemachine.BlendSourceVirtualCamera::set_Priority(System.Int32)
extern void BlendSourceVirtualCamera_set_Priority_m5D5784B3554D81A89AB942149CBD0A6DA8141A9A (void);
// 0x00000297 UnityEngine.Transform Cinemachine.BlendSourceVirtualCamera::get_LookAt()
extern void BlendSourceVirtualCamera_get_LookAt_m00F5A7F9583090E8C08E44297E4DCDA38D10FC84 (void);
// 0x00000298 System.Void Cinemachine.BlendSourceVirtualCamera::set_LookAt(UnityEngine.Transform)
extern void BlendSourceVirtualCamera_set_LookAt_m8FAD0D9F6A9EBB41AD2DB77A298EFFB12D7DDF79 (void);
// 0x00000299 UnityEngine.Transform Cinemachine.BlendSourceVirtualCamera::get_Follow()
extern void BlendSourceVirtualCamera_get_Follow_m0C785D15EAC52228AEC14483B5AEA51A34CE9ED2 (void);
// 0x0000029A System.Void Cinemachine.BlendSourceVirtualCamera::set_Follow(UnityEngine.Transform)
extern void BlendSourceVirtualCamera_set_Follow_mEA0CDA042703FCCDF0B8D1EBFB44850417EED799 (void);
// 0x0000029B Cinemachine.CameraState Cinemachine.BlendSourceVirtualCamera::get_State()
extern void BlendSourceVirtualCamera_get_State_m4317D28A3F0EB829B1A008C0BD453A0E25C41547 (void);
// 0x0000029C System.Void Cinemachine.BlendSourceVirtualCamera::set_State(Cinemachine.CameraState)
extern void BlendSourceVirtualCamera_set_State_mD9C8228DBCC8AFB62BD349ADF5FE0CF4449AC52E (void);
// 0x0000029D UnityEngine.GameObject Cinemachine.BlendSourceVirtualCamera::get_VirtualCameraGameObject()
extern void BlendSourceVirtualCamera_get_VirtualCameraGameObject_mF259156E6AB334749619FB1C2201DCD2169277C3 (void);
// 0x0000029E System.Boolean Cinemachine.BlendSourceVirtualCamera::get_IsValid()
extern void BlendSourceVirtualCamera_get_IsValid_m7ABB6F9CC7D4BBF3605B4585E2B0F0B156F0401D (void);
// 0x0000029F Cinemachine.ICinemachineCamera Cinemachine.BlendSourceVirtualCamera::get_ParentCamera()
extern void BlendSourceVirtualCamera_get_ParentCamera_mEEBA9CBE7E371DFCF07F1E3CE4B3A4113E4E894A (void);
// 0x000002A0 System.Boolean Cinemachine.BlendSourceVirtualCamera::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void BlendSourceVirtualCamera_IsLiveChild_mEC6D0D9A7823969F0045B77FA84B6E04F798B952 (void);
// 0x000002A1 Cinemachine.CameraState Cinemachine.BlendSourceVirtualCamera::CalculateNewState(System.Single)
extern void BlendSourceVirtualCamera_CalculateNewState_mA5A2FB2600449ADD557442807A24B098D7D1C249 (void);
// 0x000002A2 System.Void Cinemachine.BlendSourceVirtualCamera::UpdateCameraState(UnityEngine.Vector3,System.Single)
extern void BlendSourceVirtualCamera_UpdateCameraState_mD67B44073F7EA49860018B880F79EF829180BFCC (void);
// 0x000002A3 System.Void Cinemachine.BlendSourceVirtualCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
extern void BlendSourceVirtualCamera_InternalUpdateCameraState_mEDE2337FEDCC7987D211F3738B491402F3BC3873 (void);
// 0x000002A4 System.Void Cinemachine.BlendSourceVirtualCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void BlendSourceVirtualCamera_OnTransitionFromCamera_m750F9688DA0DFBF1640E3F90D367D56490166523 (void);
// 0x000002A5 System.Void Cinemachine.BlendSourceVirtualCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void BlendSourceVirtualCamera_OnTargetObjectWarped_m3107B7DAB47DE606823928F9A57DD9F13CE2A766 (void);
// 0x000002A6 Cinemachine.CinemachineBlendDefinition Cinemachine.CinemachineBlenderSettings::GetBlendForVirtualCameras(System.String,System.String,Cinemachine.CinemachineBlendDefinition)
extern void CinemachineBlenderSettings_GetBlendForVirtualCameras_mD1C44B6D389EA35A1AC08F3BA4DEDC1D185070D5 (void);
// 0x000002A7 System.Void Cinemachine.CinemachineBlenderSettings::.ctor()
extern void CinemachineBlenderSettings__ctor_mCD4EFC8540B918D3884B62BD5AB63AAF86433F7A (void);
// 0x000002A8 Cinemachine.CinemachineVirtualCameraBase Cinemachine.CinemachineComponentBase::get_VirtualCamera()
extern void CinemachineComponentBase_get_VirtualCamera_mB83A44E630B22D8CD9A75521079ABC1691120223 (void);
// 0x000002A9 UnityEngine.Transform Cinemachine.CinemachineComponentBase::get_FollowTarget()
extern void CinemachineComponentBase_get_FollowTarget_m656475012F330FF1C680CD7E62C81D2E7EC4AB74 (void);
// 0x000002AA UnityEngine.Transform Cinemachine.CinemachineComponentBase::get_LookAtTarget()
extern void CinemachineComponentBase_get_LookAtTarget_m7E6CF239A3905B1130A5C38B0E5668EB32D1BB04 (void);
// 0x000002AB Cinemachine.ICinemachineTargetGroup Cinemachine.CinemachineComponentBase::get_AbstractFollowTargetGroup()
extern void CinemachineComponentBase_get_AbstractFollowTargetGroup_m91BD623311234A96B2D146A8AB6574567C8C9714 (void);
// 0x000002AC Cinemachine.CinemachineTargetGroup Cinemachine.CinemachineComponentBase::get_FollowTargetGroup()
extern void CinemachineComponentBase_get_FollowTargetGroup_mE756D61F3DC852F90E1292620499B5367F810A31 (void);
// 0x000002AD UnityEngine.Vector3 Cinemachine.CinemachineComponentBase::get_FollowTargetPosition()
extern void CinemachineComponentBase_get_FollowTargetPosition_m1039B11144B61D09459CACDA7A7E38626A601CC2 (void);
// 0x000002AE UnityEngine.Quaternion Cinemachine.CinemachineComponentBase::get_FollowTargetRotation()
extern void CinemachineComponentBase_get_FollowTargetRotation_m9C7A5F1A91CCBC93B69F934060F9D4C08FA547F3 (void);
// 0x000002AF Cinemachine.ICinemachineTargetGroup Cinemachine.CinemachineComponentBase::get_AbstractLookAtTargetGroup()
extern void CinemachineComponentBase_get_AbstractLookAtTargetGroup_m83547AD312D71E3080F9C6948DF4C5DA7B6B6054 (void);
// 0x000002B0 Cinemachine.CinemachineTargetGroup Cinemachine.CinemachineComponentBase::get_LookAtTargetGroup()
extern void CinemachineComponentBase_get_LookAtTargetGroup_mC3A9F692727989A9E76109FB2C250E934A641BEC (void);
// 0x000002B1 UnityEngine.Vector3 Cinemachine.CinemachineComponentBase::get_LookAtTargetPosition()
extern void CinemachineComponentBase_get_LookAtTargetPosition_m79CE45A7F4D4A82BC47B01434F5EB35C91DC99A8 (void);
// 0x000002B2 UnityEngine.Quaternion Cinemachine.CinemachineComponentBase::get_LookAtTargetRotation()
extern void CinemachineComponentBase_get_LookAtTargetRotation_m49CBE00226BB55772DB73775412AF782892B8251 (void);
// 0x000002B3 Cinemachine.CameraState Cinemachine.CinemachineComponentBase::get_VcamState()
extern void CinemachineComponentBase_get_VcamState_m17C5F4CFD04B41EA7559216C8C50CB980140D9A2 (void);
// 0x000002B4 System.Boolean Cinemachine.CinemachineComponentBase::get_IsValid()
// 0x000002B5 System.Void Cinemachine.CinemachineComponentBase::PrePipelineMutateCameraState(Cinemachine.CameraState&,System.Single)
extern void CinemachineComponentBase_PrePipelineMutateCameraState_m05A1AC832D6F7FD16DE67103E8CB0535950BB79C (void);
// 0x000002B6 Cinemachine.CinemachineCore/Stage Cinemachine.CinemachineComponentBase::get_Stage()
// 0x000002B7 System.Boolean Cinemachine.CinemachineComponentBase::get_BodyAppliesAfterAim()
extern void CinemachineComponentBase_get_BodyAppliesAfterAim_mB9687B7FEFB9827154DE70F8139BB52D84B2B0D2 (void);
// 0x000002B8 System.Void Cinemachine.CinemachineComponentBase::MutateCameraState(Cinemachine.CameraState&,System.Single)
// 0x000002B9 System.Boolean Cinemachine.CinemachineComponentBase::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single,Cinemachine.CinemachineVirtualCameraBase/TransitionParams&)
extern void CinemachineComponentBase_OnTransitionFromCamera_m6FCB5461D89C12185703C5701848413DFB99CD56 (void);
// 0x000002BA System.Void Cinemachine.CinemachineComponentBase::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineComponentBase_OnTargetObjectWarped_m3E083DBF03C47860948F0BB3A013B241AFDAF9A0 (void);
// 0x000002BB System.Void Cinemachine.CinemachineComponentBase::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineComponentBase_ForceCameraPosition_m3D22002EC0B4F5C1AF7CC283C00BA43D22120878 (void);
// 0x000002BC System.Single Cinemachine.CinemachineComponentBase::GetMaxDampTime()
extern void CinemachineComponentBase_GetMaxDampTime_mA2159FA6C923F49F9729286A70C304298440B060 (void);
// 0x000002BD System.Boolean Cinemachine.CinemachineComponentBase::get_RequiresUserInput()
extern void CinemachineComponentBase_get_RequiresUserInput_m45F5AFF5AB81E3E6562E2A5F624768A73E6A7170 (void);
// 0x000002BE System.Void Cinemachine.CinemachineComponentBase::.ctor()
extern void CinemachineComponentBase__ctor_mFA2A3C88B75CD71B7F359220C38B253AC1353B19 (void);
// 0x000002BF Cinemachine.CinemachineCore Cinemachine.CinemachineCore::get_Instance()
extern void CinemachineCore_get_Instance_m437A8089CC851778BA1ABABA3041B24B8D8B7E9B (void);
// 0x000002C0 System.Single Cinemachine.CinemachineCore::get_DeltaTime()
extern void CinemachineCore_get_DeltaTime_m0ED50D97C4B5327468BFA2A426E4F8B2F4078F48 (void);
// 0x000002C1 System.Single Cinemachine.CinemachineCore::get_CurrentTime()
extern void CinemachineCore_get_CurrentTime_mFA05B621BE52910A8C3A304CB715257011C9A782 (void);
// 0x000002C2 System.Int32 Cinemachine.CinemachineCore::get_BrainCount()
extern void CinemachineCore_get_BrainCount_m464F67F700D9EF3D2F486C14C215578AFC8BE080 (void);
// 0x000002C3 Cinemachine.CinemachineBrain Cinemachine.CinemachineCore::GetActiveBrain(System.Int32)
extern void CinemachineCore_GetActiveBrain_m0CA11E913913E3A09CFE0C93C612C98936D480D8 (void);
// 0x000002C4 System.Void Cinemachine.CinemachineCore::AddActiveBrain(Cinemachine.CinemachineBrain)
extern void CinemachineCore_AddActiveBrain_m48DD0D8000E2EE00E9C4BF642F5CDF04EF5AB819 (void);
// 0x000002C5 System.Void Cinemachine.CinemachineCore::RemoveActiveBrain(Cinemachine.CinemachineBrain)
extern void CinemachineCore_RemoveActiveBrain_m20CECA6E425E777D991B9AF73A691D05B412EF3E (void);
// 0x000002C6 System.Int32 Cinemachine.CinemachineCore::get_VirtualCameraCount()
extern void CinemachineCore_get_VirtualCameraCount_m991909A066AD9BEE7B18512F6F2CB6B36F2ED86A (void);
// 0x000002C7 Cinemachine.CinemachineVirtualCameraBase Cinemachine.CinemachineCore::GetVirtualCamera(System.Int32)
extern void CinemachineCore_GetVirtualCamera_m4D2512A0C993348EFC8A76729802C8BBF6209F0B (void);
// 0x000002C8 System.Void Cinemachine.CinemachineCore::AddActiveCamera(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_AddActiveCamera_m80475EB1F23E16DD2CF235BD9076715402BAD5DD (void);
// 0x000002C9 System.Void Cinemachine.CinemachineCore::RemoveActiveCamera(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_RemoveActiveCamera_m879CC4BCC026A83C761ACDDA9604766086B53361 (void);
// 0x000002CA System.Void Cinemachine.CinemachineCore::CameraDestroyed(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_CameraDestroyed_m15FC49C579C1933A8D3CD481B23ED229AABB25E5 (void);
// 0x000002CB System.Void Cinemachine.CinemachineCore::CameraEnabled(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_CameraEnabled_mF0183BD36CDF19EB604E6C966F78030D53A08DDB (void);
// 0x000002CC System.Void Cinemachine.CinemachineCore::CameraDisabled(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_CameraDisabled_mFFD409EFB372B7C90824BDE0C576EFF56F61285F (void);
// 0x000002CD System.Void Cinemachine.CinemachineCore::UpdateAllActiveVirtualCameras(System.Int32,UnityEngine.Vector3,System.Single)
extern void CinemachineCore_UpdateAllActiveVirtualCameras_m89205DECAC84E31CFF4420DDB36BDFC70BE37B53 (void);
// 0x000002CE System.Void Cinemachine.CinemachineCore::UpdateVirtualCamera(Cinemachine.CinemachineVirtualCameraBase,UnityEngine.Vector3,System.Single)
extern void CinemachineCore_UpdateVirtualCamera_m8AD0E1834C19A941F246C0BD9661ED575CAA84B1 (void);
// 0x000002CF System.Void Cinemachine.CinemachineCore::InitializeModule()
extern void CinemachineCore_InitializeModule_m3AF5A969E84D95E78CD79107015F033FF4EB364F (void);
// 0x000002D0 UnityEngine.Transform Cinemachine.CinemachineCore::GetUpdateTarget(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_GetUpdateTarget_m3007FAAA1292808633CDFF235EADB297B8506282 (void);
// 0x000002D1 Cinemachine.UpdateTracker/UpdateClock Cinemachine.CinemachineCore::GetVcamUpdateStatus(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_GetVcamUpdateStatus_m6864417D5726747911A8FD36CFE4BF7BC2551ADC (void);
// 0x000002D2 System.Boolean Cinemachine.CinemachineCore::IsLive(Cinemachine.ICinemachineCamera)
extern void CinemachineCore_IsLive_m6F2EBE598087857FF7D04A078563E9972CA52678 (void);
// 0x000002D3 System.Boolean Cinemachine.CinemachineCore::IsLiveInBlend(Cinemachine.ICinemachineCamera)
extern void CinemachineCore_IsLiveInBlend_mFD1402FFF3B5D0CD0EC90914F89672724F49F778 (void);
// 0x000002D4 System.Void Cinemachine.CinemachineCore::GenerateCameraActivationEvent(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera)
extern void CinemachineCore_GenerateCameraActivationEvent_mD2A009E3CE9D80962BA7E902A9BD3B22E161C021 (void);
// 0x000002D5 System.Void Cinemachine.CinemachineCore::GenerateCameraCutEvent(Cinemachine.ICinemachineCamera)
extern void CinemachineCore_GenerateCameraCutEvent_mED951E792CC6811DFF12F79B389C3237094FACD6 (void);
// 0x000002D6 Cinemachine.CinemachineBrain Cinemachine.CinemachineCore::FindPotentialTargetBrain(Cinemachine.CinemachineVirtualCameraBase)
extern void CinemachineCore_FindPotentialTargetBrain_mD4D554DCF27AE5BD2BAEB9D09E37BFBDD9D79B3B (void);
// 0x000002D7 System.Void Cinemachine.CinemachineCore::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineCore_OnTargetObjectWarped_m39BF4DE7271E080415FDB67E1C32C8E0C94702E1 (void);
// 0x000002D8 System.Void Cinemachine.CinemachineCore::.ctor()
extern void CinemachineCore__ctor_m2F77D083353903E3CC95FC46D88EFE627D6FC2D4 (void);
// 0x000002D9 System.Void Cinemachine.CinemachineCore::.cctor()
extern void CinemachineCore__cctor_m206AFF5C9C6BE921CCFBB41D19902D33EF70EC37 (void);
// 0x000002DA System.Void Cinemachine.CinemachineCore/AxisInputDelegate::.ctor(System.Object,System.IntPtr)
extern void AxisInputDelegate__ctor_m8D7FED117FA4DDE1BBDC08B5158E73E2FACFF7B6 (void);
// 0x000002DB System.Single Cinemachine.CinemachineCore/AxisInputDelegate::Invoke(System.String)
extern void AxisInputDelegate_Invoke_m1C36C70E105C8A9091AED921BB6E7053C99F39CE (void);
// 0x000002DC System.IAsyncResult Cinemachine.CinemachineCore/AxisInputDelegate::BeginInvoke(System.String,System.AsyncCallback,System.Object)
extern void AxisInputDelegate_BeginInvoke_m7A4D0E95E70B542E40AF139F3FB6986F2973F30C (void);
// 0x000002DD System.Single Cinemachine.CinemachineCore/AxisInputDelegate::EndInvoke(System.IAsyncResult)
extern void AxisInputDelegate_EndInvoke_m03AB17EB531ED61AFA1262B48356151A0D162F24 (void);
// 0x000002DE System.Void Cinemachine.CinemachineCore/GetBlendOverrideDelegate::.ctor(System.Object,System.IntPtr)
extern void GetBlendOverrideDelegate__ctor_mE8D07530859FC280F4A642B0B05322C3568EB0C9 (void);
// 0x000002DF Cinemachine.CinemachineBlendDefinition Cinemachine.CinemachineCore/GetBlendOverrideDelegate::Invoke(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera,Cinemachine.CinemachineBlendDefinition,UnityEngine.MonoBehaviour)
extern void GetBlendOverrideDelegate_Invoke_m95589B7FBA20E761B45993D16D9D930F84E39D84 (void);
// 0x000002E0 System.IAsyncResult Cinemachine.CinemachineCore/GetBlendOverrideDelegate::BeginInvoke(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera,Cinemachine.CinemachineBlendDefinition,UnityEngine.MonoBehaviour,System.AsyncCallback,System.Object)
extern void GetBlendOverrideDelegate_BeginInvoke_mC0BD5CA7322A5B456D5955B571EB11D5D6485603 (void);
// 0x000002E1 Cinemachine.CinemachineBlendDefinition Cinemachine.CinemachineCore/GetBlendOverrideDelegate::EndInvoke(System.IAsyncResult)
extern void GetBlendOverrideDelegate_EndInvoke_m299DDA873DCE525E30CCB9621E6EB9BF9F897061 (void);
// 0x000002E2 System.Void Cinemachine.CinemachineCore/UpdateStatus::.ctor()
extern void UpdateStatus__ctor_mD7EB6573750445A83891060458B7305AB1899583 (void);
// 0x000002E3 System.Void Cinemachine.CinemachineCore/<>c::.cctor()
extern void U3CU3Ec__cctor_m88CAF6DA4D43FD43D6B42F407696C49FE93DF489 (void);
// 0x000002E4 System.Void Cinemachine.CinemachineCore/<>c::.ctor()
extern void U3CU3Ec__ctor_m0FE2AE12D8ED3BA533A9FE180F290766D49099AD (void);
// 0x000002E5 System.Int32 Cinemachine.CinemachineCore/<>c::<GetVirtualCamera>b__30_0(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineVirtualCameraBase)
extern void U3CU3Ec_U3CGetVirtualCameraU3Eb__30_0_mD00C1D63B243EAC04AF7754294C5C9998BBB1DEF (void);
// 0x000002E6 Cinemachine.CinemachineVirtualCameraBase Cinemachine.CinemachineExtension::get_VirtualCamera()
extern void CinemachineExtension_get_VirtualCamera_mD9E9C61D2B18DD4B1ECF1B6A12EE5FD3B152376E (void);
// 0x000002E7 System.Void Cinemachine.CinemachineExtension::Awake()
extern void CinemachineExtension_Awake_mF3E9E30D4CBBC656B8758FDAF759B5DFA1774C9E (void);
// 0x000002E8 System.Void Cinemachine.CinemachineExtension::OnEnable()
extern void CinemachineExtension_OnEnable_mAABA4125E1F4271A991D234F62771AD496E9EF98 (void);
// 0x000002E9 System.Void Cinemachine.CinemachineExtension::OnDestroy()
extern void CinemachineExtension_OnDestroy_m856A803E3DAE93CD0AADFA9B687A430BD24616DE (void);
// 0x000002EA System.Void Cinemachine.CinemachineExtension::EnsureStarted()
extern void CinemachineExtension_EnsureStarted_mDC4AE5D72929029A5F995886E4E5298269173FA9 (void);
// 0x000002EB System.Void Cinemachine.CinemachineExtension::ConnectToVcam(System.Boolean)
extern void CinemachineExtension_ConnectToVcam_m2052046FF2E7EB0CBB72680EAA521B5C63CDB0EC (void);
// 0x000002EC System.Void Cinemachine.CinemachineExtension::PrePipelineMutateCameraStateCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CameraState&,System.Single)
extern void CinemachineExtension_PrePipelineMutateCameraStateCallback_m5011C9CC6618F64C0A02C9BFB7E2E572E7DA3BA1 (void);
// 0x000002ED System.Void Cinemachine.CinemachineExtension::InvokePostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineExtension_InvokePostPipelineStageCallback_m67F6FE82D6B103A0B77281CE76F2BC9DBCAFE0C0 (void);
// 0x000002EE System.Void Cinemachine.CinemachineExtension::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
// 0x000002EF System.Void Cinemachine.CinemachineExtension::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineExtension_OnTargetObjectWarped_mBA612C74EBA9DC865CC6A719C5B08C4C20D785D5 (void);
// 0x000002F0 System.Void Cinemachine.CinemachineExtension::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineExtension_ForceCameraPosition_mA120130FCDE1C615EF0C6BBA3548C7B85855CD3F (void);
// 0x000002F1 System.Boolean Cinemachine.CinemachineExtension::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineExtension_OnTransitionFromCamera_mE638BC9E573F2F32B6D6A01EE6734DF8C54E46D2 (void);
// 0x000002F2 System.Single Cinemachine.CinemachineExtension::GetMaxDampTime()
extern void CinemachineExtension_GetMaxDampTime_mB9B8F4B9F9B418766F43C1660840B07465672823 (void);
// 0x000002F3 System.Boolean Cinemachine.CinemachineExtension::get_RequiresUserInput()
extern void CinemachineExtension_get_RequiresUserInput_m1BA5050417CEAF7E438AADFC1F3F847AD3C64FC6 (void);
// 0x000002F4 T Cinemachine.CinemachineExtension::GetExtraState(Cinemachine.ICinemachineCamera)
// 0x000002F5 System.Collections.Generic.List`1<T> Cinemachine.CinemachineExtension::GetAllExtraStates()
// 0x000002F6 System.Void Cinemachine.CinemachineExtension::.ctor()
extern void CinemachineExtension__ctor_m8955F80D4A62DE7C3DA510CFECEF722346241C94 (void);
// 0x000002F7 System.Void Cinemachine.AxisBase::Validate()
extern void AxisBase_Validate_mD6017BA404C55814A0E55DD7D036FA666EE038CB (void);
// 0x000002F8 System.Void Cinemachine.CinemachineInputAxisDriver::Validate()
extern void CinemachineInputAxisDriver_Validate_mC2EFECCBF1C729D83650ECCB9EC02BA70A3692FC (void);
// 0x000002F9 System.Boolean Cinemachine.CinemachineInputAxisDriver::Update(System.Single,Cinemachine.AxisBase&)
extern void CinemachineInputAxisDriver_Update_m8AFFF82834DDE9F93045956D8A9EEEA933766FD4 (void);
// 0x000002FA System.Boolean Cinemachine.CinemachineInputAxisDriver::Update(System.Single,Cinemachine.AxisState&)
extern void CinemachineInputAxisDriver_Update_m24BE353BA761E2D8A9EE55CF6274D17C31EB3F76 (void);
// 0x000002FB System.Single Cinemachine.CinemachineInputAxisDriver::ClampValue(Cinemachine.AxisBase&,System.Single)
extern void CinemachineInputAxisDriver_ClampValue_mA2A92688571EA4584213869F0C7CA9A7699B2747 (void);
// 0x000002FC System.Single Cinemachine.CinemachinePathBase::get_MinPos()
// 0x000002FD System.Single Cinemachine.CinemachinePathBase::get_MaxPos()
// 0x000002FE System.Boolean Cinemachine.CinemachinePathBase::get_Looped()
// 0x000002FF System.Single Cinemachine.CinemachinePathBase::StandardizePos(System.Single)
extern void CinemachinePathBase_StandardizePos_mCD78307D06204C7543CB99D3F2FB5F320B996423 (void);
// 0x00000300 UnityEngine.Vector3 Cinemachine.CinemachinePathBase::EvaluatePosition(System.Single)
extern void CinemachinePathBase_EvaluatePosition_mD21133E8D0CDCBBDE460D6887C77EA998E78AF80 (void);
// 0x00000301 UnityEngine.Vector3 Cinemachine.CinemachinePathBase::EvaluateTangent(System.Single)
extern void CinemachinePathBase_EvaluateTangent_m97E5547FC4721284BB6A39980A5D4F3721BAFD3C (void);
// 0x00000302 UnityEngine.Quaternion Cinemachine.CinemachinePathBase::EvaluateOrientation(System.Single)
extern void CinemachinePathBase_EvaluateOrientation_m4A61E042185BAD353F6C9658E2B12FE93A456034 (void);
// 0x00000303 UnityEngine.Vector3 Cinemachine.CinemachinePathBase::EvaluateLocalPosition(System.Single)
// 0x00000304 UnityEngine.Vector3 Cinemachine.CinemachinePathBase::EvaluateLocalTangent(System.Single)
// 0x00000305 UnityEngine.Quaternion Cinemachine.CinemachinePathBase::EvaluateLocalOrientation(System.Single)
// 0x00000306 System.Single Cinemachine.CinemachinePathBase::FindClosestPoint(UnityEngine.Vector3,System.Int32,System.Int32,System.Int32)
extern void CinemachinePathBase_FindClosestPoint_m57D4703AEE2B706B72070E39406CFF0DF5BE5F81 (void);
// 0x00000307 System.Single Cinemachine.CinemachinePathBase::MinUnit(Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_MinUnit_m0B6884D7FD0928C74C702C191368EF0B37712737 (void);
// 0x00000308 System.Single Cinemachine.CinemachinePathBase::MaxUnit(Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_MaxUnit_mD6C8BEEF736AF66618CD9FEA69D61CC5C9854F76 (void);
// 0x00000309 System.Single Cinemachine.CinemachinePathBase::StandardizeUnit(System.Single,Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_StandardizeUnit_m0BD23E05F3EF8C163CB4F7830B2336B3D0713460 (void);
// 0x0000030A UnityEngine.Vector3 Cinemachine.CinemachinePathBase::EvaluatePositionAtUnit(System.Single,Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_EvaluatePositionAtUnit_mCE1B51BBCAEFF5A65A68F1D3113390F7BC223843 (void);
// 0x0000030B UnityEngine.Vector3 Cinemachine.CinemachinePathBase::EvaluateTangentAtUnit(System.Single,Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_EvaluateTangentAtUnit_m0EA566D1633A62430B68960B5AF3E882F9F2219E (void);
// 0x0000030C UnityEngine.Quaternion Cinemachine.CinemachinePathBase::EvaluateOrientationAtUnit(System.Single,Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_EvaluateOrientationAtUnit_m28859D88DD40B298B14EE6D04A6358534E09C0A7 (void);
// 0x0000030D System.Int32 Cinemachine.CinemachinePathBase::get_DistanceCacheSampleStepsPerSegment()
// 0x0000030E System.Void Cinemachine.CinemachinePathBase::InvalidateDistanceCache()
extern void CinemachinePathBase_InvalidateDistanceCache_m5A8B12C547975C78D5167E08B823DCD800799878 (void);
// 0x0000030F System.Boolean Cinemachine.CinemachinePathBase::DistanceCacheIsValid()
extern void CinemachinePathBase_DistanceCacheIsValid_m513365FFC2B3206A2C0687DD7F0F3C698C031240 (void);
// 0x00000310 System.Single Cinemachine.CinemachinePathBase::get_PathLength()
extern void CinemachinePathBase_get_PathLength_m7416A92ED48925E5C49D4D6B70C13AEEA4A90660 (void);
// 0x00000311 System.Single Cinemachine.CinemachinePathBase::StandardizePathDistance(System.Single)
extern void CinemachinePathBase_StandardizePathDistance_mA05975615F90DFD7E4F000026B3C6200DA02591E (void);
// 0x00000312 System.Single Cinemachine.CinemachinePathBase::ToNativePathUnits(System.Single,Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_ToNativePathUnits_m71355B86B0027D58831E4B9489CCFEE69B7E9158 (void);
// 0x00000313 System.Single Cinemachine.CinemachinePathBase::FromPathNativeUnits(System.Single,Cinemachine.CinemachinePathBase/PositionUnits)
extern void CinemachinePathBase_FromPathNativeUnits_mEFCB692BFEC5A048AF23D9BA3EC74A4255D5D867 (void);
// 0x00000314 System.Void Cinemachine.CinemachinePathBase::ResamplePath(System.Int32)
extern void CinemachinePathBase_ResamplePath_mDB5434016BE1079B26B3DF4C4371B446875648C1 (void);
// 0x00000315 System.Void Cinemachine.CinemachinePathBase::.ctor()
extern void CinemachinePathBase__ctor_m8BEE8B0F31272FDA797F0459F992A3B491570A3F (void);
// 0x00000316 System.Void Cinemachine.CinemachinePathBase/Appearance::.ctor()
extern void Appearance__ctor_m3600590C110F6BC2BBA3F48A4334B8A38A030796 (void);
// 0x00000317 System.Void Cinemachine.AxisStatePropertyAttribute::.ctor()
extern void AxisStatePropertyAttribute__ctor_m1D11BE55127A1BACC4CB41824FF44D0E00A0DC87 (void);
// 0x00000318 System.Void Cinemachine.OrbitalTransposerHeadingPropertyAttribute::.ctor()
extern void OrbitalTransposerHeadingPropertyAttribute__ctor_m5B7E2CE0AC1A499F6E98EFAAE60778B2F21E8928 (void);
// 0x00000319 System.Void Cinemachine.LensSettingsPropertyAttribute::.ctor()
extern void LensSettingsPropertyAttribute__ctor_mFF909C9CF0455F425DCA867724371F4F4EF66A79 (void);
// 0x0000031A System.Void Cinemachine.VcamTargetPropertyAttribute::.ctor()
extern void VcamTargetPropertyAttribute__ctor_mE0BC017BF1E9F5AEC1B8BCFCED347AA3D88D8A29 (void);
// 0x0000031B System.Void Cinemachine.CinemachineBlendDefinitionPropertyAttribute::.ctor()
extern void CinemachineBlendDefinitionPropertyAttribute__ctor_mA824C842C2AB60CF5A7528ADFA275AF270B8ADB9 (void);
// 0x0000031C System.Void Cinemachine.SaveDuringPlayAttribute::.ctor()
extern void SaveDuringPlayAttribute__ctor_m1F7704B00AFB92A9175DFDEC562EFF62D768807A (void);
// 0x0000031D System.Void Cinemachine.NoSaveDuringPlayAttribute::.ctor()
extern void NoSaveDuringPlayAttribute__ctor_m04387BDD20964808880B69859589C3CE10BFE22E (void);
// 0x0000031E System.Void Cinemachine.TagFieldAttribute::.ctor()
extern void TagFieldAttribute__ctor_m37CB058BFE10C3277F3180F177571BC7F985C6B8 (void);
// 0x0000031F System.Void Cinemachine.NoiseSettingsPropertyAttribute::.ctor()
extern void NoiseSettingsPropertyAttribute__ctor_mE969AACC17EE1AC1C654D65EB7C1E036BB4E16C4 (void);
// 0x00000320 System.Void Cinemachine.CinemachineEmbeddedAssetPropertyAttribute::.ctor(System.Boolean)
extern void CinemachineEmbeddedAssetPropertyAttribute__ctor_m5AA2CEDCBE161BBB0F5B93AB0691E7D72FBA8F32 (void);
// 0x00000321 Cinemachine.DocumentationSortingAttribute/Level Cinemachine.DocumentationSortingAttribute::get_Category()
extern void DocumentationSortingAttribute_get_Category_mE12739E1F69EC05F32AB71A8AC8252DC7B8D722C (void);
// 0x00000322 System.Void Cinemachine.DocumentationSortingAttribute::set_Category(Cinemachine.DocumentationSortingAttribute/Level)
extern void DocumentationSortingAttribute_set_Category_mBF1A4C6A80D15E9A772468A2B4981A7658FCFD5B (void);
// 0x00000323 System.Void Cinemachine.DocumentationSortingAttribute::.ctor(Cinemachine.DocumentationSortingAttribute/Level)
extern void DocumentationSortingAttribute__ctor_m31E87AB883A8DF0AF3B9D233D4AAE4FE5964DE6F (void);
// 0x00000324 System.Int32 Cinemachine.CinemachineVirtualCameraBase::get_ValidatingStreamVersion()
extern void CinemachineVirtualCameraBase_get_ValidatingStreamVersion_m3FA3A728FAF24AA79D08CB23CB7416D250280C45 (void);
// 0x00000325 System.Void Cinemachine.CinemachineVirtualCameraBase::set_ValidatingStreamVersion(System.Int32)
extern void CinemachineVirtualCameraBase_set_ValidatingStreamVersion_mE99F8D7C363F7259CD22F03D43428AA9E14ED433 (void);
// 0x00000326 System.Single Cinemachine.CinemachineVirtualCameraBase::GetMaxDampTime()
extern void CinemachineVirtualCameraBase_GetMaxDampTime_m4D72BD93F9B3B32BDFB20976CE7A1E8B41567047 (void);
// 0x00000327 System.Single Cinemachine.CinemachineVirtualCameraBase::DetachedFollowTargetDamp(System.Single,System.Single,System.Single)
extern void CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m215A089B8451330FA8D7D6E4DB8E38400AD9E7CF (void);
// 0x00000328 UnityEngine.Vector3 Cinemachine.CinemachineVirtualCameraBase::DetachedFollowTargetDamp(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m871E131EE59CEEC1B5691F5DC570B18816530C97 (void);
// 0x00000329 UnityEngine.Vector3 Cinemachine.CinemachineVirtualCameraBase::DetachedFollowTargetDamp(UnityEngine.Vector3,System.Single,System.Single)
extern void CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m12B68094CE823031220DD1E2EAB52AAD0AC25412 (void);
// 0x0000032A System.Single Cinemachine.CinemachineVirtualCameraBase::DetachedLookAtTargetDamp(System.Single,System.Single,System.Single)
extern void CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_mFB6FAA90EB2A5263D19E3D91C30C072C972E849E (void);
// 0x0000032B UnityEngine.Vector3 Cinemachine.CinemachineVirtualCameraBase::DetachedLookAtTargetDamp(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m3919D9F0DA00F12FA05608A3EBE1345E938E5DE3 (void);
// 0x0000032C UnityEngine.Vector3 Cinemachine.CinemachineVirtualCameraBase::DetachedLookAtTargetDamp(UnityEngine.Vector3,System.Single,System.Single)
extern void CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m8ABF51D39DFC31B7EF4FBCC0139ACDB64FD7F94E (void);
// 0x0000032D System.Void Cinemachine.CinemachineVirtualCameraBase::AddExtension(Cinemachine.CinemachineExtension)
extern void CinemachineVirtualCameraBase_AddExtension_m84615F482D5461685B226D4B57539FA2E7F8EE20 (void);
// 0x0000032E System.Void Cinemachine.CinemachineVirtualCameraBase::RemoveExtension(Cinemachine.CinemachineExtension)
extern void CinemachineVirtualCameraBase_RemoveExtension_m7047B670478FDAF97D48F2906539F250D354EF6C (void);
// 0x0000032F System.Collections.Generic.List`1<Cinemachine.CinemachineExtension> Cinemachine.CinemachineVirtualCameraBase::get_mExtensions()
extern void CinemachineVirtualCameraBase_get_mExtensions_mF66637306356FAB2C06F53097534FC7AA54ECD00 (void);
// 0x00000330 System.Void Cinemachine.CinemachineVirtualCameraBase::set_mExtensions(System.Collections.Generic.List`1<Cinemachine.CinemachineExtension>)
extern void CinemachineVirtualCameraBase_set_mExtensions_m14B06AC70E1112D5D87372C2D868EA55B7B218E5 (void);
// 0x00000331 System.Void Cinemachine.CinemachineVirtualCameraBase::InvokePostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineVirtualCameraBase_InvokePostPipelineStageCallback_m55457276F8291B6645FE4F7250C84DE97DDFDECF (void);
// 0x00000332 System.Void Cinemachine.CinemachineVirtualCameraBase::InvokePrePipelineMutateCameraStateCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CameraState&,System.Single)
extern void CinemachineVirtualCameraBase_InvokePrePipelineMutateCameraStateCallback_m6468DFDE19E8860EC1EE36073D4BEE179D609CB1 (void);
// 0x00000333 System.Boolean Cinemachine.CinemachineVirtualCameraBase::InvokeOnTransitionInExtensions(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCameraBase_InvokeOnTransitionInExtensions_m0D02A9178DEE05F756D00FDBA9A4A5B6EE3BB225 (void);
// 0x00000334 System.String Cinemachine.CinemachineVirtualCameraBase::get_Name()
extern void CinemachineVirtualCameraBase_get_Name_mD5D510F82C6F26960F8036AC76A6B107E0304DD0 (void);
// 0x00000335 System.String Cinemachine.CinemachineVirtualCameraBase::get_Description()
extern void CinemachineVirtualCameraBase_get_Description_m6D0D078084943330192565A884AA823576E68792 (void);
// 0x00000336 System.Int32 Cinemachine.CinemachineVirtualCameraBase::get_Priority()
extern void CinemachineVirtualCameraBase_get_Priority_m273769ED137982DE43BB658BBE704BCAA55E5246 (void);
// 0x00000337 System.Void Cinemachine.CinemachineVirtualCameraBase::set_Priority(System.Int32)
extern void CinemachineVirtualCameraBase_set_Priority_m233ED0376CE0BD1244CCA52DF4532C8988DC05AC (void);
// 0x00000338 System.Void Cinemachine.CinemachineVirtualCameraBase::ApplyPositionBlendMethod(Cinemachine.CameraState&,Cinemachine.CinemachineVirtualCameraBase/BlendHint)
extern void CinemachineVirtualCameraBase_ApplyPositionBlendMethod_mD956666402D6A533C23C8AE5AD6DB5C875C2FDCD (void);
// 0x00000339 UnityEngine.GameObject Cinemachine.CinemachineVirtualCameraBase::get_VirtualCameraGameObject()
extern void CinemachineVirtualCameraBase_get_VirtualCameraGameObject_m85F9D37C4395161F7C4023FDCD9858EC8952D617 (void);
// 0x0000033A System.Boolean Cinemachine.CinemachineVirtualCameraBase::get_IsValid()
extern void CinemachineVirtualCameraBase_get_IsValid_m5BB3331E066DF4C9A02FA433F785C5B64C3330C3 (void);
// 0x0000033B Cinemachine.CameraState Cinemachine.CinemachineVirtualCameraBase::get_State()
// 0x0000033C Cinemachine.ICinemachineCamera Cinemachine.CinemachineVirtualCameraBase::get_ParentCamera()
extern void CinemachineVirtualCameraBase_get_ParentCamera_m1A8F8096F8BBD5B994873B756069EA00AEDD5DCD (void);
// 0x0000033D System.Boolean Cinemachine.CinemachineVirtualCameraBase::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
extern void CinemachineVirtualCameraBase_IsLiveChild_m21C2756B555D31CD1EE71FCE06A76254C9E206ED (void);
// 0x0000033E UnityEngine.Transform Cinemachine.CinemachineVirtualCameraBase::get_LookAt()
// 0x0000033F System.Void Cinemachine.CinemachineVirtualCameraBase::set_LookAt(UnityEngine.Transform)
// 0x00000340 UnityEngine.Transform Cinemachine.CinemachineVirtualCameraBase::get_Follow()
// 0x00000341 System.Void Cinemachine.CinemachineVirtualCameraBase::set_Follow(UnityEngine.Transform)
// 0x00000342 System.Boolean Cinemachine.CinemachineVirtualCameraBase::get_PreviousStateIsValid()
extern void CinemachineVirtualCameraBase_get_PreviousStateIsValid_m4B2B6DD649ACD80A30AA0ACE19449EE49C13DF7B (void);
// 0x00000343 System.Void Cinemachine.CinemachineVirtualCameraBase::set_PreviousStateIsValid(System.Boolean)
extern void CinemachineVirtualCameraBase_set_PreviousStateIsValid_mA7F5F377366606747B15F5A35EE3E6DA7DAEDDE8 (void);
// 0x00000344 System.Void Cinemachine.CinemachineVirtualCameraBase::UpdateCameraState(UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCameraBase_UpdateCameraState_mC16F582BFC88FD8E3BC61EEFC11EA243A4289836 (void);
// 0x00000345 System.Void Cinemachine.CinemachineVirtualCameraBase::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
// 0x00000346 System.Void Cinemachine.CinemachineVirtualCameraBase::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
extern void CinemachineVirtualCameraBase_OnTransitionFromCamera_m867C0945DA41A144EE64BDF095F13CE1EB1B3493 (void);
// 0x00000347 System.Void Cinemachine.CinemachineVirtualCameraBase::OnDestroy()
extern void CinemachineVirtualCameraBase_OnDestroy_m57F48C3BC5A324EDE38FD6A59FB1A8165EC823B5 (void);
// 0x00000348 System.Void Cinemachine.CinemachineVirtualCameraBase::OnTransformParentChanged()
extern void CinemachineVirtualCameraBase_OnTransformParentChanged_mE18E59ADAD6A7CFC9DB45C0D18D09EA0E9B1D46C (void);
// 0x00000349 System.Void Cinemachine.CinemachineVirtualCameraBase::Start()
extern void CinemachineVirtualCameraBase_Start_m3855F6FFCEC24D4700C39D8798D8558FD5524003 (void);
// 0x0000034A System.Boolean Cinemachine.CinemachineVirtualCameraBase::RequiresUserInput()
extern void CinemachineVirtualCameraBase_RequiresUserInput_m1C2BE14E1F00E4EBD5BD1C9C4AF56E47AEF161A8 (void);
// 0x0000034B System.Void Cinemachine.CinemachineVirtualCameraBase::EnsureStarted()
extern void CinemachineVirtualCameraBase_EnsureStarted_m5BE1C9CC37F882E1B76E2C84BC6B789BF14451D0 (void);
// 0x0000034C Cinemachine.AxisState/IInputAxisProvider Cinemachine.CinemachineVirtualCameraBase::GetInputAxisProvider()
extern void CinemachineVirtualCameraBase_GetInputAxisProvider_mC735C4764E6CB8469D115142D842729C95D9C39E (void);
// 0x0000034D System.Void Cinemachine.CinemachineVirtualCameraBase::OnValidate()
extern void CinemachineVirtualCameraBase_OnValidate_m53411A67E10ECB12B114F5FC47C1026E0BEDB1D0 (void);
// 0x0000034E System.Void Cinemachine.CinemachineVirtualCameraBase::OnEnable()
extern void CinemachineVirtualCameraBase_OnEnable_m56F225786F42BD1069930B91D8448A0779F71F4B (void);
// 0x0000034F System.Void Cinemachine.CinemachineVirtualCameraBase::OnDisable()
extern void CinemachineVirtualCameraBase_OnDisable_mF7435377931FB3F6EC410752D62C18392C47917A (void);
// 0x00000350 System.Void Cinemachine.CinemachineVirtualCameraBase::Update()
extern void CinemachineVirtualCameraBase_Update_mF88D942EB1867E4A52BF819B17FF5BEDE62853E7 (void);
// 0x00000351 System.Void Cinemachine.CinemachineVirtualCameraBase::UpdateSlaveStatus()
extern void CinemachineVirtualCameraBase_UpdateSlaveStatus_m84F59A7B5AD0A8C77ADD3F59AEE050DF0F21DB90 (void);
// 0x00000352 UnityEngine.Transform Cinemachine.CinemachineVirtualCameraBase::ResolveLookAt(UnityEngine.Transform)
extern void CinemachineVirtualCameraBase_ResolveLookAt_mDAFDBB0AC2E3CF214A3093116560E55B681BC172 (void);
// 0x00000353 UnityEngine.Transform Cinemachine.CinemachineVirtualCameraBase::ResolveFollow(UnityEngine.Transform)
extern void CinemachineVirtualCameraBase_ResolveFollow_m9A01047C3AB01393D582E1879EC53213D38AE7E8 (void);
// 0x00000354 System.Void Cinemachine.CinemachineVirtualCameraBase::UpdateVcamPoolStatus()
extern void CinemachineVirtualCameraBase_UpdateVcamPoolStatus_mA377CAB45CBB5AF28FE084955BC2CF4C7C944883 (void);
// 0x00000355 System.Void Cinemachine.CinemachineVirtualCameraBase::MoveToTopOfPrioritySubqueue()
extern void CinemachineVirtualCameraBase_MoveToTopOfPrioritySubqueue_mDB771BE69809F79B86410E4102E30C13F0E9044D (void);
// 0x00000356 System.Void Cinemachine.CinemachineVirtualCameraBase::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
extern void CinemachineVirtualCameraBase_OnTargetObjectWarped_m918462E96A2EEC9848F6C47B0F35E0D5C0DBF333 (void);
// 0x00000357 System.Void Cinemachine.CinemachineVirtualCameraBase::ForceCameraPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void CinemachineVirtualCameraBase_ForceCameraPosition_m3EE879AD97E2BDB01606CB878C6E789078271492 (void);
// 0x00000358 System.Boolean Cinemachine.CinemachineVirtualCameraBase::GetInheritPosition(Cinemachine.ICinemachineCamera)
extern void CinemachineVirtualCameraBase_GetInheritPosition_m7D3E2A6630CAE96C0EB4DFD35DC28A1D30CF4703 (void);
// 0x00000359 Cinemachine.CinemachineBlend Cinemachine.CinemachineVirtualCameraBase::CreateBlend(Cinemachine.ICinemachineCamera,Cinemachine.ICinemachineCamera,Cinemachine.CinemachineBlendDefinition,Cinemachine.CinemachineBlend)
extern void CinemachineVirtualCameraBase_CreateBlend_m8CCA4253F833A686FEA07989BB3D068A246FF2CA (void);
// 0x0000035A Cinemachine.CameraState Cinemachine.CinemachineVirtualCameraBase::PullStateFromVirtualCamera(UnityEngine.Vector3,Cinemachine.LensSettings&)
extern void CinemachineVirtualCameraBase_PullStateFromVirtualCamera_m0629C5BA281A84DFA090D4B28CFBCC8E234BB298 (void);
// 0x0000035B System.Void Cinemachine.CinemachineVirtualCameraBase::InvalidateCachedTargets()
extern void CinemachineVirtualCameraBase_InvalidateCachedTargets_m66FC53DA8DB6FC7BC34EF2B9E6CDF68DB74F3D3C (void);
// 0x0000035C System.Boolean Cinemachine.CinemachineVirtualCameraBase::get_FollowTargetChanged()
extern void CinemachineVirtualCameraBase_get_FollowTargetChanged_m4CB9C2AA28F8B2898B82BBF51348C6670110ADF2 (void);
// 0x0000035D System.Void Cinemachine.CinemachineVirtualCameraBase::set_FollowTargetChanged(System.Boolean)
extern void CinemachineVirtualCameraBase_set_FollowTargetChanged_m60A2DC1564B3D6A17821605470D9919C590FE0D7 (void);
// 0x0000035E System.Boolean Cinemachine.CinemachineVirtualCameraBase::get_LookAtTargetChanged()
extern void CinemachineVirtualCameraBase_get_LookAtTargetChanged_m6D2FF4FB863501796CB778CB7AABA0126E57C134 (void);
// 0x0000035F System.Void Cinemachine.CinemachineVirtualCameraBase::set_LookAtTargetChanged(System.Boolean)
extern void CinemachineVirtualCameraBase_set_LookAtTargetChanged_mA9FFCF3EC189C5CD35BE0E5B450939B4A8BB1D8B (void);
// 0x00000360 System.Void Cinemachine.CinemachineVirtualCameraBase::UpdateTargetCache()
extern void CinemachineVirtualCameraBase_UpdateTargetCache_m0472352417911B6F5E21A85A2BBDA72ECEB85BAE (void);
// 0x00000361 Cinemachine.ICinemachineTargetGroup Cinemachine.CinemachineVirtualCameraBase::get_AbstractFollowTargetGroup()
extern void CinemachineVirtualCameraBase_get_AbstractFollowTargetGroup_mF66D843C00156E41B1C9C14FFBA0C4429D29AEBF (void);
// 0x00000362 Cinemachine.CinemachineVirtualCameraBase Cinemachine.CinemachineVirtualCameraBase::get_FollowTargetAsVcam()
extern void CinemachineVirtualCameraBase_get_FollowTargetAsVcam_mBABF664226D707405BAAA123F29619FAF6758053 (void);
// 0x00000363 Cinemachine.ICinemachineTargetGroup Cinemachine.CinemachineVirtualCameraBase::get_AbstractLookAtTargetGroup()
extern void CinemachineVirtualCameraBase_get_AbstractLookAtTargetGroup_mC8E5278181AF27E945676602C10DB78E6EE15DAA (void);
// 0x00000364 Cinemachine.CinemachineVirtualCameraBase Cinemachine.CinemachineVirtualCameraBase::get_LookAtTargetAsVcam()
extern void CinemachineVirtualCameraBase_get_LookAtTargetAsVcam_m51058DC76D5F1833C294015E89A65A64DB36A41F (void);
// 0x00000365 System.Void Cinemachine.CinemachineVirtualCameraBase::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void CinemachineVirtualCameraBase_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mBAC03B3A1F38032BA1E5ED1E824EECF6127BAC43 (void);
// 0x00000366 System.Void Cinemachine.CinemachineVirtualCameraBase::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void CinemachineVirtualCameraBase_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m3834C2540CDE8082263DD7587FDDDF3649306D99 (void);
// 0x00000367 System.Void Cinemachine.CinemachineVirtualCameraBase::LegacyUpgrade(System.Int32)
extern void CinemachineVirtualCameraBase_LegacyUpgrade_m12831751E9E0697F2A87F5B837C714C242796BFE (void);
// 0x00000368 System.Void Cinemachine.CinemachineVirtualCameraBase::OnBeforeSerialize()
extern void CinemachineVirtualCameraBase_OnBeforeSerialize_mAFFE51C4E0C07640C3104CF1EC0B808D3A1176C1 (void);
// 0x00000369 System.Void Cinemachine.CinemachineVirtualCameraBase::CancelDamping(System.Boolean)
extern void CinemachineVirtualCameraBase_CancelDamping_m3E85FF31183FA04DA877ACF8708C3985EC51B998 (void);
// 0x0000036A System.Void Cinemachine.CinemachineVirtualCameraBase::.ctor()
extern void CinemachineVirtualCameraBase__ctor_m1BACC836C669C0C52C8A891BAB34E09821F21C46 (void);
// 0x0000036B System.Void Cinemachine.CinemachineVirtualCameraBase/<>c::.cctor()
extern void U3CU3Ec__cctor_m83F07ABBB95DE062E87E35CA344B44AF2F9C7495 (void);
// 0x0000036C System.Void Cinemachine.CinemachineVirtualCameraBase/<>c::.ctor()
extern void U3CU3Ec__ctor_m1B1FF86140C80F51640B1FBB220C7086D68D0D30 (void);
// 0x0000036D System.Boolean Cinemachine.CinemachineVirtualCameraBase/<>c::<RequiresUserInput>b__66_0(Cinemachine.CinemachineExtension)
extern void U3CU3Ec_U3CRequiresUserInputU3Eb__66_0_m1178F3D4B17F28B357018AF317FF0BDDDD399F89 (void);
// 0x0000036E System.Void Cinemachine.ConfinerOven::.ctor(System.Collections.Generic.List`1<System.Collections.Generic.List`1<UnityEngine.Vector2>>&,System.Single&,System.Single,System.Single)
extern void ConfinerOven__ctor_m1C4C18216256E2888451CD5123F9170C805CE8CE (void);
// 0x0000036F Cinemachine.ConfinerOven/BakedSolution Cinemachine.ConfinerOven::GetBakedSolution(System.Single)
extern void ConfinerOven_GetBakedSolution_mF4174B4DFFAB8831F94627D68558CCD505FB3273 (void);
// 0x00000370 Cinemachine.ConfinerOven/BakingState Cinemachine.ConfinerOven::get_State()
extern void ConfinerOven_get_State_mA6308A899FB1F875B90CD6D1647071F3380F5470 (void);
// 0x00000371 System.Void Cinemachine.ConfinerOven::set_State(Cinemachine.ConfinerOven/BakingState)
extern void ConfinerOven_set_State_m1AB5EBA915E1EF83FAFA334DA7DD59388CE3AE6A (void);
// 0x00000372 System.Void Cinemachine.ConfinerOven::Initialize(System.Collections.Generic.List`1<System.Collections.Generic.List`1<UnityEngine.Vector2>>&,System.Single&,System.Single,System.Single)
extern void ConfinerOven_Initialize_m71FEAC5752F454084810F5EA33F04B2550B712A1 (void);
// 0x00000373 System.Void Cinemachine.ConfinerOven::BakeConfiner(System.Single)
extern void ConfinerOven_BakeConfiner_mB6F563C71AE4CBECBEE74F4A76770B09EE74348F (void);
// 0x00000374 UnityEngine.Rect Cinemachine.ConfinerOven::<Initialize>g__GetPolygonBoundingBox|24_0(System.Collections.Generic.List`1<System.Collections.Generic.List`1<UnityEngine.Vector2>>&)
extern void ConfinerOven_U3CInitializeU3Eg__GetPolygonBoundingBoxU7C24_0_m17DC6FA0F5288A71A59D241BE2F58BAF75D7F51E (void);
// 0x00000375 Cinemachine.ClipperLib/IntPoint Cinemachine.ConfinerOven::<Initialize>g__MidPointOfIntRect|24_1(Cinemachine.ClipperLib/IntRect)
extern void ConfinerOven_U3CInitializeU3Eg__MidPointOfIntRectU7C24_1_m25F63BF3258DE5F2324DA5351CB65A83744063EF (void);
// 0x00000376 System.Void Cinemachine.ConfinerOven::<BakeConfiner>g__ComputeSkeleton|25_0(System.Collections.Generic.List`1<Cinemachine.ConfinerOven/PolygonSolution>&)
extern void ConfinerOven_U3CBakeConfinerU3Eg__ComputeSkeletonU7C25_0_mF1E1E6E7DF36056216EA606F5B4BA90BB5F63003 (void);
// 0x00000377 System.Void Cinemachine.ConfinerOven/BakedSolution::.ctor(System.Single,System.Single,System.Boolean,UnityEngine.Rect,System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>)
extern void BakedSolution__ctor_mA1172BA1CEFBBF4467D64FFF62D0A2C386AC15F4 (void);
// 0x00000378 System.Boolean Cinemachine.ConfinerOven/BakedSolution::IsValid()
extern void BakedSolution_IsValid_m3EF3F690673A2328E948D0C5F7252B5775F5B7F9 (void);
// 0x00000379 UnityEngine.Vector2 Cinemachine.ConfinerOven/BakedSolution::ConfinePoint(UnityEngine.Vector2&)
extern void BakedSolution_ConfinePoint_mE43B748BAE673FA3E44EAB0EF05478C0FE4AA949 (void);
// 0x0000037A System.Int32 Cinemachine.ConfinerOven/BakedSolution::FindIntersection(Cinemachine.ClipperLib/IntPoint&,Cinemachine.ClipperLib/IntPoint&,Cinemachine.ClipperLib/IntPoint&,Cinemachine.ClipperLib/IntPoint&)
extern void BakedSolution_FindIntersection_mC5B880D5AB3CFCA35837A27BC906CAEC0B2C8ACC (void);
// 0x0000037B Cinemachine.ClipperLib/IntPoint Cinemachine.ConfinerOven/BakedSolution::<ConfinePoint>g__IntPointLerp|9_0(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,System.Single)
extern void BakedSolution_U3CConfinePointU3Eg__IntPointLerpU7C9_0_mB5D748A6BD7AA0AF2B829EBCC5CE96CD771B6F41 (void);
// 0x0000037C System.Boolean Cinemachine.ConfinerOven/BakedSolution::<ConfinePoint>g__IsInsideOriginal|9_1(Cinemachine.ClipperLib/IntPoint)
extern void BakedSolution_U3CConfinePointU3Eg__IsInsideOriginalU7C9_1_m5864C528E7CB650C265F883630E8A06B39BA809C (void);
// 0x0000037D System.Single Cinemachine.ConfinerOven/BakedSolution::<ConfinePoint>g__ClosestPointOnSegment|9_2(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void BakedSolution_U3CConfinePointU3Eg__ClosestPointOnSegmentU7C9_2_mB4893132D90DA9B406DBD6D9E85F6DB487FE361C (void);
// 0x0000037E System.Boolean Cinemachine.ConfinerOven/BakedSolution::<ConfinePoint>g__DoesIntersectOriginal|9_3(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void BakedSolution_U3CConfinePointU3Eg__DoesIntersectOriginalU7C9_3_m387046A6D2E2A143D05848F635EC8D48070A805D (void);
// 0x0000037F System.Double Cinemachine.ConfinerOven/BakedSolution::<FindIntersection>g__IntPointDiffSqrMagnitude|10_0(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void BakedSolution_U3CFindIntersectionU3Eg__IntPointDiffSqrMagnitudeU7C10_0_mF1681FA711188BB13B1BEAE931206FE45A1A8838 (void);
// 0x00000380 System.Void Cinemachine.ConfinerOven/BakedSolution/<>c__DisplayClass9_0::.ctor()
extern void U3CU3Ec__DisplayClass9_0__ctor_m1BDB065538D7B8869BB31B5CF477CEDB77B950EF (void);
// 0x00000381 System.Boolean Cinemachine.ConfinerOven/BakedSolution/<>c__DisplayClass9_0::<ConfinePoint>b__4(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>)
extern void U3CU3Ec__DisplayClass9_0_U3CConfinePointU3Eb__4_mA501F2C6999E9021FC3A4D5CE9A5786FD26F79D9 (void);
// 0x00000382 System.Single Cinemachine.ConfinerOven/AspectStretcher::get_Aspect()
extern void AspectStretcher_get_Aspect_m506D4C16F8E6AE36198ACCAE2C4AFEED14552272 (void);
// 0x00000383 System.Void Cinemachine.ConfinerOven/AspectStretcher::.ctor(System.Single,System.Single)
extern void AspectStretcher__ctor_m84A17187C183823205C2C732202DCBBAA2350852 (void);
// 0x00000384 UnityEngine.Vector2 Cinemachine.ConfinerOven/AspectStretcher::Stretch(UnityEngine.Vector2)
extern void AspectStretcher_Stretch_m1265459BD5A34090D4174D1D69509C2C53D36A92 (void);
// 0x00000385 UnityEngine.Vector2 Cinemachine.ConfinerOven/AspectStretcher::Unstretch(UnityEngine.Vector2)
extern void AspectStretcher_Unstretch_mA27250710163BEFDB2E5F0E074F41B878A7AB08E (void);
// 0x00000386 System.Boolean Cinemachine.ConfinerOven/PolygonSolution::StateChanged(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>&)
extern void PolygonSolution_StateChanged_mB1E13BE21168ACB0EA459D4757A52799724CD7A0 (void);
// 0x00000387 System.Boolean Cinemachine.ConfinerOven/PolygonSolution::get_IsNull()
extern void PolygonSolution_get_IsNull_m55D141117498A201C12B86647EB9B17E7BDDA801 (void);
// 0x00000388 System.String Cinemachine.ICinemachineCamera::get_Name()
// 0x00000389 System.String Cinemachine.ICinemachineCamera::get_Description()
// 0x0000038A System.Int32 Cinemachine.ICinemachineCamera::get_Priority()
// 0x0000038B System.Void Cinemachine.ICinemachineCamera::set_Priority(System.Int32)
// 0x0000038C UnityEngine.Transform Cinemachine.ICinemachineCamera::get_LookAt()
// 0x0000038D System.Void Cinemachine.ICinemachineCamera::set_LookAt(UnityEngine.Transform)
// 0x0000038E UnityEngine.Transform Cinemachine.ICinemachineCamera::get_Follow()
// 0x0000038F System.Void Cinemachine.ICinemachineCamera::set_Follow(UnityEngine.Transform)
// 0x00000390 Cinemachine.CameraState Cinemachine.ICinemachineCamera::get_State()
// 0x00000391 UnityEngine.GameObject Cinemachine.ICinemachineCamera::get_VirtualCameraGameObject()
// 0x00000392 System.Boolean Cinemachine.ICinemachineCamera::get_IsValid()
// 0x00000393 Cinemachine.ICinemachineCamera Cinemachine.ICinemachineCamera::get_ParentCamera()
// 0x00000394 System.Boolean Cinemachine.ICinemachineCamera::IsLiveChild(Cinemachine.ICinemachineCamera,System.Boolean)
// 0x00000395 System.Void Cinemachine.ICinemachineCamera::UpdateCameraState(UnityEngine.Vector3,System.Single)
// 0x00000396 System.Void Cinemachine.ICinemachineCamera::InternalUpdateCameraState(UnityEngine.Vector3,System.Single)
// 0x00000397 System.Void Cinemachine.ICinemachineCamera::OnTransitionFromCamera(Cinemachine.ICinemachineCamera,UnityEngine.Vector3,System.Single)
// 0x00000398 System.Void Cinemachine.ICinemachineCamera::OnTargetObjectWarped(UnityEngine.Transform,UnityEngine.Vector3)
// 0x00000399 System.Boolean Cinemachine.LensSettings::get_Orthographic()
extern void LensSettings_get_Orthographic_m198D9052494017EEE832066A64F81ADD2B75C17D (void);
// 0x0000039A System.Void Cinemachine.LensSettings::set_Orthographic(System.Boolean)
extern void LensSettings_set_Orthographic_mDD2CDEBC91693B4A25E92DB0DEDE8698DD115EB6 (void);
// 0x0000039B UnityEngine.Vector2 Cinemachine.LensSettings::get_SensorSize()
extern void LensSettings_get_SensorSize_m1D1F2A7226C400F0062842864C67608E2DCBBD4B (void);
// 0x0000039C System.Void Cinemachine.LensSettings::set_SensorSize(UnityEngine.Vector2)
extern void LensSettings_set_SensorSize_mD43BCB83C6FBE95F48DAD8993E8DA53927F5CA04 (void);
// 0x0000039D System.Single Cinemachine.LensSettings::get_Aspect()
extern void LensSettings_get_Aspect_m47C88E8BFBCFA1394AF0259DF528CCC4786A2555 (void);
// 0x0000039E System.Boolean Cinemachine.LensSettings::get_IsPhysicalCamera()
extern void LensSettings_get_IsPhysicalCamera_m6AD402DF51FEFD48DC4813C53C10D034B16F9386 (void);
// 0x0000039F System.Void Cinemachine.LensSettings::set_IsPhysicalCamera(System.Boolean)
extern void LensSettings_set_IsPhysicalCamera_m818868B1BD841C9DBD8B9DCBBAE69A14D099D5E9 (void);
// 0x000003A0 Cinemachine.LensSettings Cinemachine.LensSettings::FromCamera(UnityEngine.Camera)
extern void LensSettings_FromCamera_m3A279A7BFABD4F4D4ADA789C44122E5608626693 (void);
// 0x000003A1 System.Void Cinemachine.LensSettings::SnapshotCameraReadOnlyProperties(UnityEngine.Camera)
extern void LensSettings_SnapshotCameraReadOnlyProperties_mBFE20278097AE1A1D51AC775347F47AB84E32F4D (void);
// 0x000003A2 System.Void Cinemachine.LensSettings::SnapshotCameraReadOnlyProperties(Cinemachine.LensSettings&)
extern void LensSettings_SnapshotCameraReadOnlyProperties_mEF4FCF04C9AE103AAFE9CDB4531154896BFD660E (void);
// 0x000003A3 System.Void Cinemachine.LensSettings::.ctor(System.Single,System.Single,System.Single,System.Single,System.Single)
extern void LensSettings__ctor_mA347110802F0FE6F33CD4D92461CADF6B1B93F59 (void);
// 0x000003A4 Cinemachine.LensSettings Cinemachine.LensSettings::Lerp(Cinemachine.LensSettings,Cinemachine.LensSettings,System.Single)
extern void LensSettings_Lerp_mC7E1E297C8E3750B6997616993572DDBCEDDC590 (void);
// 0x000003A5 System.Void Cinemachine.LensSettings::Validate()
extern void LensSettings_Validate_m2C9ABCED7FE02F0C7B575CFADB77493DEEA03B01 (void);
// 0x000003A6 System.Void Cinemachine.LensSettings::.cctor()
extern void LensSettings__cctor_m85AA3809A95F2865AA4677633CBDF7EB7917CF6E (void);
// 0x000003A7 UnityEngine.Vector3 Cinemachine.NoiseSettings::GetCombinedFilterResults(Cinemachine.NoiseSettings/TransformNoiseParams[],System.Single,UnityEngine.Vector3)
extern void NoiseSettings_GetCombinedFilterResults_m5F4C940ACF6542F9F08BDE0D4E3E1622AAABD739 (void);
// 0x000003A8 System.Single Cinemachine.NoiseSettings::get_SignalDuration()
extern void NoiseSettings_get_SignalDuration_mC7F19EFCFBABC1DA89A8B227811C88AA1BE7A40F (void);
// 0x000003A9 System.Void Cinemachine.NoiseSettings::GetSignal(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void NoiseSettings_GetSignal_m1C4C44CCE78C53D1EECE65EDD5077449964DE65E (void);
// 0x000003AA System.Void Cinemachine.NoiseSettings::.ctor()
extern void NoiseSettings__ctor_mD5112AED5CE668F76D5260B5BB9FD0104A7756C6 (void);
// 0x000003AB System.Single Cinemachine.NoiseSettings/NoiseParams::GetValueAt(System.Single,System.Single)
extern void NoiseParams_GetValueAt_mDDE2123C634874F162FAFAD1270E443FD9C13F7B (void);
// 0x000003AC UnityEngine.Vector3 Cinemachine.NoiseSettings/TransformNoiseParams::GetValueAt(System.Single,UnityEngine.Vector3)
extern void TransformNoiseParams_GetValueAt_m541D9F21055D8F6F727E24C5A3D2C26B7044D4F0 (void);
// 0x000003AD System.Void Cinemachine.RuntimeUtility::DestroyObject(UnityEngine.Object)
extern void RuntimeUtility_DestroyObject_mEEBC4EE2A429B4E4D00EC07BA62044C9FF8E8A18 (void);
// 0x000003AE System.Boolean Cinemachine.RuntimeUtility::IsPrefab(UnityEngine.GameObject)
extern void RuntimeUtility_IsPrefab_m98872C6B07F84AAF4C91F87F490562A4935ACF9D (void);
// 0x000003AF System.Boolean Cinemachine.RuntimeUtility::RaycastIgnoreTag(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32,System.String&)
extern void RuntimeUtility_RaycastIgnoreTag_m7532A8E173E44D2952124031C7973B87FE322B49 (void);
// 0x000003B0 System.Boolean Cinemachine.RuntimeUtility::SphereCastIgnoreTag(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,System.String&)
extern void RuntimeUtility_SphereCastIgnoreTag_m0C12A9B6676E60CB82EF263F23F86296456DC4BD (void);
// 0x000003B1 UnityEngine.SphereCollider Cinemachine.RuntimeUtility::GetScratchCollider()
extern void RuntimeUtility_GetScratchCollider_m91C5D854E7110AF1A5F570FFC246BB18181B8AFA (void);
// 0x000003B2 System.Void Cinemachine.RuntimeUtility::DestroyScratchCollider()
extern void RuntimeUtility_DestroyScratchCollider_m4364DE7BEF85A175A66261E184D0A58BA4F62D5C (void);
// 0x000003B3 UnityEngine.AnimationCurve Cinemachine.RuntimeUtility::NormalizeCurve(UnityEngine.AnimationCurve,System.Boolean,System.Boolean)
extern void RuntimeUtility_NormalizeCurve_m0EAB5ED8DAFF226807EDD5A2D0B3876FBB5263F1 (void);
// 0x000003B4 System.Void Cinemachine.RuntimeUtility::.cctor()
extern void RuntimeUtility__cctor_m7D751C759A76C02DBF3757EB06AFB5DB01914E0F (void);
// 0x000003B5 System.Single Cinemachine.ISignalSource6D::get_SignalDuration()
// 0x000003B6 System.Void Cinemachine.ISignalSource6D::GetSignal(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
// 0x000003B7 System.Single Cinemachine.SignalSourceAsset::get_SignalDuration()
// 0x000003B8 System.Void Cinemachine.SignalSourceAsset::GetSignal(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
// 0x000003B9 System.Void Cinemachine.SignalSourceAsset::.ctor()
extern void SignalSourceAsset__ctor_mC0043F9E30AE0206A5124C695C6F511EA43B83DA (void);
// 0x000003BA Cinemachine.TargetPositionCache/Mode Cinemachine.TargetPositionCache::get_CacheMode()
extern void TargetPositionCache_get_CacheMode_m01290EDF84037C27C4ACDD03ED9F0E0D16215AD0 (void);
// 0x000003BB System.Void Cinemachine.TargetPositionCache::set_CacheMode(Cinemachine.TargetPositionCache/Mode)
extern void TargetPositionCache_set_CacheMode_mF13D07A7F20356D8F2631BF7C238AF542CA2DE77 (void);
// 0x000003BC System.Boolean Cinemachine.TargetPositionCache::get_IsRecording()
extern void TargetPositionCache_get_IsRecording_m5B0EF7A6DC7DCC938FA7600255944B1353CC982F (void);
// 0x000003BD System.Boolean Cinemachine.TargetPositionCache::get_CurrentPlaybackTimeValid()
extern void TargetPositionCache_get_CurrentPlaybackTimeValid_m93FCE647A563303A9390A2409B58FF9702CC9F09 (void);
// 0x000003BE System.Boolean Cinemachine.TargetPositionCache::get_IsEmpty()
extern void TargetPositionCache_get_IsEmpty_mDBD8C0F5EAE840127C82EC96E682119CE1E4D67C (void);
// 0x000003BF Cinemachine.TargetPositionCache/TimeRange Cinemachine.TargetPositionCache::get_CacheTimeRange()
extern void TargetPositionCache_get_CacheTimeRange_m3594D3F35DCD95F2A0A1133E871343D3AD37EC5A (void);
// 0x000003C0 System.Boolean Cinemachine.TargetPositionCache::get_HasCurrentTime()
extern void TargetPositionCache_get_HasCurrentTime_mCB98DBD74426664E1D6E7F5F5DEE8AA2D38352C9 (void);
// 0x000003C1 System.Void Cinemachine.TargetPositionCache::ClearCache()
extern void TargetPositionCache_ClearCache_m8A8D824AC7D0D548512228B7934901D36091BE63 (void);
// 0x000003C2 System.Void Cinemachine.TargetPositionCache::CreatePlaybackCurves()
extern void TargetPositionCache_CreatePlaybackCurves_m61C5C2979270EE52949A1FC610A1C1DE53E99FD3 (void);
// 0x000003C3 UnityEngine.Vector3 Cinemachine.TargetPositionCache::GetTargetPosition(UnityEngine.Transform)
extern void TargetPositionCache_GetTargetPosition_m86454680691707EEE6E70984ED39E00821B60B2E (void);
// 0x000003C4 UnityEngine.Quaternion Cinemachine.TargetPositionCache::GetTargetRotation(UnityEngine.Transform)
extern void TargetPositionCache_GetTargetRotation_m882B2051C458EB18BCF332AFB00112484F31C9EF (void);
// 0x000003C5 System.Void Cinemachine.TargetPositionCache::.ctor()
extern void TargetPositionCache__ctor_m2CCD00DBBBA03A2852EDBD724FC1A386F9F9B814 (void);
// 0x000003C6 System.Int32 Cinemachine.TargetPositionCache/CacheCurve::get_Count()
extern void CacheCurve_get_Count_mCC8846F251F4F9C02B89FA7A6FE14FCA89C74DBD (void);
// 0x000003C7 System.Void Cinemachine.TargetPositionCache/CacheCurve::.ctor(System.Single,System.Single,System.Single)
extern void CacheCurve__ctor_m06133A4F30CD9E04416FDF29D25BC8110A86E6A8 (void);
// 0x000003C8 System.Void Cinemachine.TargetPositionCache/CacheCurve::Add(Cinemachine.TargetPositionCache/CacheCurve/Item)
extern void CacheCurve_Add_mF90F9432E39BD237C462FF82B83DD3DF1A48A0BA (void);
// 0x000003C9 System.Void Cinemachine.TargetPositionCache/CacheCurve::AddUntil(Cinemachine.TargetPositionCache/CacheCurve/Item,System.Single,System.Boolean)
extern void CacheCurve_AddUntil_mBF186D5A7181FBC89C6870525C47887BBB8DD571 (void);
// 0x000003CA Cinemachine.TargetPositionCache/CacheCurve/Item Cinemachine.TargetPositionCache/CacheCurve::Evaluate(System.Single)
extern void CacheCurve_Evaluate_m5C353080800042A73C91D598EC009771A77ABB8C (void);
// 0x000003CB Cinemachine.TargetPositionCache/CacheCurve/Item Cinemachine.TargetPositionCache/CacheCurve/Item::Lerp(Cinemachine.TargetPositionCache/CacheCurve/Item,Cinemachine.TargetPositionCache/CacheCurve/Item,System.Single)
extern void Item_Lerp_mE80C14D381D6951A33443B59D2B4439911D98363 (void);
// 0x000003CC Cinemachine.TargetPositionCache/CacheCurve/Item Cinemachine.TargetPositionCache/CacheCurve/Item::get_Empty()
extern void Item_get_Empty_m09EC22FCDCCA6A75C1FFE623F2AE7789D55A7E2B (void);
// 0x000003CD System.Void Cinemachine.TargetPositionCache/CacheEntry::AddRawItem(System.Single,System.Boolean,UnityEngine.Transform)
extern void CacheEntry_AddRawItem_mB7B07DE2B9D0E5152DFD3788547F22FF4C2688D9 (void);
// 0x000003CE System.Void Cinemachine.TargetPositionCache/CacheEntry::CreateCurves()
extern void CacheEntry_CreateCurves_m01DAEFFC5B38FB88A32CD9738CCBD0F7E168F385 (void);
// 0x000003CF System.Void Cinemachine.TargetPositionCache/CacheEntry::.ctor()
extern void CacheEntry__ctor_mED77E8B3231C201C296CBFE39CA995FD45AC1EFA (void);
// 0x000003D0 System.Boolean Cinemachine.TargetPositionCache/TimeRange::get_IsEmpty()
extern void TimeRange_get_IsEmpty_mB51E1F97CF61DD08C502385B2AFC72C6A7F68BCB (void);
// 0x000003D1 System.Boolean Cinemachine.TargetPositionCache/TimeRange::Contains(System.Single)
extern void TimeRange_Contains_mF974E5E36DD20BD63B5B09564B09800AB30CF6B6 (void);
// 0x000003D2 Cinemachine.TargetPositionCache/TimeRange Cinemachine.TargetPositionCache/TimeRange::get_Empty()
extern void TimeRange_get_Empty_m5DA39A592AAFE2830C9EBED3C119E1CABE417D10 (void);
// 0x000003D3 System.Void Cinemachine.TargetPositionCache/TimeRange::Include(System.Single)
extern void TimeRange_Include_mC1E0A1214F4C3A6FC7F15728A3EB9C398B96F1B2 (void);
// 0x000003D4 System.Void Cinemachine.UpdateTracker::InitializeModule()
extern void UpdateTracker_InitializeModule_mE6A6DDA8C4EF428E778BD799277089B43F3E933B (void);
// 0x000003D5 System.Void Cinemachine.UpdateTracker::UpdateTargets(Cinemachine.UpdateTracker/UpdateClock)
extern void UpdateTracker_UpdateTargets_mA2095E5739702F84976027C24FA0B8AC37562BA9 (void);
// 0x000003D6 Cinemachine.UpdateTracker/UpdateClock Cinemachine.UpdateTracker::GetPreferredUpdate(UnityEngine.Transform)
extern void UpdateTracker_GetPreferredUpdate_mCA810B7D3B87B3A511764993265DFDC2F246C3E8 (void);
// 0x000003D7 System.Void Cinemachine.UpdateTracker::OnUpdate(Cinemachine.UpdateTracker/UpdateClock)
extern void UpdateTracker_OnUpdate_m89A8FE83B9271FE8D7F03CA25F20DE6BD06D489A (void);
// 0x000003D8 System.Void Cinemachine.UpdateTracker::.ctor()
extern void UpdateTracker__ctor_mBE6F79EBEEE48D9C75839E7CF7952105F4E1C6C1 (void);
// 0x000003D9 System.Void Cinemachine.UpdateTracker::.cctor()
extern void UpdateTracker__cctor_mE2A4318EC8695B882C24BB0252A9A7FB46581637 (void);
// 0x000003DA Cinemachine.UpdateTracker/UpdateClock Cinemachine.UpdateTracker/UpdateStatus::get_PreferredUpdate()
extern void UpdateStatus_get_PreferredUpdate_m31E0BC5E8BDA920C47D2D04EE51BB8060BAE3FD9 (void);
// 0x000003DB System.Void Cinemachine.UpdateTracker/UpdateStatus::set_PreferredUpdate(Cinemachine.UpdateTracker/UpdateClock)
extern void UpdateStatus_set_PreferredUpdate_mD1E743C821F5D01F8643FBB4C11E274CC266D91A (void);
// 0x000003DC System.Void Cinemachine.UpdateTracker/UpdateStatus::.ctor(System.Int32,UnityEngine.Matrix4x4)
extern void UpdateStatus__ctor_mB7FBA4B732BDB2DCDDCF719176D7FDCB1C8D3EA1 (void);
// 0x000003DD System.Void Cinemachine.UpdateTracker/UpdateStatus::OnUpdate(System.Int32,Cinemachine.UpdateTracker/UpdateClock,UnityEngine.Matrix4x4)
extern void UpdateStatus_OnUpdate_m1F8A371233566E0E68A29136C571951AC06C9CC4 (void);
// 0x000003DE System.Single Cinemachine.CinemachineInputProvider::GetAxisValue(System.Int32)
extern void CinemachineInputProvider_GetAxisValue_mA8EE8963FE6381D4BEA6AD72D32C23EDAB211056 (void);
// 0x000003DF UnityEngine.InputSystem.InputAction Cinemachine.CinemachineInputProvider::ResolveForPlayer(System.Int32,UnityEngine.InputSystem.InputActionReference)
extern void CinemachineInputProvider_ResolveForPlayer_mBC7763E40BD8E978D1C030402B8A47FF13A668A9 (void);
// 0x000003E0 System.Void Cinemachine.CinemachineInputProvider::OnDisable()
extern void CinemachineInputProvider_OnDisable_m64B4EAD22EC11413F99123C76E3BC2AC59C1FE7A (void);
// 0x000003E1 System.Void Cinemachine.CinemachineInputProvider::.ctor()
extern void CinemachineInputProvider__ctor_mD407437EB55A2B44CCBC5212077431E81B122A66 (void);
// 0x000003E2 UnityEngine.InputSystem.InputAction Cinemachine.CinemachineInputProvider::<ResolveForPlayer>g__GetFirstMatch|7_0(UnityEngine.InputSystem.Users.InputUser&,UnityEngine.InputSystem.InputActionReference)
extern void CinemachineInputProvider_U3CResolveForPlayerU3Eg__GetFirstMatchU7C7_0_m3ADD855E00E795B9BE33D798EA2DD3D8E09EB7B8 (void);
// 0x000003E3 System.Void Cinemachine.CinemachineInputProvider/<>c__DisplayClass7_0::.ctor()
extern void U3CU3Ec__DisplayClass7_0__ctor_mF98AC1D50BE3684CFF345BF9C2309B14C50C544E (void);
// 0x000003E4 System.Boolean Cinemachine.CinemachineInputProvider/<>c__DisplayClass7_0::<ResolveForPlayer>b__1(UnityEngine.InputSystem.InputAction)
extern void U3CU3Ec__DisplayClass7_0_U3CResolveForPlayerU3Eb__1_m2258459485E7ECD621E7DF2486752AF5105C5526 (void);
// 0x000003E5 System.Boolean Cinemachine.CinemachineTriggerAction::Filter(UnityEngine.GameObject)
extern void CinemachineTriggerAction_Filter_mCB26261B2A07B948A5ECDC34528692F0576CEF3F (void);
// 0x000003E6 System.Void Cinemachine.CinemachineTriggerAction::InternalDoTriggerEnter(UnityEngine.GameObject)
extern void CinemachineTriggerAction_InternalDoTriggerEnter_mE10EA16EC009B9A98D636DC641FAE6C98E10A50E (void);
// 0x000003E7 System.Void Cinemachine.CinemachineTriggerAction::InternalDoTriggerExit(UnityEngine.GameObject)
extern void CinemachineTriggerAction_InternalDoTriggerExit_m551E998CCCAD85A67717E915FB9EB5EDBA20F3CA (void);
// 0x000003E8 System.Void Cinemachine.CinemachineTriggerAction::OnTriggerEnter(UnityEngine.Collider)
extern void CinemachineTriggerAction_OnTriggerEnter_m9025492203BE275D711E0A8B35296E36CC22C7D7 (void);
// 0x000003E9 System.Void Cinemachine.CinemachineTriggerAction::OnTriggerExit(UnityEngine.Collider)
extern void CinemachineTriggerAction_OnTriggerExit_mE43C1B91D9A3BD5883790F5A3BA86D5297ADC9F6 (void);
// 0x000003EA System.Void Cinemachine.CinemachineTriggerAction::OnCollisionEnter(UnityEngine.Collision)
extern void CinemachineTriggerAction_OnCollisionEnter_mB33BB207F8E16E38E54B77EDE8B9716C1B1945CF (void);
// 0x000003EB System.Void Cinemachine.CinemachineTriggerAction::OnCollisionExit(UnityEngine.Collision)
extern void CinemachineTriggerAction_OnCollisionExit_mB5D84FA567A5E2C652785CFEDDC19952F4DDEE57 (void);
// 0x000003EC System.Void Cinemachine.CinemachineTriggerAction::OnTriggerEnter2D(UnityEngine.Collider2D)
extern void CinemachineTriggerAction_OnTriggerEnter2D_mD951D1E1A931BFAFB451F31F5A2EE206670A3336 (void);
// 0x000003ED System.Void Cinemachine.CinemachineTriggerAction::OnTriggerExit2D(UnityEngine.Collider2D)
extern void CinemachineTriggerAction_OnTriggerExit2D_mD813BBBF15C31020ACC81895354D2773DDBFEB91 (void);
// 0x000003EE System.Void Cinemachine.CinemachineTriggerAction::OnCollisionEnter2D(UnityEngine.Collision2D)
extern void CinemachineTriggerAction_OnCollisionEnter2D_mB267C61D975BC34CA85A89DE04175BAD29F08A5F (void);
// 0x000003EF System.Void Cinemachine.CinemachineTriggerAction::OnCollisionExit2D(UnityEngine.Collision2D)
extern void CinemachineTriggerAction_OnCollisionExit2D_m8138F3F5653B6019A914598CD8A5AD4F8076BC85 (void);
// 0x000003F0 System.Void Cinemachine.CinemachineTriggerAction::OnEnable()
extern void CinemachineTriggerAction_OnEnable_m283DC186365A62AB5D92E6824A697C95C842E85E (void);
// 0x000003F1 System.Void Cinemachine.CinemachineTriggerAction::.ctor()
extern void CinemachineTriggerAction__ctor_m750E55C64E2E3A0E9D5A3ABD6F7567974CCFDC06 (void);
// 0x000003F2 System.Void Cinemachine.CinemachineTriggerAction/ActionSettings::.ctor(Cinemachine.CinemachineTriggerAction/ActionSettings/Mode)
extern void ActionSettings__ctor_m1713D8512FDCD108FF5F41EE681F79311452DB49 (void);
// 0x000003F3 System.Void Cinemachine.CinemachineTriggerAction/ActionSettings::Invoke()
extern void ActionSettings_Invoke_mF55BAA376882270B52604D6E67EC1EF1F15F8A01 (void);
// 0x000003F4 System.Void Cinemachine.CinemachineTriggerAction/ActionSettings/TriggerEvent::.ctor()
extern void TriggerEvent__ctor_mA394074CF7871DB4571DB21160835BEE47169C9B (void);
// 0x000003F5 System.Void Cinemachine.GroupWeightManipulator::Start()
extern void GroupWeightManipulator_Start_mF07981B10129DBE29CFEE1F9ADE65BBE6DD9C9A4 (void);
// 0x000003F6 System.Void Cinemachine.GroupWeightManipulator::OnValidate()
extern void GroupWeightManipulator_OnValidate_m4B99CE8E84D3798C03521FB3B5DEDE9C2D4A1DC3 (void);
// 0x000003F7 System.Void Cinemachine.GroupWeightManipulator::Update()
extern void GroupWeightManipulator_Update_m3E00D3BEE2D51348E7648A78B8F74CC90349043C (void);
// 0x000003F8 System.Void Cinemachine.GroupWeightManipulator::UpdateWeights()
extern void GroupWeightManipulator_UpdateWeights_mE2A29DD6CF0FC460CFDD84E71FAD6372C27F47D4 (void);
// 0x000003F9 System.Void Cinemachine.GroupWeightManipulator::.ctor()
extern void GroupWeightManipulator__ctor_m4B4614CE1F0E71BAD1F5335351198F058E0C5008 (void);
// 0x000003FA System.Void Cinemachine.CinemachineCollisionImpulseSource::Start()
extern void CinemachineCollisionImpulseSource_Start_mD780C5C503490C38F898548C6FB0A7D5C623AF1D (void);
// 0x000003FB System.Void Cinemachine.CinemachineCollisionImpulseSource::OnEnable()
extern void CinemachineCollisionImpulseSource_OnEnable_m10190FA2B9F936C0C6FB898DF37334045A8E905A (void);
// 0x000003FC System.Void Cinemachine.CinemachineCollisionImpulseSource::OnCollisionEnter(UnityEngine.Collision)
extern void CinemachineCollisionImpulseSource_OnCollisionEnter_m9732E95ACF98DEA64FED280AF703213D0F83CAE6 (void);
// 0x000003FD System.Void Cinemachine.CinemachineCollisionImpulseSource::OnTriggerEnter(UnityEngine.Collider)
extern void CinemachineCollisionImpulseSource_OnTriggerEnter_m467BE3D2033373E84402CC1E1212A302EA01A25D (void);
// 0x000003FE System.Single Cinemachine.CinemachineCollisionImpulseSource::GetMassAndVelocity(UnityEngine.Collider,UnityEngine.Vector3&)
extern void CinemachineCollisionImpulseSource_GetMassAndVelocity_m2F5253142AC35C57B515057564D6ECD076D61008 (void);
// 0x000003FF System.Void Cinemachine.CinemachineCollisionImpulseSource::GenerateImpactEvent(UnityEngine.Collider,UnityEngine.Vector3)
extern void CinemachineCollisionImpulseSource_GenerateImpactEvent_mEBA5FE1D601106C72CF2AB94A7A743EB61EC74DD (void);
// 0x00000400 System.Void Cinemachine.CinemachineCollisionImpulseSource::OnCollisionEnter2D(UnityEngine.Collision2D)
extern void CinemachineCollisionImpulseSource_OnCollisionEnter2D_m454EAECFE909B7CE136DF6DCE9A120AD6F1C8236 (void);
// 0x00000401 System.Void Cinemachine.CinemachineCollisionImpulseSource::OnTriggerEnter2D(UnityEngine.Collider2D)
extern void CinemachineCollisionImpulseSource_OnTriggerEnter2D_m547EDA9A08B6FD2293E2DCC177B9B5F0C4B317FE (void);
// 0x00000402 System.Single Cinemachine.CinemachineCollisionImpulseSource::GetMassAndVelocity2D(UnityEngine.Collider2D,UnityEngine.Vector3&)
extern void CinemachineCollisionImpulseSource_GetMassAndVelocity2D_m11EA7DF2EB01937666501439E973F242EC9A7E38 (void);
// 0x00000403 System.Void Cinemachine.CinemachineCollisionImpulseSource::GenerateImpactEvent2D(UnityEngine.Collider2D,UnityEngine.Vector3)
extern void CinemachineCollisionImpulseSource_GenerateImpactEvent2D_mA36033B447E28159B7762C3FE1042D5EB2164F8B (void);
// 0x00000404 System.Void Cinemachine.CinemachineCollisionImpulseSource::.ctor()
extern void CinemachineCollisionImpulseSource__ctor_m3C8D3ED88B1665A036669198ECFE3AEA06C74DEE (void);
// 0x00000405 System.Single Cinemachine.CinemachineFixedSignal::get_SignalDuration()
extern void CinemachineFixedSignal_get_SignalDuration_m9FE84FB633C279195165B615AC7C5D82AC4624C0 (void);
// 0x00000406 System.Single Cinemachine.CinemachineFixedSignal::AxisDuration(UnityEngine.AnimationCurve)
extern void CinemachineFixedSignal_AxisDuration_m797DC90D6AFC528AF8B94DFBB98B4C53EA8C561A (void);
// 0x00000407 System.Void Cinemachine.CinemachineFixedSignal::GetSignal(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void CinemachineFixedSignal_GetSignal_mA2E81C1A66FDD3644F089E61B12A64D6272C9EB6 (void);
// 0x00000408 System.Single Cinemachine.CinemachineFixedSignal::AxisValue(UnityEngine.AnimationCurve,System.Single)
extern void CinemachineFixedSignal_AxisValue_mD7C8B430549A9BCD3968864805CA52A3385B2AF4 (void);
// 0x00000409 System.Void Cinemachine.CinemachineFixedSignal::.ctor()
extern void CinemachineFixedSignal__ctor_m6E4EB146C0D95EEE4E904171259E075DF24DC84E (void);
// 0x0000040A System.Void Cinemachine.CinemachineImpulseDefinitionPropertyAttribute::.ctor()
extern void CinemachineImpulseDefinitionPropertyAttribute__ctor_m33901999C6EBE50D4ED2C2A04BB515F97C7D7368 (void);
// 0x0000040B System.Void Cinemachine.CinemachineImpulseDefinition::OnValidate()
extern void CinemachineImpulseDefinition_OnValidate_mC9E8C5D4227DB0C7467BBDE0004C424B72C083ED (void);
// 0x0000040C System.Void Cinemachine.CinemachineImpulseDefinition::CreateStandardShapes()
extern void CinemachineImpulseDefinition_CreateStandardShapes_m0D05D8A81C5CE6473AC7F8A52BFD502FFECB2924 (void);
// 0x0000040D UnityEngine.AnimationCurve Cinemachine.CinemachineImpulseDefinition::GetStandardCurve(Cinemachine.CinemachineImpulseDefinition/ImpulseShapes)
extern void CinemachineImpulseDefinition_GetStandardCurve_mDFBFBA8FDF8DCC1BCC7F7498E1908DB4189D40E7 (void);
// 0x0000040E UnityEngine.AnimationCurve Cinemachine.CinemachineImpulseDefinition::get_ImpulseCurve()
extern void CinemachineImpulseDefinition_get_ImpulseCurve_mAA1558EAB001196015FCCE60BB8DF3200AEC5C4E (void);
// 0x0000040F System.Void Cinemachine.CinemachineImpulseDefinition::CreateEvent(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineImpulseDefinition_CreateEvent_m12158346403D3428D6DBC0D3D22DDDCF551E5B41 (void);
// 0x00000410 Cinemachine.CinemachineImpulseManager/ImpulseEvent Cinemachine.CinemachineImpulseDefinition::CreateAndReturnEvent(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineImpulseDefinition_CreateAndReturnEvent_mCBF4EF79D6393E9D33F9504D30E876AA479C22A0 (void);
// 0x00000411 Cinemachine.CinemachineImpulseManager/ImpulseEvent Cinemachine.CinemachineImpulseDefinition::LegacyCreateAndReturnEvent(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineImpulseDefinition_LegacyCreateAndReturnEvent_m5213920FF8EAF05E85F9D1D92A94E40AE04A087C (void);
// 0x00000412 System.Void Cinemachine.CinemachineImpulseDefinition::.ctor()
extern void CinemachineImpulseDefinition__ctor_m9A793CB4F91B42D9A7D38C1953A0A081A04B4045 (void);
// 0x00000413 System.Void Cinemachine.CinemachineImpulseDefinition/SignalSource::.ctor(Cinemachine.CinemachineImpulseDefinition,UnityEngine.Vector3)
extern void SignalSource__ctor_mD776DEC5B0B9B1370EC2E238B46C5AD936C9B0B5 (void);
// 0x00000414 System.Single Cinemachine.CinemachineImpulseDefinition/SignalSource::get_SignalDuration()
extern void SignalSource_get_SignalDuration_mFFE7C306FADED4391BD9545AF68ECFA6CA95B751 (void);
// 0x00000415 System.Void Cinemachine.CinemachineImpulseDefinition/SignalSource::GetSignal(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void SignalSource_GetSignal_mAB4EB25F3B71C410FDF51FA4A2115D5EEFBEA1FE (void);
// 0x00000416 System.Void Cinemachine.CinemachineImpulseDefinition/LegacySignalSource::.ctor(Cinemachine.CinemachineImpulseDefinition,UnityEngine.Vector3)
extern void LegacySignalSource__ctor_mDC721B7D0B692CDD699AB2012C53D81E0864AA8B (void);
// 0x00000417 System.Single Cinemachine.CinemachineImpulseDefinition/LegacySignalSource::get_SignalDuration()
extern void LegacySignalSource_get_SignalDuration_mAEA4137DACAED4BA10E1161309A87C8305904373 (void);
// 0x00000418 System.Void Cinemachine.CinemachineImpulseDefinition/LegacySignalSource::GetSignal(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void LegacySignalSource_GetSignal_mC5FAAE1D894534894FE2312D28EE5314919C6D34 (void);
// 0x00000419 System.Void Cinemachine.CinemachineImpulseListener::Reset()
extern void CinemachineImpulseListener_Reset_m7B09DF77C46498F557E7DFD9342ACE488AE9FCF6 (void);
// 0x0000041A System.Void Cinemachine.CinemachineImpulseListener::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachineImpulseListener_PostPipelineStageCallback_mCEF55A67F8286EA75D6B8930474DFD4F70B6F315 (void);
// 0x0000041B System.Void Cinemachine.CinemachineImpulseListener::.ctor()
extern void CinemachineImpulseListener__ctor_m0C42D66C35F6EC6323B195B56B5F4DD2B287CB5A (void);
// 0x0000041C System.Void Cinemachine.CinemachineImpulseListener/ImpulseReaction::ReSeed()
extern void ImpulseReaction_ReSeed_m0E5974B5A368FE95D4137FB5A87BF71699535DB5 (void);
// 0x0000041D System.Boolean Cinemachine.CinemachineImpulseListener/ImpulseReaction::GetReaction(System.Single,UnityEngine.Vector3,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void ImpulseReaction_GetReaction_mF1A9A410B1F05368B416656512188311E44E8CC3 (void);
// 0x0000041E System.Void Cinemachine.CinemachineImpulseEnvelopePropertyAttribute::.ctor()
extern void CinemachineImpulseEnvelopePropertyAttribute__ctor_m62808EE32019CC07FAB95B0A2DB0E20A61D5DDF5 (void);
// 0x0000041F System.Void Cinemachine.CinemachineImpulseChannelPropertyAttribute::.ctor()
extern void CinemachineImpulseChannelPropertyAttribute__ctor_m26E7D8A1C8D42789392B0CBC9BF4755CA01D2E49 (void);
// 0x00000420 System.Void Cinemachine.CinemachineImpulseManager::.ctor()
extern void CinemachineImpulseManager__ctor_m29FD2A34BDDE38151BEEADBEDE5A966E91FFC143 (void);
// 0x00000421 Cinemachine.CinemachineImpulseManager Cinemachine.CinemachineImpulseManager::get_Instance()
extern void CinemachineImpulseManager_get_Instance_mCBBB7EE190D29704E4F44241A50BC2F6F23EB179 (void);
// 0x00000422 System.Void Cinemachine.CinemachineImpulseManager::InitializeModule()
extern void CinemachineImpulseManager_InitializeModule_m7383E0726196C358A3DBC6574F9082010E318FEE (void);
// 0x00000423 System.Single Cinemachine.CinemachineImpulseManager::EvaluateDissipationScale(System.Single,System.Single)
extern void CinemachineImpulseManager_EvaluateDissipationScale_m0913FA74400EE4645D1F48867FED336DD6C03239 (void);
// 0x00000424 System.Boolean Cinemachine.CinemachineImpulseManager::GetImpulseAt(UnityEngine.Vector3,System.Boolean,System.Int32,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void CinemachineImpulseManager_GetImpulseAt_m60503248A6E51D9CB74F28148A982D8FA5C57D04 (void);
// 0x00000425 System.Single Cinemachine.CinemachineImpulseManager::get_CurrentTime()
extern void CinemachineImpulseManager_get_CurrentTime_m67D3ABCDB387670ECEF30D37A8E3CBDA5EB530F4 (void);
// 0x00000426 Cinemachine.CinemachineImpulseManager/ImpulseEvent Cinemachine.CinemachineImpulseManager::NewImpulseEvent()
extern void CinemachineImpulseManager_NewImpulseEvent_m10EB7672F67FB33CD98758F3CED27E168241508C (void);
// 0x00000427 System.Void Cinemachine.CinemachineImpulseManager::AddImpulseEvent(Cinemachine.CinemachineImpulseManager/ImpulseEvent)
extern void CinemachineImpulseManager_AddImpulseEvent_m248DA95ECD499D4B501E282B5886389C02F0D223 (void);
// 0x00000428 System.Void Cinemachine.CinemachineImpulseManager::Clear()
extern void CinemachineImpulseManager_Clear_mA6DC571A238EB0C4C7B07C06D2263B5743FB82BA (void);
// 0x00000429 Cinemachine.CinemachineImpulseManager/EnvelopeDefinition Cinemachine.CinemachineImpulseManager/EnvelopeDefinition::Default()
extern void EnvelopeDefinition_Default_m57F738873D011A83AAC0BFF7DCED3A74BAC13E06 (void);
// 0x0000042A System.Single Cinemachine.CinemachineImpulseManager/EnvelopeDefinition::get_Duration()
extern void EnvelopeDefinition_get_Duration_m3CF863DC6B71344BE096AD9CBFC6C86AD2FF634C (void);
// 0x0000042B System.Single Cinemachine.CinemachineImpulseManager/EnvelopeDefinition::GetValueAt(System.Single)
extern void EnvelopeDefinition_GetValueAt_mAF39D22894C1B21FDA3A5D7C50AF4AEB393BEE6D (void);
// 0x0000042C System.Void Cinemachine.CinemachineImpulseManager/EnvelopeDefinition::ChangeStopTime(System.Single,System.Boolean)
extern void EnvelopeDefinition_ChangeStopTime_mFE038CADEFEBC54B1262B69139C2C8C209CE1196 (void);
// 0x0000042D System.Void Cinemachine.CinemachineImpulseManager/EnvelopeDefinition::Clear()
extern void EnvelopeDefinition_Clear_m70B8BFD4F76297739F4D6EC3044D46832196D4EF (void);
// 0x0000042E System.Void Cinemachine.CinemachineImpulseManager/EnvelopeDefinition::Validate()
extern void EnvelopeDefinition_Validate_m2DE55071F0E90DAF09281B7735FD96A93D278C37 (void);
// 0x0000042F System.Boolean Cinemachine.CinemachineImpulseManager/ImpulseEvent::get_Expired()
extern void ImpulseEvent_get_Expired_mCA721141A1BF8EABF33B58335F3BF081EE517160 (void);
// 0x00000430 System.Void Cinemachine.CinemachineImpulseManager/ImpulseEvent::Cancel(System.Single,System.Boolean)
extern void ImpulseEvent_Cancel_m9B2DA20770168C09A4245378190B67A574444BC2 (void);
// 0x00000431 System.Single Cinemachine.CinemachineImpulseManager/ImpulseEvent::DistanceDecay(System.Single)
extern void ImpulseEvent_DistanceDecay_mD470E344C6B95A38CCFFD8EA0869E98D30141C16 (void);
// 0x00000432 System.Boolean Cinemachine.CinemachineImpulseManager/ImpulseEvent::GetDecayedSignal(UnityEngine.Vector3,System.Boolean,UnityEngine.Vector3&,UnityEngine.Quaternion&)
extern void ImpulseEvent_GetDecayedSignal_m0A606723EF1C6867ECAC33BBBDCE4826BE5AB78F (void);
// 0x00000433 System.Void Cinemachine.CinemachineImpulseManager/ImpulseEvent::Clear()
extern void ImpulseEvent_Clear_mBE18EB8EBB0F8F266AB084B44307155CE823C17A (void);
// 0x00000434 System.Void Cinemachine.CinemachineImpulseManager/ImpulseEvent::.ctor()
extern void ImpulseEvent__ctor_mC5F7C05F235EB65CED1FF6E0FBC6C83D9BCBF81B (void);
// 0x00000435 System.Void Cinemachine.CinemachineImpulseSource::OnValidate()
extern void CinemachineImpulseSource_OnValidate_m5AF490404689D1FD50FA16C1A3621FF85B4E9D28 (void);
// 0x00000436 System.Void Cinemachine.CinemachineImpulseSource::Reset()
extern void CinemachineImpulseSource_Reset_m6D1F0F55B77C67D7D03992E820A189D52DF9EDB9 (void);
// 0x00000437 System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulseAtPositionWithVelocity(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineImpulseSource_GenerateImpulseAtPositionWithVelocity_m943E5F58B6A439998A9C427F42FEBF61094F82C1 (void);
// 0x00000438 System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulseWithVelocity(UnityEngine.Vector3)
extern void CinemachineImpulseSource_GenerateImpulseWithVelocity_mF16A92675C0D88821A81722EE1CCEA704F17258F (void);
// 0x00000439 System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulseWithForce(System.Single)
extern void CinemachineImpulseSource_GenerateImpulseWithForce_mB82D805193E1A63D2EBAE187F450E110FDFB2780 (void);
// 0x0000043A System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulse()
extern void CinemachineImpulseSource_GenerateImpulse_m0AE0716BE48B2DE1186BF9757917BB0BD8E9999A (void);
// 0x0000043B System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulseAt(UnityEngine.Vector3,UnityEngine.Vector3)
extern void CinemachineImpulseSource_GenerateImpulseAt_mC9E45E6ABBB7111E7553994C17F3AA71A3E025A8 (void);
// 0x0000043C System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulse(UnityEngine.Vector3)
extern void CinemachineImpulseSource_GenerateImpulse_m700534622EE72CDDAFEBEFAB2E60F88654660B6A (void);
// 0x0000043D System.Void Cinemachine.CinemachineImpulseSource::GenerateImpulse(System.Single)
extern void CinemachineImpulseSource_GenerateImpulse_m64F06385534151C880B380839786BC1C1B59D5C6 (void);
// 0x0000043E System.Void Cinemachine.CinemachineImpulseSource::.ctor()
extern void CinemachineImpulseSource__ctor_mAC87ADE7ACFB4405EE58B0C6AA0AE5D93EA3915E (void);
// 0x0000043F System.Void Cinemachine.CinemachineIndependentImpulseListener::Reset()
extern void CinemachineIndependentImpulseListener_Reset_m73C4B1F95F2A15991E711CC74A4AEE221108AF29 (void);
// 0x00000440 System.Void Cinemachine.CinemachineIndependentImpulseListener::OnEnable()
extern void CinemachineIndependentImpulseListener_OnEnable_m5439B23A0659AC177EB940684D9C1D17BC03F470 (void);
// 0x00000441 System.Void Cinemachine.CinemachineIndependentImpulseListener::Update()
extern void CinemachineIndependentImpulseListener_Update_m6869DA3648BEE15F08D80FBC4EEF6842D7023C6D (void);
// 0x00000442 System.Void Cinemachine.CinemachineIndependentImpulseListener::LateUpdate()
extern void CinemachineIndependentImpulseListener_LateUpdate_m7F9FFDFE7E2C8ABFF7DC8D9B723DAC97B966ACDC (void);
// 0x00000443 System.Void Cinemachine.CinemachineIndependentImpulseListener::.ctor()
extern void CinemachineIndependentImpulseListener__ctor_mE2B8C7909A17100C4E1F08A3B2889187C7E58261 (void);
// 0x00000444 System.Void Cinemachine.ClipperLib/DoublePoint::.ctor(System.Double,System.Double)
extern void DoublePoint__ctor_m87B0A4A8419F7E7608DF989FFBD0E92295A0A72F (void);
// 0x00000445 System.Void Cinemachine.ClipperLib/DoublePoint::.ctor(Cinemachine.ClipperLib/DoublePoint)
extern void DoublePoint__ctor_m1DD41CFCE7D02EDD548BC15B67AF3A5FF8919DAB (void);
// 0x00000446 System.Void Cinemachine.ClipperLib/DoublePoint::.ctor(Cinemachine.ClipperLib/IntPoint)
extern void DoublePoint__ctor_mC072F91863E140D4884B37BD4072B89E6ACAD877 (void);
// 0x00000447 System.Void Cinemachine.ClipperLib/PolyTree::Clear()
extern void PolyTree_Clear_m9590AC2AB10E2126CDA65C9824B31FC88E191185 (void);
// 0x00000448 Cinemachine.ClipperLib/PolyNode Cinemachine.ClipperLib/PolyTree::GetFirst()
extern void PolyTree_GetFirst_mACA8C473F4D95CBDA2F146C9648514B60D8931C7 (void);
// 0x00000449 System.Int32 Cinemachine.ClipperLib/PolyTree::get_Total()
extern void PolyTree_get_Total_m8DC091670D07F6D08D9AAE2C1991C305464F5C5E (void);
// 0x0000044A System.Void Cinemachine.ClipperLib/PolyTree::.ctor()
extern void PolyTree__ctor_m25085F6B77087E89C73FFB4335CD09B9848E8246 (void);
// 0x0000044B System.Boolean Cinemachine.ClipperLib/PolyNode::IsHoleNode()
extern void PolyNode_IsHoleNode_mFE61220C7F4EB0C946AD5D87241DD51162D736ED (void);
// 0x0000044C System.Int32 Cinemachine.ClipperLib/PolyNode::get_ChildCount()
extern void PolyNode_get_ChildCount_mC79FA922B67A88E6E940285881BDB16109D57C67 (void);
// 0x0000044D System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint> Cinemachine.ClipperLib/PolyNode::get_Contour()
extern void PolyNode_get_Contour_mE6F44528DF76BE1EF3CF01FC0F7D3957DE8B8AAA (void);
// 0x0000044E System.Void Cinemachine.ClipperLib/PolyNode::AddChild(Cinemachine.ClipperLib/PolyNode)
extern void PolyNode_AddChild_m0AE9AA1CD1E9EB371362350CAA9304F95A2D2DA5 (void);
// 0x0000044F Cinemachine.ClipperLib/PolyNode Cinemachine.ClipperLib/PolyNode::GetNext()
extern void PolyNode_GetNext_mFCD1626A75F96BF0AF694F9D3796E7F889AA2E40 (void);
// 0x00000450 Cinemachine.ClipperLib/PolyNode Cinemachine.ClipperLib/PolyNode::GetNextSiblingUp()
extern void PolyNode_GetNextSiblingUp_m69D8EDA536BA118464D65E808DCE56325EF2F2ED (void);
// 0x00000451 System.Collections.Generic.List`1<Cinemachine.ClipperLib/PolyNode> Cinemachine.ClipperLib/PolyNode::get_Childs()
extern void PolyNode_get_Childs_m35BF478E71CEEAEC1015E536C4144BDC1877C5FB (void);
// 0x00000452 Cinemachine.ClipperLib/PolyNode Cinemachine.ClipperLib/PolyNode::get_Parent()
extern void PolyNode_get_Parent_m6DF789E40F975877A3439B525B8456883DFF401A (void);
// 0x00000453 System.Boolean Cinemachine.ClipperLib/PolyNode::get_IsHole()
extern void PolyNode_get_IsHole_mDBB3A947DDBE08C1270D230F56DBD51066C2D37B (void);
// 0x00000454 System.Boolean Cinemachine.ClipperLib/PolyNode::get_IsOpen()
extern void PolyNode_get_IsOpen_m3EAD7E4249B97146F59766B32A6D00EC0E766A0A (void);
// 0x00000455 System.Void Cinemachine.ClipperLib/PolyNode::set_IsOpen(System.Boolean)
extern void PolyNode_set_IsOpen_mAD635554DCA3C131384AC9FB2D63F8E7D2132974 (void);
// 0x00000456 System.Void Cinemachine.ClipperLib/PolyNode::.ctor()
extern void PolyNode__ctor_m7DF908F626C3C169AEBAF5DDE0AAC631F0153E85 (void);
// 0x00000457 System.Void Cinemachine.ClipperLib/Int128::.ctor(System.Int64)
extern void Int128__ctor_m99DFFF77A5A8617353DAB1869A052B84E772B858 (void);
// 0x00000458 System.Void Cinemachine.ClipperLib/Int128::.ctor(System.Int64,System.UInt64)
extern void Int128__ctor_m8C6D7B59F2A217F10B2117D900C9890B7BA677B5 (void);
// 0x00000459 System.Void Cinemachine.ClipperLib/Int128::.ctor(Cinemachine.ClipperLib/Int128)
extern void Int128__ctor_mC6B960C84486E99631A6B965A0D0357EFC13756D (void);
// 0x0000045A System.Boolean Cinemachine.ClipperLib/Int128::IsNegative()
extern void Int128_IsNegative_mC7705DD6A31CD27D6F86081FB8E63A61FC51DBEE (void);
// 0x0000045B System.Boolean Cinemachine.ClipperLib/Int128::op_Equality(Cinemachine.ClipperLib/Int128,Cinemachine.ClipperLib/Int128)
extern void Int128_op_Equality_mF1F30DB2596C14A9FC5571B629156D0AEFE92456 (void);
// 0x0000045C System.Boolean Cinemachine.ClipperLib/Int128::op_Inequality(Cinemachine.ClipperLib/Int128,Cinemachine.ClipperLib/Int128)
extern void Int128_op_Inequality_m02FCB8EE852ED36C0C9E915C42641CA84C436328 (void);
// 0x0000045D System.Boolean Cinemachine.ClipperLib/Int128::Equals(System.Object)
extern void Int128_Equals_m3A1780C1A972388F69135020EA85391611CF4777 (void);
// 0x0000045E System.Int32 Cinemachine.ClipperLib/Int128::GetHashCode()
extern void Int128_GetHashCode_m03339EA88F9C7443DD7F84339389F35A70703484 (void);
// 0x0000045F System.Boolean Cinemachine.ClipperLib/Int128::op_GreaterThan(Cinemachine.ClipperLib/Int128,Cinemachine.ClipperLib/Int128)
extern void Int128_op_GreaterThan_m849AB9CDB36CF25794F5FE1A318FAFA40D0DCBE1 (void);
// 0x00000460 System.Boolean Cinemachine.ClipperLib/Int128::op_LessThan(Cinemachine.ClipperLib/Int128,Cinemachine.ClipperLib/Int128)
extern void Int128_op_LessThan_m9FE58A2EBB1CDB6B8B25FC546BEDF387F0C7AC39 (void);
// 0x00000461 Cinemachine.ClipperLib/Int128 Cinemachine.ClipperLib/Int128::op_Addition(Cinemachine.ClipperLib/Int128,Cinemachine.ClipperLib/Int128)
extern void Int128_op_Addition_mD0A0D022CBA36F57EA01A2AFBAE91041818348F8 (void);
// 0x00000462 Cinemachine.ClipperLib/Int128 Cinemachine.ClipperLib/Int128::op_Subtraction(Cinemachine.ClipperLib/Int128,Cinemachine.ClipperLib/Int128)
extern void Int128_op_Subtraction_m3786F47F422FA9FD0AD62DC0B102B065704BCF24 (void);
// 0x00000463 Cinemachine.ClipperLib/Int128 Cinemachine.ClipperLib/Int128::op_UnaryNegation(Cinemachine.ClipperLib/Int128)
extern void Int128_op_UnaryNegation_m5D1CBD65C37A7819B4F2B398F894768719D043D7 (void);
// 0x00000464 System.Double Cinemachine.ClipperLib/Int128::op_Explicit(Cinemachine.ClipperLib/Int128)
extern void Int128_op_Explicit_m76E4BD7F4E70AD040E37C08649DCBB9FDA5D89AD (void);
// 0x00000465 Cinemachine.ClipperLib/Int128 Cinemachine.ClipperLib/Int128::Int128Mul(System.Int64,System.Int64)
extern void Int128_Int128Mul_m09B7DC6DFEAFA7226B5B0DC71BA6EDE669E48EBB (void);
// 0x00000466 System.Void Cinemachine.ClipperLib/IntPoint::.ctor(System.Int64,System.Int64)
extern void IntPoint__ctor_mD85ED4713EDE2C713D37C9CD776B791F21C048E1 (void);
// 0x00000467 System.Void Cinemachine.ClipperLib/IntPoint::.ctor(System.Double,System.Double)
extern void IntPoint__ctor_mA161A7BD0009BE271681C45C59B291FDB224616E (void);
// 0x00000468 System.Void Cinemachine.ClipperLib/IntPoint::.ctor(Cinemachine.ClipperLib/IntPoint)
extern void IntPoint__ctor_m64D954CC64AF3DEB13E5E64F544E46789983BDEB (void);
// 0x00000469 System.Boolean Cinemachine.ClipperLib/IntPoint::op_Equality(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void IntPoint_op_Equality_m1895B2D711859DD40BE3B629806686F2EB454FC5 (void);
// 0x0000046A System.Boolean Cinemachine.ClipperLib/IntPoint::op_Inequality(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void IntPoint_op_Inequality_m6755E58DA3258E1041F8C8C1A0DBB6AA20497EC4 (void);
// 0x0000046B System.Boolean Cinemachine.ClipperLib/IntPoint::Equals(System.Object)
extern void IntPoint_Equals_m6E23677EC9306F8D66DD717865D49A3E9A9BAB5E (void);
// 0x0000046C System.Int32 Cinemachine.ClipperLib/IntPoint::GetHashCode()
extern void IntPoint_GetHashCode_m19A1D74A3CD370690B8C8751C6762CDB47736788 (void);
// 0x0000046D System.Void Cinemachine.ClipperLib/IntRect::.ctor(System.Int64,System.Int64,System.Int64,System.Int64)
extern void IntRect__ctor_mE1409649D5D928E097BE8C8B4B771DC881052464 (void);
// 0x0000046E System.Void Cinemachine.ClipperLib/IntRect::.ctor(Cinemachine.ClipperLib/IntRect)
extern void IntRect__ctor_m40FEB4C1F3B4B8123E190855A65E09E81D766961 (void);
// 0x0000046F System.Void Cinemachine.ClipperLib/TEdge::.ctor()
extern void TEdge__ctor_m8B8B5C7EDFCE49053A6831B431DD8778DA48A793 (void);
// 0x00000470 System.Void Cinemachine.ClipperLib/IntersectNode::.ctor()
extern void IntersectNode__ctor_m531399DCA2D444AA95E847A819983CF287A38A93 (void);
// 0x00000471 System.Int32 Cinemachine.ClipperLib/MyIntersectNodeSort::Compare(Cinemachine.ClipperLib/IntersectNode,Cinemachine.ClipperLib/IntersectNode)
extern void MyIntersectNodeSort_Compare_mA615ABEEB4A5BD5FC383393FEAE2ED508CFE2DC4 (void);
// 0x00000472 System.Void Cinemachine.ClipperLib/MyIntersectNodeSort::.ctor()
extern void MyIntersectNodeSort__ctor_m0319149B8A1830A064E146C75B97ECF76507BCA6 (void);
// 0x00000473 System.Void Cinemachine.ClipperLib/LocalMinima::.ctor()
extern void LocalMinima__ctor_mE19D8A32FAECDA7B52BD732D58E68E0DD486A0EF (void);
// 0x00000474 System.Void Cinemachine.ClipperLib/Scanbeam::.ctor()
extern void Scanbeam__ctor_m6A54606B58D387BF7CEEA2F05C2604FFC0CF97CE (void);
// 0x00000475 System.Void Cinemachine.ClipperLib/Maxima::.ctor()
extern void Maxima__ctor_m831FD8937546D1F859449A8BB3AD2467476CE9D3 (void);
// 0x00000476 System.Void Cinemachine.ClipperLib/OutRec::.ctor()
extern void OutRec__ctor_mE1E0B900C419695A0EC929A77C823DD7B806D715 (void);
// 0x00000477 System.Void Cinemachine.ClipperLib/OutPt::.ctor()
extern void OutPt__ctor_mB35AA5D0B77A19A8611ED4C8B748BE311B413C80 (void);
// 0x00000478 System.Void Cinemachine.ClipperLib/Join::.ctor()
extern void Join__ctor_m0275B6A7878993163832BBF4D62AEDAB9058D665 (void);
// 0x00000479 System.Boolean Cinemachine.ClipperLib/ClipperBase::near_zero(System.Double)
extern void ClipperBase_near_zero_mDAFA25942FF6D795F79D917BD5A178F5085E0504 (void);
// 0x0000047A System.Boolean Cinemachine.ClipperLib/ClipperBase::get_PreserveCollinear()
extern void ClipperBase_get_PreserveCollinear_mF23688F07E4141A6BB90080B464208DF71B73328 (void);
// 0x0000047B System.Void Cinemachine.ClipperLib/ClipperBase::set_PreserveCollinear(System.Boolean)
extern void ClipperBase_set_PreserveCollinear_m53E7942B8B6AE1A1C850791D7FB63C094BD3AB63 (void);
// 0x0000047C System.Void Cinemachine.ClipperLib/ClipperBase::Swap(System.Int64&,System.Int64&)
extern void ClipperBase_Swap_mF999B5BD7A715214B574ED4F7A74092F67F9A836 (void);
// 0x0000047D System.Boolean Cinemachine.ClipperLib/ClipperBase::IsHorizontal(Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_IsHorizontal_m018DF969F9BD306E460CBA58013819081441C411 (void);
// 0x0000047E System.Boolean Cinemachine.ClipperLib/ClipperBase::PointIsVertex(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/OutPt)
extern void ClipperBase_PointIsVertex_m59600D27C1BACB19FC117EF15967FA90933D8A33 (void);
// 0x0000047F System.Boolean Cinemachine.ClipperLib/ClipperBase::PointOnLineSegment(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,System.Boolean)
extern void ClipperBase_PointOnLineSegment_m0FD505D256EC78BFBAFEC71564228375FFF90D18 (void);
// 0x00000480 System.Boolean Cinemachine.ClipperLib/ClipperBase::PointOnPolygon(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/OutPt,System.Boolean)
extern void ClipperBase_PointOnPolygon_m0D2EBFA14EEFE915C44B56321B5BBD18DC44D536 (void);
// 0x00000481 System.Boolean Cinemachine.ClipperLib/ClipperBase::SlopesEqual(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,System.Boolean)
extern void ClipperBase_SlopesEqual_m2FD334595102DCBC8C3DEBCD7A84BCF4A30F8A4F (void);
// 0x00000482 System.Boolean Cinemachine.ClipperLib/ClipperBase::SlopesEqual(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,System.Boolean)
extern void ClipperBase_SlopesEqual_mAA286348A715F1648DDF94B0D6D79AA678A438F7 (void);
// 0x00000483 System.Boolean Cinemachine.ClipperLib/ClipperBase::SlopesEqual(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,System.Boolean)
extern void ClipperBase_SlopesEqual_mFEBD4FD4B503A8D4CB75DD1E0B870B71E1BC580B (void);
// 0x00000484 System.Void Cinemachine.ClipperLib/ClipperBase::.ctor()
extern void ClipperBase__ctor_mDD73F9BB8C4F62B9182AD325A825ECFC5B0FC2B8 (void);
// 0x00000485 System.Void Cinemachine.ClipperLib/ClipperBase::Clear()
extern void ClipperBase_Clear_m3392AC2BFE9E6F6F31BE1302250A427D6B067619 (void);
// 0x00000486 System.Void Cinemachine.ClipperLib/ClipperBase::DisposeLocalMinimaList()
extern void ClipperBase_DisposeLocalMinimaList_mAAE9BA56E0950FF2C34D33C4D5DBA769E028F939 (void);
// 0x00000487 System.Void Cinemachine.ClipperLib/ClipperBase::RangeTest(Cinemachine.ClipperLib/IntPoint,System.Boolean&)
extern void ClipperBase_RangeTest_mA4C55F148BD7F493C28F0504FEF4DA2290D47877 (void);
// 0x00000488 System.Void Cinemachine.ClipperLib/ClipperBase::InitEdge(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/IntPoint)
extern void ClipperBase_InitEdge_m982226D0C1C630392C77A166BEAC4761A8F93EC5 (void);
// 0x00000489 System.Void Cinemachine.ClipperLib/ClipperBase::InitEdge2(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/PolyType)
extern void ClipperBase_InitEdge2_mE657B680AC1506D145CB96A57D7B7ED14EAB5EB2 (void);
// 0x0000048A Cinemachine.ClipperLib/TEdge Cinemachine.ClipperLib/ClipperBase::FindNextLocMin(Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_FindNextLocMin_m45C5FE3F29B6B82782CDCF8AFF7A24C5EE3C397F (void);
// 0x0000048B Cinemachine.ClipperLib/TEdge Cinemachine.ClipperLib/ClipperBase::ProcessBound(Cinemachine.ClipperLib/TEdge,System.Boolean)
extern void ClipperBase_ProcessBound_m6A494AFF34846AF6948C68F98400404423E62AD4 (void);
// 0x0000048C System.Boolean Cinemachine.ClipperLib/ClipperBase::AddPath(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,Cinemachine.ClipperLib/PolyType,System.Boolean)
extern void ClipperBase_AddPath_m34810D1B9F21314A52EB4A1B77F454EE19CAA62F (void);
// 0x0000048D System.Boolean Cinemachine.ClipperLib/ClipperBase::AddPaths(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,Cinemachine.ClipperLib/PolyType,System.Boolean)
extern void ClipperBase_AddPaths_m3A4E3756B561A396C16E4658743F61D088FA2EEA (void);
// 0x0000048E System.Boolean Cinemachine.ClipperLib/ClipperBase::Pt2IsBetweenPt1AndPt3(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void ClipperBase_Pt2IsBetweenPt1AndPt3_m8DAAA16021926CF945ED648C6B39AE2E2B54B344 (void);
// 0x0000048F Cinemachine.ClipperLib/TEdge Cinemachine.ClipperLib/ClipperBase::RemoveEdge(Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_RemoveEdge_mEE738D2187081283AF322CF5F9FC9E231AB2E17A (void);
// 0x00000490 System.Void Cinemachine.ClipperLib/ClipperBase::SetDx(Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_SetDx_m471055292D10981774F3433FCBE57E7E508CA16E (void);
// 0x00000491 System.Void Cinemachine.ClipperLib/ClipperBase::InsertLocalMinima(Cinemachine.ClipperLib/LocalMinima)
extern void ClipperBase_InsertLocalMinima_mD4018919209DC3F43CEDA9FD7284F71F629A564B (void);
// 0x00000492 System.Boolean Cinemachine.ClipperLib/ClipperBase::PopLocalMinima(System.Int64,Cinemachine.ClipperLib/LocalMinima&)
extern void ClipperBase_PopLocalMinima_m4E164FB1C3B362046B58791AC93C616A5E054247 (void);
// 0x00000493 System.Void Cinemachine.ClipperLib/ClipperBase::ReverseHorizontal(Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_ReverseHorizontal_m763FCD506B30C857C7C100FD33F2CB0BBEC75444 (void);
// 0x00000494 System.Void Cinemachine.ClipperLib/ClipperBase::Reset()
extern void ClipperBase_Reset_mC6F18D6EBC923A5D2BDBCAACDCCF2BA3E6A70B52 (void);
// 0x00000495 Cinemachine.ClipperLib/IntRect Cinemachine.ClipperLib/ClipperBase::GetBounds(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>)
extern void ClipperBase_GetBounds_mC13AB297ABF4C0D5C98672C33C9B8358242A0ED7 (void);
// 0x00000496 System.Void Cinemachine.ClipperLib/ClipperBase::InsertScanbeam(System.Int64)
extern void ClipperBase_InsertScanbeam_m69EAA6C8EF670077B1CA2FE21CE5CC8060294417 (void);
// 0x00000497 System.Boolean Cinemachine.ClipperLib/ClipperBase::PopScanbeam(System.Int64&)
extern void ClipperBase_PopScanbeam_mD7D3E1613AE0AD4A4E1C317C5A3B60D9142F40FF (void);
// 0x00000498 System.Boolean Cinemachine.ClipperLib/ClipperBase::LocalMinimaPending()
extern void ClipperBase_LocalMinimaPending_m5D90C289A0E26A71881F938546EB817F038B0ACF (void);
// 0x00000499 Cinemachine.ClipperLib/OutRec Cinemachine.ClipperLib/ClipperBase::CreateOutRec()
extern void ClipperBase_CreateOutRec_mE25562A65CFAD14C0D80ECC335FC513D73F13073 (void);
// 0x0000049A System.Void Cinemachine.ClipperLib/ClipperBase::DisposeOutRec(System.Int32)
extern void ClipperBase_DisposeOutRec_m6A228CBBF968B111B5ACBC23BE7CE16C1E4F0D24 (void);
// 0x0000049B System.Void Cinemachine.ClipperLib/ClipperBase::UpdateEdgeIntoAEL(Cinemachine.ClipperLib/TEdge&)
extern void ClipperBase_UpdateEdgeIntoAEL_mC31E817EFC95348D1F3898522D45FB4041431F1B (void);
// 0x0000049C System.Void Cinemachine.ClipperLib/ClipperBase::SwapPositionsInAEL(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_SwapPositionsInAEL_m60BE707FB4A2F4F7AB84F60C86E9421A9E23A268 (void);
// 0x0000049D System.Void Cinemachine.ClipperLib/ClipperBase::DeleteFromAEL(Cinemachine.ClipperLib/TEdge)
extern void ClipperBase_DeleteFromAEL_m7D98BBD07D917A74E6C0F683D33B1D01D2DDE1F1 (void);
// 0x0000049E System.Void Cinemachine.ClipperLib/Clipper::.ctor(System.Int32)
extern void Clipper__ctor_mFE1DEBE1F52B653F893824C803EC03A94560C146 (void);
// 0x0000049F System.Void Cinemachine.ClipperLib/Clipper::InsertMaxima(System.Int64)
extern void Clipper_InsertMaxima_mA2E8085C09D3A5519556B24A3C619D2087DA8421 (void);
// 0x000004A0 System.Boolean Cinemachine.ClipperLib/Clipper::get_ReverseSolution()
extern void Clipper_get_ReverseSolution_m58207CBA9E1BF47F359F847CE77963A18D3FE70D (void);
// 0x000004A1 System.Void Cinemachine.ClipperLib/Clipper::set_ReverseSolution(System.Boolean)
extern void Clipper_set_ReverseSolution_m4C3D2FEE37DDCCB590395F6A7199C8510813834A (void);
// 0x000004A2 System.Boolean Cinemachine.ClipperLib/Clipper::get_StrictlySimple()
extern void Clipper_get_StrictlySimple_m3BF161D9925CF5125B2D8786A8EDF317D11EF46B (void);
// 0x000004A3 System.Void Cinemachine.ClipperLib/Clipper::set_StrictlySimple(System.Boolean)
extern void Clipper_set_StrictlySimple_m01963B17681B30F7189B6C54CCA2D222B5537007 (void);
// 0x000004A4 System.Boolean Cinemachine.ClipperLib/Clipper::Execute(Cinemachine.ClipperLib/ClipType,System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,Cinemachine.ClipperLib/PolyFillType)
extern void Clipper_Execute_mBAA9C5AE591C28C14F8E235A726810482235D5D4 (void);
// 0x000004A5 System.Boolean Cinemachine.ClipperLib/Clipper::Execute(Cinemachine.ClipperLib/ClipType,Cinemachine.ClipperLib/PolyTree,Cinemachine.ClipperLib/PolyFillType)
extern void Clipper_Execute_m21D40DF8C4615CEC73E449737C33394FA2057F61 (void);
// 0x000004A6 System.Boolean Cinemachine.ClipperLib/Clipper::Execute(Cinemachine.ClipperLib/ClipType,System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,Cinemachine.ClipperLib/PolyFillType,Cinemachine.ClipperLib/PolyFillType)
extern void Clipper_Execute_mD4963862505A6417805A36BDC8EE3551AF5227A0 (void);
// 0x000004A7 System.Boolean Cinemachine.ClipperLib/Clipper::Execute(Cinemachine.ClipperLib/ClipType,Cinemachine.ClipperLib/PolyTree,Cinemachine.ClipperLib/PolyFillType,Cinemachine.ClipperLib/PolyFillType)
extern void Clipper_Execute_mDBF9988159022C1F340702FED6E189758BD23D52 (void);
// 0x000004A8 System.Void Cinemachine.ClipperLib/Clipper::FixHoleLinkage(Cinemachine.ClipperLib/OutRec)
extern void Clipper_FixHoleLinkage_m8383DDB3534A268C9D0BCE5A923FCF4B1284C83B (void);
// 0x000004A9 System.Boolean Cinemachine.ClipperLib/Clipper::ExecuteInternal()
extern void Clipper_ExecuteInternal_m71E3C0DA830D24D2F6BDAB1B5D01E8C87DABDD8F (void);
// 0x000004AA System.Void Cinemachine.ClipperLib/Clipper::DisposeAllPolyPts()
extern void Clipper_DisposeAllPolyPts_mF4263E2B3EDF1A70EA2A11C865C64C13FA097D9D (void);
// 0x000004AB System.Void Cinemachine.ClipperLib/Clipper::AddJoin(Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_AddJoin_m8ADF8728F8BD9FE7631356696FA27CB7C110E70A (void);
// 0x000004AC System.Void Cinemachine.ClipperLib/Clipper::AddGhostJoin(Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_AddGhostJoin_m990DB3A2075C093C710D575BBC37BA4A4C969C6B (void);
// 0x000004AD System.Void Cinemachine.ClipperLib/Clipper::InsertLocalMinimaIntoAEL(System.Int64)
extern void Clipper_InsertLocalMinimaIntoAEL_mA8C63CB0128DB1A1C703BAD42D8CC0E08E1685B6 (void);
// 0x000004AE System.Void Cinemachine.ClipperLib/Clipper::InsertEdgeIntoAEL(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void Clipper_InsertEdgeIntoAEL_mC2B55AE5665316899504CBCA4F7F2D3390A5C019 (void);
// 0x000004AF System.Boolean Cinemachine.ClipperLib/Clipper::E2InsertsBeforeE1(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void Clipper_E2InsertsBeforeE1_m88B8A274A20CA89FFBC22FD18CE23758FF70945D (void);
// 0x000004B0 System.Boolean Cinemachine.ClipperLib/Clipper::IsEvenOddFillType(Cinemachine.ClipperLib/TEdge)
extern void Clipper_IsEvenOddFillType_mE55640494B101C66E086F351AA619705F7ABC0C5 (void);
// 0x000004B1 System.Boolean Cinemachine.ClipperLib/Clipper::IsEvenOddAltFillType(Cinemachine.ClipperLib/TEdge)
extern void Clipper_IsEvenOddAltFillType_m4E105EE325099625299F946B0BE5675868BE2BA3 (void);
// 0x000004B2 System.Boolean Cinemachine.ClipperLib/Clipper::IsContributing(Cinemachine.ClipperLib/TEdge)
extern void Clipper_IsContributing_m05CCF9C25A8F921D8EE4A1B88E810AD844E74012 (void);
// 0x000004B3 System.Void Cinemachine.ClipperLib/Clipper::SetWindingCount(Cinemachine.ClipperLib/TEdge)
extern void Clipper_SetWindingCount_mC49B8B45D72F8BA8A1B6E4FD58D5305B158B8E21 (void);
// 0x000004B4 System.Void Cinemachine.ClipperLib/Clipper::AddEdgeToSEL(Cinemachine.ClipperLib/TEdge)
extern void Clipper_AddEdgeToSEL_m5D29FF86F0F6B1FA0CC46FE4E86780244E8CF0B5 (void);
// 0x000004B5 System.Boolean Cinemachine.ClipperLib/Clipper::PopEdgeFromSEL(Cinemachine.ClipperLib/TEdge&)
extern void Clipper_PopEdgeFromSEL_m1A0A7CACEF17CBF53D2D5404064EB11D2426D161 (void);
// 0x000004B6 System.Void Cinemachine.ClipperLib/Clipper::CopyAELToSEL()
extern void Clipper_CopyAELToSEL_mBE2FFC8FFF11AA874A8F542E437DB2FA824B32F7 (void);
// 0x000004B7 System.Void Cinemachine.ClipperLib/Clipper::SwapPositionsInSEL(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void Clipper_SwapPositionsInSEL_mD3379C551074FD2F32E777F91952245DEA9E9594 (void);
// 0x000004B8 System.Void Cinemachine.ClipperLib/Clipper::AddLocalMaxPoly(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_AddLocalMaxPoly_mC531749CA70DF137D954703614E013FCFD6068D4 (void);
// 0x000004B9 Cinemachine.ClipperLib/OutPt Cinemachine.ClipperLib/Clipper::AddLocalMinPoly(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_AddLocalMinPoly_m3214E0184F98D4553AED2B6DB0CC1C6A4BA6331F (void);
// 0x000004BA Cinemachine.ClipperLib/OutPt Cinemachine.ClipperLib/Clipper::AddOutPt(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_AddOutPt_m46F920591D075726FB133415FB8D46A51301407C (void);
// 0x000004BB Cinemachine.ClipperLib/OutPt Cinemachine.ClipperLib/Clipper::GetLastOutPt(Cinemachine.ClipperLib/TEdge)
extern void Clipper_GetLastOutPt_m4E969A4052E60A00532A658AF980E03CC6DA4131 (void);
// 0x000004BC System.Void Cinemachine.ClipperLib/Clipper::SwapPoints(Cinemachine.ClipperLib/IntPoint&,Cinemachine.ClipperLib/IntPoint&)
extern void Clipper_SwapPoints_m87CAA96126A2EC872972D1675CD6A9DECDD96037 (void);
// 0x000004BD System.Boolean Cinemachine.ClipperLib/Clipper::HorzSegmentsOverlap(System.Int64,System.Int64,System.Int64,System.Int64)
extern void Clipper_HorzSegmentsOverlap_m5FA9498ACDD1B422C8024DA1A7BBB24BAC552790 (void);
// 0x000004BE System.Void Cinemachine.ClipperLib/Clipper::SetHoleState(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/OutRec)
extern void Clipper_SetHoleState_m0C85355B78FE5FEE59358DCA9241F8D6FD386529 (void);
// 0x000004BF System.Double Cinemachine.ClipperLib/Clipper::GetDx(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_GetDx_mC6E8199B23B026C8AE8C793B05AED49AF0EA8355 (void);
// 0x000004C0 System.Boolean Cinemachine.ClipperLib/Clipper::FirstIsBottomPt(Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/OutPt)
extern void Clipper_FirstIsBottomPt_m0BC35D9AAC75A173CB303ACCD69EFB2EE72F1391 (void);
// 0x000004C1 Cinemachine.ClipperLib/OutPt Cinemachine.ClipperLib/Clipper::GetBottomPt(Cinemachine.ClipperLib/OutPt)
extern void Clipper_GetBottomPt_m54E17AA4BE26A3CC71C96BBAD7ACBC3DA61DA18F (void);
// 0x000004C2 Cinemachine.ClipperLib/OutRec Cinemachine.ClipperLib/Clipper::GetLowermostRec(Cinemachine.ClipperLib/OutRec,Cinemachine.ClipperLib/OutRec)
extern void Clipper_GetLowermostRec_mA8822C6550166EF7904D4B4C20ED66102F064F7D (void);
// 0x000004C3 System.Boolean Cinemachine.ClipperLib/Clipper::OutRec1RightOfOutRec2(Cinemachine.ClipperLib/OutRec,Cinemachine.ClipperLib/OutRec)
extern void Clipper_OutRec1RightOfOutRec2_m6F9DFB83D02704D771F43FB7E25088060393CF9F (void);
// 0x000004C4 Cinemachine.ClipperLib/OutRec Cinemachine.ClipperLib/Clipper::GetOutRec(System.Int32)
extern void Clipper_GetOutRec_mA981F566088446A10C95006E435C4BFB43A98BC1 (void);
// 0x000004C5 System.Void Cinemachine.ClipperLib/Clipper::AppendPolygon(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void Clipper_AppendPolygon_m737BF9DD8CA443E80402E3E7ABC2C32EFD839F1C (void);
// 0x000004C6 System.Void Cinemachine.ClipperLib/Clipper::ReversePolyPtLinks(Cinemachine.ClipperLib/OutPt)
extern void Clipper_ReversePolyPtLinks_m10B9631CBBD7C03F36B509D8F5BDA2BE7B6D6FC5 (void);
// 0x000004C7 System.Void Cinemachine.ClipperLib/Clipper::SwapSides(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void Clipper_SwapSides_mD8D5422E34AAED12D88D5FAEC03E3562FDFE4413 (void);
// 0x000004C8 System.Void Cinemachine.ClipperLib/Clipper::SwapPolyIndexes(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge)
extern void Clipper_SwapPolyIndexes_m8D0C37CBD6054A5AD310E5FA0642D0EFFF32D445 (void);
// 0x000004C9 System.Void Cinemachine.ClipperLib/Clipper::IntersectEdges(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_IntersectEdges_m38A4C149A9CAA52BBB887233919F6D3770B0C6CB (void);
// 0x000004CA System.Void Cinemachine.ClipperLib/Clipper::DeleteFromSEL(Cinemachine.ClipperLib/TEdge)
extern void Clipper_DeleteFromSEL_m1E414DF925CB7E80E8C88B7208954F86CD6BAC6E (void);
// 0x000004CB System.Void Cinemachine.ClipperLib/Clipper::ProcessHorizontals()
extern void Clipper_ProcessHorizontals_m2D749E6B682EB52D3815374C4C1194B40E81315A (void);
// 0x000004CC System.Void Cinemachine.ClipperLib/Clipper::GetHorzDirection(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/Direction&,System.Int64&,System.Int64&)
extern void Clipper_GetHorzDirection_m2746F275D04EBDAE2EA2C70C4BC402C3B587F4AE (void);
// 0x000004CD System.Void Cinemachine.ClipperLib/Clipper::ProcessHorizontal(Cinemachine.ClipperLib/TEdge)
extern void Clipper_ProcessHorizontal_m23F417A997A068313227F265CC9A713CAB63F56C (void);
// 0x000004CE Cinemachine.ClipperLib/TEdge Cinemachine.ClipperLib/Clipper::GetNextInAEL(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/Direction)
extern void Clipper_GetNextInAEL_m7A6526C1C5278EC4ACE3A69700E6D82E36A4B274 (void);
// 0x000004CF System.Boolean Cinemachine.ClipperLib/Clipper::IsMinima(Cinemachine.ClipperLib/TEdge)
extern void Clipper_IsMinima_mDBA5FABD10350ECB457547A781997F9E165D099B (void);
// 0x000004D0 System.Boolean Cinemachine.ClipperLib/Clipper::IsMaxima(Cinemachine.ClipperLib/TEdge,System.Double)
extern void Clipper_IsMaxima_m15A892565E5C8644F6F1F5EE948B4EBDDE7FCD9C (void);
// 0x000004D1 System.Boolean Cinemachine.ClipperLib/Clipper::IsIntermediate(Cinemachine.ClipperLib/TEdge,System.Double)
extern void Clipper_IsIntermediate_mDB13F1C11F973F437A65A406235DF8B2A6B0D419 (void);
// 0x000004D2 Cinemachine.ClipperLib/TEdge Cinemachine.ClipperLib/Clipper::GetMaximaPair(Cinemachine.ClipperLib/TEdge)
extern void Clipper_GetMaximaPair_m8F0A19E8344154A1A55DA93D025C143CE8876820 (void);
// 0x000004D3 Cinemachine.ClipperLib/TEdge Cinemachine.ClipperLib/Clipper::GetMaximaPairEx(Cinemachine.ClipperLib/TEdge)
extern void Clipper_GetMaximaPairEx_m9356317DFEFF8514EF0391D0BDA06802879B98A2 (void);
// 0x000004D4 System.Boolean Cinemachine.ClipperLib/Clipper::ProcessIntersections(System.Int64)
extern void Clipper_ProcessIntersections_m561C929E8754747190448077F511FC1B74093FD3 (void);
// 0x000004D5 System.Void Cinemachine.ClipperLib/Clipper::BuildIntersectList(System.Int64)
extern void Clipper_BuildIntersectList_m6B5DA2A81AF660F5471A530B97383E223BEECB66 (void);
// 0x000004D6 System.Boolean Cinemachine.ClipperLib/Clipper::EdgesAdjacent(Cinemachine.ClipperLib/IntersectNode)
extern void Clipper_EdgesAdjacent_m8CB2FDA541E506262090D1B39A8585158A4CDD50 (void);
// 0x000004D7 System.Int32 Cinemachine.ClipperLib/Clipper::IntersectNodeSort(Cinemachine.ClipperLib/IntersectNode,Cinemachine.ClipperLib/IntersectNode)
extern void Clipper_IntersectNodeSort_m7D01712EAA690EC202397BBA1ACD1844C762DDBB (void);
// 0x000004D8 System.Boolean Cinemachine.ClipperLib/Clipper::FixupIntersectionOrder()
extern void Clipper_FixupIntersectionOrder_m11A6732B2BAE536BDCEFD38A652944B153C2BE9F (void);
// 0x000004D9 System.Void Cinemachine.ClipperLib/Clipper::ProcessIntersectList()
extern void Clipper_ProcessIntersectList_mB3ADFF851F65B6F8CC05E1E5CB46265779E862E1 (void);
// 0x000004DA System.Int64 Cinemachine.ClipperLib/Clipper::Round(System.Double)
extern void Clipper_Round_m045C6DD247A771A5626C5E50CA5C133836D294B2 (void);
// 0x000004DB System.Int64 Cinemachine.ClipperLib/Clipper::TopX(Cinemachine.ClipperLib/TEdge,System.Int64)
extern void Clipper_TopX_mEAAE42DE6C0AD711F8BC435773AD506749A62102 (void);
// 0x000004DC System.Void Cinemachine.ClipperLib/Clipper::IntersectPoint(Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/TEdge,Cinemachine.ClipperLib/IntPoint&)
extern void Clipper_IntersectPoint_mE17B6245017EAD8BEA10EDBAF03A182AEABCCFEE (void);
// 0x000004DD System.Void Cinemachine.ClipperLib/Clipper::ProcessEdgesAtTopOfScanbeam(System.Int64)
extern void Clipper_ProcessEdgesAtTopOfScanbeam_m7AF18AD101770BC5E9FF00A769F2AA855D803866 (void);
// 0x000004DE System.Void Cinemachine.ClipperLib/Clipper::DoMaxima(Cinemachine.ClipperLib/TEdge)
extern void Clipper_DoMaxima_mFAFCF9A9E0A4CB22C57EBDEC6564493006160F18 (void);
// 0x000004DF System.Void Cinemachine.ClipperLib/Clipper::ReversePaths(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>)
extern void Clipper_ReversePaths_m2BF00831C6F40C9DA37A2EA51B2E5E7735E9F2E2 (void);
// 0x000004E0 System.Boolean Cinemachine.ClipperLib/Clipper::Orientation(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>)
extern void Clipper_Orientation_mCCFCAC82598BB2359FAC352A25CC7CD9DF71E406 (void);
// 0x000004E1 System.Int32 Cinemachine.ClipperLib/Clipper::PointCount(Cinemachine.ClipperLib/OutPt)
extern void Clipper_PointCount_m5DAEA2750B49726731544F0F61B694295D5A4FC8 (void);
// 0x000004E2 System.Void Cinemachine.ClipperLib/Clipper::BuildResult(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>)
extern void Clipper_BuildResult_m8DEC825FFD16A1CBFAE17FEB01E21A6010850DF8 (void);
// 0x000004E3 System.Void Cinemachine.ClipperLib/Clipper::BuildResult2(Cinemachine.ClipperLib/PolyTree)
extern void Clipper_BuildResult2_m115358F60E247678C58F113E4AADB97BFA2789A0 (void);
// 0x000004E4 System.Void Cinemachine.ClipperLib/Clipper::FixupOutPolyline(Cinemachine.ClipperLib/OutRec)
extern void Clipper_FixupOutPolyline_mE8755CFCB5009D731A43764719EDD8A34BDECAD3 (void);
// 0x000004E5 System.Void Cinemachine.ClipperLib/Clipper::FixupOutPolygon(Cinemachine.ClipperLib/OutRec)
extern void Clipper_FixupOutPolygon_m1E6DBAE927150B9971F4115A5764DC6992969660 (void);
// 0x000004E6 Cinemachine.ClipperLib/OutPt Cinemachine.ClipperLib/Clipper::DupOutPt(Cinemachine.ClipperLib/OutPt,System.Boolean)
extern void Clipper_DupOutPt_mF94EE9A7F893922E4E8EEEBAAA8B91E06E748D12 (void);
// 0x000004E7 System.Boolean Cinemachine.ClipperLib/Clipper::GetOverlap(System.Int64,System.Int64,System.Int64,System.Int64,System.Int64&,System.Int64&)
extern void Clipper_GetOverlap_m40BDFF02F99393EC15CA74B5C6F680F2198D05D7 (void);
// 0x000004E8 System.Boolean Cinemachine.ClipperLib/Clipper::JoinHorz(Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/IntPoint,System.Boolean)
extern void Clipper_JoinHorz_mA2DA78D8763F8883DEB40BA70E79D306CB306FF4 (void);
// 0x000004E9 System.Boolean Cinemachine.ClipperLib/Clipper::JoinPoints(Cinemachine.ClipperLib/Join,Cinemachine.ClipperLib/OutRec,Cinemachine.ClipperLib/OutRec)
extern void Clipper_JoinPoints_m8DE3553CA0E63A41839CCECB808BAAD4CD30CA70 (void);
// 0x000004EA System.Int32 Cinemachine.ClipperLib/Clipper::PointInPolygon(Cinemachine.ClipperLib/IntPoint,System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>)
extern void Clipper_PointInPolygon_mBEDDABD4FDE3DCA3142EE3D6341E5B41E243ADB6 (void);
// 0x000004EB System.Int32 Cinemachine.ClipperLib/Clipper::PointInPolygon(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/OutPt)
extern void Clipper_PointInPolygon_m6B65511BF890F9C19B1CE324C3F4A7016EF3404B (void);
// 0x000004EC System.Boolean Cinemachine.ClipperLib/Clipper::Poly2ContainsPoly1(Cinemachine.ClipperLib/OutPt,Cinemachine.ClipperLib/OutPt)
extern void Clipper_Poly2ContainsPoly1_mB4ACD78F9B8E488BF07BF33B77C7494796A22ED4 (void);
// 0x000004ED System.Void Cinemachine.ClipperLib/Clipper::FixupFirstLefts1(Cinemachine.ClipperLib/OutRec,Cinemachine.ClipperLib/OutRec)
extern void Clipper_FixupFirstLefts1_m65E2AA48CBB7DF4953C93306CC38293E73751B37 (void);
// 0x000004EE System.Void Cinemachine.ClipperLib/Clipper::FixupFirstLefts2(Cinemachine.ClipperLib/OutRec,Cinemachine.ClipperLib/OutRec)
extern void Clipper_FixupFirstLefts2_m3628DAEB783FFA0C15CC88FE7D5A43031780BD19 (void);
// 0x000004EF System.Void Cinemachine.ClipperLib/Clipper::FixupFirstLefts3(Cinemachine.ClipperLib/OutRec,Cinemachine.ClipperLib/OutRec)
extern void Clipper_FixupFirstLefts3_m5FE8223D4C067F2405FB9E5397CAC2E91B799EBB (void);
// 0x000004F0 Cinemachine.ClipperLib/OutRec Cinemachine.ClipperLib/Clipper::ParseFirstLeft(Cinemachine.ClipperLib/OutRec)
extern void Clipper_ParseFirstLeft_mB314AD38EE3F6A8E49A1D7889FA2F60EF818F3AD (void);
// 0x000004F1 System.Void Cinemachine.ClipperLib/Clipper::JoinCommonEdges()
extern void Clipper_JoinCommonEdges_m1144D7D13B127B352BEE7B56E194E6FA5223144E (void);
// 0x000004F2 System.Void Cinemachine.ClipperLib/Clipper::UpdateOutPtIdxs(Cinemachine.ClipperLib/OutRec)
extern void Clipper_UpdateOutPtIdxs_mC571A93028E6202AB3531A1F6583EDEF411E70AB (void);
// 0x000004F3 System.Void Cinemachine.ClipperLib/Clipper::DoSimplePolygons()
extern void Clipper_DoSimplePolygons_m2B00FD05078E3E8689721DC7C62FA9EEBF9744A4 (void);
// 0x000004F4 System.Double Cinemachine.ClipperLib/Clipper::Area(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>)
extern void Clipper_Area_mA39460C41233FBD033C7222201D33F5A56DC4661 (void);
// 0x000004F5 System.Double Cinemachine.ClipperLib/Clipper::Area(Cinemachine.ClipperLib/OutRec)
extern void Clipper_Area_mF674418737AC1594BFB2CD37162839CBC117A13E (void);
// 0x000004F6 System.Double Cinemachine.ClipperLib/Clipper::Area(Cinemachine.ClipperLib/OutPt)
extern void Clipper_Area_mFA662DAA8CD3045CAAE005E591ACC6782AB49164 (void);
// 0x000004F7 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::SimplifyPolygon(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,Cinemachine.ClipperLib/PolyFillType)
extern void Clipper_SimplifyPolygon_m5D964DFAFE6251CB5230892673D1825829A41608 (void);
// 0x000004F8 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::SimplifyPolygons(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,Cinemachine.ClipperLib/PolyFillType)
extern void Clipper_SimplifyPolygons_mC43520F605202A488565AB6AC98609C829BC27B7 (void);
// 0x000004F9 System.Double Cinemachine.ClipperLib/Clipper::DistanceSqrd(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_DistanceSqrd_mE09149587060D3E1E2613F6A466F22BFAC784871 (void);
// 0x000004FA System.Double Cinemachine.ClipperLib/Clipper::DistanceFromLineSqrd(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_DistanceFromLineSqrd_m83EB8AD5E4740D6D2C9E05A3A50B6AB7B93125C2 (void);
// 0x000004FB System.Boolean Cinemachine.ClipperLib/Clipper::SlopesNearCollinear(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,System.Double)
extern void Clipper_SlopesNearCollinear_m995CC508199595093168D5AE1B06C6B029092DE4 (void);
// 0x000004FC System.Boolean Cinemachine.ClipperLib/Clipper::PointsAreClose(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint,System.Double)
extern void Clipper_PointsAreClose_mB37FFCEA3A853FD6D1FB9EB698C6ED444B5438DB (void);
// 0x000004FD Cinemachine.ClipperLib/OutPt Cinemachine.ClipperLib/Clipper::ExcludeOp(Cinemachine.ClipperLib/OutPt)
extern void Clipper_ExcludeOp_m0BF04A50C9B809D52528BA1C2F3E1488B1D4A469 (void);
// 0x000004FE System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint> Cinemachine.ClipperLib/Clipper::CleanPolygon(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Double)
extern void Clipper_CleanPolygon_m983259D79EA94E281D1F6EC14BEFDC191BBDEAEA (void);
// 0x000004FF System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::CleanPolygons(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,System.Double)
extern void Clipper_CleanPolygons_mAB8185A21A022A9A5240A2378F651D8A34B8FBB1 (void);
// 0x00000500 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::Minkowski(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Boolean,System.Boolean)
extern void Clipper_Minkowski_mD85E9DDB9CAF5B69BBA8B4DC2E4E2D58FB81F32A (void);
// 0x00000501 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::MinkowskiSum(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Boolean)
extern void Clipper_MinkowskiSum_m29ABD52399BCF104E8685795B86F0E1A2A28C1C1 (void);
// 0x00000502 System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint> Cinemachine.ClipperLib/Clipper::TranslatePath(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,Cinemachine.ClipperLib/IntPoint)
extern void Clipper_TranslatePath_mF158D477E0B6F15F4A22EC080212529506ECB08E (void);
// 0x00000503 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::MinkowskiSum(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,System.Boolean)
extern void Clipper_MinkowskiSum_m02D0DB720CCF4A9A76EFC0BFF6B0BA477D1B6D3C (void);
// 0x00000504 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::MinkowskiDiff(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>)
extern void Clipper_MinkowskiDiff_mE2847469BCCD1BD028179E51C352DF12D9473C04 (void);
// 0x00000505 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::PolyTreeToPaths(Cinemachine.ClipperLib/PolyTree)
extern void Clipper_PolyTreeToPaths_m9E06A3B3F6527EF442C636FB195AC7E67A0C38AF (void);
// 0x00000506 System.Void Cinemachine.ClipperLib/Clipper::AddPolyNodeToPaths(Cinemachine.ClipperLib/PolyNode,Cinemachine.ClipperLib/Clipper/NodeType,System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>)
extern void Clipper_AddPolyNodeToPaths_m94EC1CD2E0DBD0759A9C448163D82A373025BEE4 (void);
// 0x00000507 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::OpenPathsFromPolyTree(Cinemachine.ClipperLib/PolyTree)
extern void Clipper_OpenPathsFromPolyTree_mF1221E00B6D25AFAECFA106E1CFC43137BB42EE4 (void);
// 0x00000508 System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>> Cinemachine.ClipperLib/Clipper::ClosedPathsFromPolyTree(Cinemachine.ClipperLib/PolyTree)
extern void Clipper_ClosedPathsFromPolyTree_mFDF300DCFD546952007701D71C87A90E86CE0874 (void);
// 0x00000509 System.Double Cinemachine.ClipperLib/ClipperOffset::get_ArcTolerance()
extern void ClipperOffset_get_ArcTolerance_m25B298CB94489BE792C4EF8870B396D2158C3E92 (void);
// 0x0000050A System.Void Cinemachine.ClipperLib/ClipperOffset::set_ArcTolerance(System.Double)
extern void ClipperOffset_set_ArcTolerance_mF1025B552D6EB0FA28D7E6807397DC0DC4DC93B0 (void);
// 0x0000050B System.Double Cinemachine.ClipperLib/ClipperOffset::get_MiterLimit()
extern void ClipperOffset_get_MiterLimit_m12626F15B3A2B944F81E05ADBB263C8C3479D7DD (void);
// 0x0000050C System.Void Cinemachine.ClipperLib/ClipperOffset::set_MiterLimit(System.Double)
extern void ClipperOffset_set_MiterLimit_m81F85F0942E5A1B4A131627D7E7DFCF46BFCD859 (void);
// 0x0000050D System.Void Cinemachine.ClipperLib/ClipperOffset::.ctor(System.Double,System.Double)
extern void ClipperOffset__ctor_mEC075A2F9B5E587A9D06CF9E45A555417C69D158 (void);
// 0x0000050E System.Void Cinemachine.ClipperLib/ClipperOffset::Clear()
extern void ClipperOffset_Clear_mED8C219339D64A95987DB230DFE13C32FEC9CF87 (void);
// 0x0000050F System.Int64 Cinemachine.ClipperLib/ClipperOffset::Round(System.Double)
extern void ClipperOffset_Round_m5FB50144D5D0ABDE18741EC99B86BAF660F25F4F (void);
// 0x00000510 System.Void Cinemachine.ClipperLib/ClipperOffset::AddPath(System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>,Cinemachine.ClipperLib/JoinType,Cinemachine.ClipperLib/EndType)
extern void ClipperOffset_AddPath_m30CDF70BFA54E89D99CBDD267D513347A6D46EEA (void);
// 0x00000511 System.Void Cinemachine.ClipperLib/ClipperOffset::AddPaths(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>,Cinemachine.ClipperLib/JoinType,Cinemachine.ClipperLib/EndType)
extern void ClipperOffset_AddPaths_m48B4B426171D6EEAE676FCDA8E55F6FBBCEE66A8 (void);
// 0x00000512 System.Void Cinemachine.ClipperLib/ClipperOffset::FixOrientations()
extern void ClipperOffset_FixOrientations_m32685E41D88FB8B7FC06282DA7C128E197C7C518 (void);
// 0x00000513 Cinemachine.ClipperLib/DoublePoint Cinemachine.ClipperLib/ClipperOffset::GetUnitNormal(Cinemachine.ClipperLib/IntPoint,Cinemachine.ClipperLib/IntPoint)
extern void ClipperOffset_GetUnitNormal_mE79775AAC5E47077FE6EC3003830E30BA5D42786 (void);
// 0x00000514 System.Void Cinemachine.ClipperLib/ClipperOffset::DoOffset(System.Double)
extern void ClipperOffset_DoOffset_m21188ACCDBCEA566BFD6DEE0D03F9B5340CEF009 (void);
// 0x00000515 System.Void Cinemachine.ClipperLib/ClipperOffset::Execute(System.Collections.Generic.List`1<System.Collections.Generic.List`1<Cinemachine.ClipperLib/IntPoint>>&,System.Double)
extern void ClipperOffset_Execute_mB6E649005853A610D808D8E5F0CBB8EDE436D7C7 (void);
// 0x00000516 System.Void Cinemachine.ClipperLib/ClipperOffset::Execute(Cinemachine.ClipperLib/PolyTree&,System.Double)
extern void ClipperOffset_Execute_mAD1CC573A536C2365E3AE2290B6AB3EDA1D2FA03 (void);
// 0x00000517 System.Void Cinemachine.ClipperLib/ClipperOffset::OffsetPoint(System.Int32,System.Int32&,Cinemachine.ClipperLib/JoinType)
extern void ClipperOffset_OffsetPoint_mA364CFD851041996876AA12D1E32CB8CD56FD35F (void);
// 0x00000518 System.Void Cinemachine.ClipperLib/ClipperOffset::DoSquare(System.Int32,System.Int32)
extern void ClipperOffset_DoSquare_m6FF4AC6A3D3A432776BEA5EC3869CC1EED8807D8 (void);
// 0x00000519 System.Void Cinemachine.ClipperLib/ClipperOffset::DoMiter(System.Int32,System.Int32,System.Double)
extern void ClipperOffset_DoMiter_m2797F6E5BA1D568908043B4883ED4850EFAF39C3 (void);
// 0x0000051A System.Void Cinemachine.ClipperLib/ClipperOffset::DoRound(System.Int32,System.Int32)
extern void ClipperOffset_DoRound_m87BA50E7B0346922ACC73C69B6D28E9099E42692 (void);
// 0x0000051B System.Void Cinemachine.ClipperLib/ClipperException::.ctor(System.String)
extern void ClipperException__ctor_mD885137DA5F4345C11209699548F776B93180F1A (void);
// 0x0000051C System.Void Cinemachine.PostFX.CinemachinePostProcessing::PostPipelineStageCallback(Cinemachine.CinemachineVirtualCameraBase,Cinemachine.CinemachineCore/Stage,Cinemachine.CameraState&,System.Single)
extern void CinemachinePostProcessing_PostPipelineStageCallback_m4E796F5B2758A2E4DB5CCE4FCFCF6545BC248033 (void);
// 0x0000051D System.Void Cinemachine.PostFX.CinemachinePostProcessing::.ctor()
extern void CinemachinePostProcessing__ctor_mCB4146D6BBBE2356147A9DB6D2E0F349B89A908A (void);
// 0x0000051E System.Void Cinemachine.PostFX.CinemachineVolumeSettings::.ctor()
extern void CinemachineVolumeSettings__ctor_mB23ECA31DF611267B81182121229A3B5EF12014B (void);
// 0x0000051F System.Void Cinemachine.Utility.CinemachineDebug::ReleaseScreenPos(UnityEngine.Object)
extern void CinemachineDebug_ReleaseScreenPos_m6C54E91372A22F7D171D8D91C454DF06E95A6593 (void);
// 0x00000520 UnityEngine.Rect Cinemachine.Utility.CinemachineDebug::GetScreenPos(UnityEngine.Object,System.String,UnityEngine.GUIStyle)
extern void CinemachineDebug_GetScreenPos_m1C7476476BC6BB8D907CCF2D4B946C8B029872D8 (void);
// 0x00000521 System.Text.StringBuilder Cinemachine.Utility.CinemachineDebug::SBFromPool()
extern void CinemachineDebug_SBFromPool_mAA83D56A38ECFD61FC135792DC0778A7B152938D (void);
// 0x00000522 System.Void Cinemachine.Utility.CinemachineDebug::ReturnToPool(System.Text.StringBuilder)
extern void CinemachineDebug_ReturnToPool_mBFBCCF1AEE29E2BD115AF5BCD172BBB8B95C5EA7 (void);
// 0x00000523 System.Void Cinemachine.Utility.CinemachineDebug::.ctor()
extern void CinemachineDebug__ctor_mAF77C21C69B520883959BEC3DE58BBDD83CE5F8E (void);
// 0x00000524 System.Void Cinemachine.Utility.CinemachineDebug/OnGUIDelegate::.ctor(System.Object,System.IntPtr)
extern void OnGUIDelegate__ctor_mB8767C1FEE32279209BC7F763E7C133C62B92FB0 (void);
// 0x00000525 System.Void Cinemachine.Utility.CinemachineDebug/OnGUIDelegate::Invoke()
extern void OnGUIDelegate_Invoke_mA8B9CF3C0FA6CD716557C7E66D18F061E6410464 (void);
// 0x00000526 System.IAsyncResult Cinemachine.Utility.CinemachineDebug/OnGUIDelegate::BeginInvoke(System.AsyncCallback,System.Object)
extern void OnGUIDelegate_BeginInvoke_m7F8DBBFCF5F26BB61396A8DACB90A543E942EE20 (void);
// 0x00000527 System.Void Cinemachine.Utility.CinemachineDebug/OnGUIDelegate::EndInvoke(System.IAsyncResult)
extern void OnGUIDelegate_EndInvoke_m333ABE0CEA01B43E00AE0D77A7CE8279DDF2604F (void);
// 0x00000528 System.Single Cinemachine.Utility.GaussianWindow1d`1::get_Sigma()
// 0x00000529 System.Void Cinemachine.Utility.GaussianWindow1d`1::set_Sigma(System.Single)
// 0x0000052A System.Int32 Cinemachine.Utility.GaussianWindow1d`1::get_KernelSize()
// 0x0000052B System.Void Cinemachine.Utility.GaussianWindow1d`1::GenerateKernel(System.Single,System.Int32)
// 0x0000052C T Cinemachine.Utility.GaussianWindow1d`1::Compute(System.Int32)
// 0x0000052D System.Void Cinemachine.Utility.GaussianWindow1d`1::.ctor(System.Single,System.Int32)
// 0x0000052E System.Void Cinemachine.Utility.GaussianWindow1d`1::Reset()
// 0x0000052F System.Boolean Cinemachine.Utility.GaussianWindow1d`1::IsEmpty()
// 0x00000530 System.Void Cinemachine.Utility.GaussianWindow1d`1::AddValue(T)
// 0x00000531 T Cinemachine.Utility.GaussianWindow1d`1::Filter(T)
// 0x00000532 T Cinemachine.Utility.GaussianWindow1d`1::Value()
// 0x00000533 System.Int32 Cinemachine.Utility.GaussianWindow1d`1::get_BufferLength()
// 0x00000534 System.Void Cinemachine.Utility.GaussianWindow1d`1::SetBufferValue(System.Int32,T)
// 0x00000535 T Cinemachine.Utility.GaussianWindow1d`1::GetBufferValue(System.Int32)
// 0x00000536 System.Void Cinemachine.Utility.GaussianWindow1D_Vector3::.ctor(System.Single,System.Int32)
extern void GaussianWindow1D_Vector3__ctor_m24BDA3F6806B2C7687313112EF321052C4FFA574 (void);
// 0x00000537 UnityEngine.Vector3 Cinemachine.Utility.GaussianWindow1D_Vector3::Compute(System.Int32)
extern void GaussianWindow1D_Vector3_Compute_m50CFE925DFF71745A8032D15E19D650B6A4AB4A4 (void);
// 0x00000538 System.Void Cinemachine.Utility.GaussianWindow1D_Quaternion::.ctor(System.Single,System.Int32)
extern void GaussianWindow1D_Quaternion__ctor_m34E6A4BE5AEDDB27919FB447706477666FDEF7BE (void);
// 0x00000539 UnityEngine.Quaternion Cinemachine.Utility.GaussianWindow1D_Quaternion::Compute(System.Int32)
extern void GaussianWindow1D_Quaternion_Compute_m7482A5084DE01B7B0AAF60567BF9FBBE2C3A1738 (void);
// 0x0000053A System.Void Cinemachine.Utility.GaussianWindow1D_CameraRotation::.ctor(System.Single,System.Int32)
extern void GaussianWindow1D_CameraRotation__ctor_m93E64892C4CC9FFE4D20A5AE0EDB3761E0D357C5 (void);
// 0x0000053B UnityEngine.Vector2 Cinemachine.Utility.GaussianWindow1D_CameraRotation::Compute(System.Int32)
extern void GaussianWindow1D_CameraRotation_Compute_m5D42413D3C9A040A0E75B1B3FFFE24DA04628AD2 (void);
// 0x0000053C System.Boolean Cinemachine.Utility.PositionPredictor::IsEmpty()
extern void PositionPredictor_IsEmpty_m61F4928BCB8526CD0A823F2B2A46FDE04905C97B (void);
// 0x0000053D System.Void Cinemachine.Utility.PositionPredictor::ApplyTransformDelta(UnityEngine.Vector3)
extern void PositionPredictor_ApplyTransformDelta_mDA012CCA329F143DDF342616369F0E75B2E2C97A (void);
// 0x0000053E System.Void Cinemachine.Utility.PositionPredictor::Reset()
extern void PositionPredictor_Reset_mDA454522FB1823437E5538169D712A2E18F956C5 (void);
// 0x0000053F System.Void Cinemachine.Utility.PositionPredictor::AddPosition(UnityEngine.Vector3,System.Single,System.Single)
extern void PositionPredictor_AddPosition_mB5EFA6BB6598A9D52D1CE6BD50400E56938C433C (void);
// 0x00000540 UnityEngine.Vector3 Cinemachine.Utility.PositionPredictor::PredictPositionDelta(System.Single)
extern void PositionPredictor_PredictPositionDelta_mC16231F75C5C088B5CC2444D3C2FA12F6DACC4AD (void);
// 0x00000541 UnityEngine.Vector3 Cinemachine.Utility.PositionPredictor::PredictPosition(System.Single)
extern void PositionPredictor_PredictPosition_mB280F23A4D236037F339758BDEC4AD2DE89AB18F (void);
// 0x00000542 System.Void Cinemachine.Utility.PositionPredictor::.ctor()
extern void PositionPredictor__ctor_m98DC334F817608D8CA4FA09966193AA59A16DB25 (void);
// 0x00000543 System.Single Cinemachine.Utility.Damper::DecayConstant(System.Single,System.Single)
extern void Damper_DecayConstant_m66A0B4920185F5E89CB4ACA436BBA7DDDB7AFBBC (void);
// 0x00000544 System.Single Cinemachine.Utility.Damper::DecayedRemainder(System.Single,System.Single,System.Single)
extern void Damper_DecayedRemainder_mFDDE3E237019D299FCB912A50836D5B89714F048 (void);
// 0x00000545 System.Single Cinemachine.Utility.Damper::Damp(System.Single,System.Single,System.Single)
extern void Damper_Damp_m3245F3453DB74DE11801C6EE15F163D5EAFC0CE4 (void);
// 0x00000546 UnityEngine.Vector3 Cinemachine.Utility.Damper::Damp(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Damper_Damp_m3FF6416E2538374C5805A252094351151793F5E7 (void);
// 0x00000547 UnityEngine.Vector3 Cinemachine.Utility.Damper::Damp(UnityEngine.Vector3,System.Single,System.Single)
extern void Damper_Damp_mBBBCCE7F67FF9678EE42AA0B0EBD7BC4FFEF2FB9 (void);
// 0x00000548 System.Void Cinemachine.Utility.HeadingTracker::.ctor(System.Int32)
extern void HeadingTracker__ctor_m65E930C6FC3B44B9DE66B61332E4A960A14BE25B (void);
// 0x00000549 System.Int32 Cinemachine.Utility.HeadingTracker::get_FilterSize()
extern void HeadingTracker_get_FilterSize_mEF06A6674D9D5FE8F1802922DECACF11BA7BE151 (void);
// 0x0000054A System.Void Cinemachine.Utility.HeadingTracker::ClearHistory()
extern void HeadingTracker_ClearHistory_m220EDE26052383AFAD5F74F885541BF3797352A5 (void);
// 0x0000054B System.Single Cinemachine.Utility.HeadingTracker::Decay(System.Single)
extern void HeadingTracker_Decay_mCBB06FF8B9BC4A154A4EFC9DA910854ED0FB25F8 (void);
// 0x0000054C System.Void Cinemachine.Utility.HeadingTracker::Add(UnityEngine.Vector3)
extern void HeadingTracker_Add_m9FC794FA982A8598BC1FA0DB46EFAA7507CB861D (void);
// 0x0000054D System.Void Cinemachine.Utility.HeadingTracker::PopBottom()
extern void HeadingTracker_PopBottom_mCB0D0A7AE4238580CB7FFAAD17497F7B532B57AE (void);
// 0x0000054E System.Void Cinemachine.Utility.HeadingTracker::DecayHistory()
extern void HeadingTracker_DecayHistory_m9E2B8A0731C6C492AE78B36925860F4A3EFA1BB7 (void);
// 0x0000054F UnityEngine.Vector3 Cinemachine.Utility.HeadingTracker::GetReliableHeading()
extern void HeadingTracker_GetReliableHeading_m3277A5C1F94F1269E38655527EB71AACF594F695 (void);
// 0x00000550 UnityEngine.Vector3 Cinemachine.Utility.SplineHelpers::Bezier3(System.Single,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void SplineHelpers_Bezier3_mB25BD7261EDA22A9FB162738403E6DC66F70F326 (void);
// 0x00000551 UnityEngine.Vector3 Cinemachine.Utility.SplineHelpers::BezierTangent3(System.Single,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void SplineHelpers_BezierTangent3_m0CAB33B99E4DD03F36C592C5A95BCDC52C16BF27 (void);
// 0x00000552 System.Void Cinemachine.Utility.SplineHelpers::BezierTangentWeights3(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void SplineHelpers_BezierTangentWeights3_m2513B683B43808D72E1F58CFC79DBF1AEBB07A39 (void);
// 0x00000553 System.Single Cinemachine.Utility.SplineHelpers::Bezier1(System.Single,System.Single,System.Single,System.Single,System.Single)
extern void SplineHelpers_Bezier1_mAA7872DF66FA529E419AE4B19F25BDD9B28A7041 (void);
// 0x00000554 System.Single Cinemachine.Utility.SplineHelpers::BezierTangent1(System.Single,System.Single,System.Single,System.Single,System.Single)
extern void SplineHelpers_BezierTangent1_m41F1633A4094E5701EB543B445C8C1051CC4AA21 (void);
// 0x00000555 System.Void Cinemachine.Utility.SplineHelpers::ComputeSmoothControlPoints(UnityEngine.Vector4[]&,UnityEngine.Vector4[]&,UnityEngine.Vector4[]&)
extern void SplineHelpers_ComputeSmoothControlPoints_mF56B274A09DF5E4E77BC1BD1903C423FE9F1391A (void);
// 0x00000556 System.Void Cinemachine.Utility.SplineHelpers::ComputeSmoothControlPointsLooped(UnityEngine.Vector4[]&,UnityEngine.Vector4[]&,UnityEngine.Vector4[]&)
extern void SplineHelpers_ComputeSmoothControlPointsLooped_m8B1901AC903B71584D7A4F381F723F2DF41D319F (void);
// 0x00000557 System.Boolean Cinemachine.Utility.UnityVectorExtensions::IsNaN(UnityEngine.Vector2)
extern void UnityVectorExtensions_IsNaN_m9E064699098E04ADD8B174395C1902E85BBCC179 (void);
// 0x00000558 System.Boolean Cinemachine.Utility.UnityVectorExtensions::IsNaN(UnityEngine.Vector3)
extern void UnityVectorExtensions_IsNaN_mAED27A1EFF752377901140C13A7B586561F23745 (void);
// 0x00000559 System.Single Cinemachine.Utility.UnityVectorExtensions::ClosestPointOnSegment(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void UnityVectorExtensions_ClosestPointOnSegment_m5AF0D62D87EF8EF307260D87EA85BE2A4C35B85D (void);
// 0x0000055A System.Single Cinemachine.Utility.UnityVectorExtensions::ClosestPointOnSegment(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2)
extern void UnityVectorExtensions_ClosestPointOnSegment_m53E511FE9498F8B6FE6A0A001F2EE476BF4235A5 (void);
// 0x0000055B UnityEngine.Vector3 Cinemachine.Utility.UnityVectorExtensions::ProjectOntoPlane(UnityEngine.Vector3,UnityEngine.Vector3)
extern void UnityVectorExtensions_ProjectOntoPlane_m7B4042CE802D9E43593F9290EBCFFC1E1F1568A6 (void);
// 0x0000055C UnityEngine.Vector2 Cinemachine.Utility.UnityVectorExtensions::SquareNormalize(UnityEngine.Vector2)
extern void UnityVectorExtensions_SquareNormalize_mA45A9518904E5EF647E7D598B9ADA28EBF5D8E03 (void);
// 0x0000055D System.Int32 Cinemachine.Utility.UnityVectorExtensions::FindIntersection(UnityEngine.Vector2&,UnityEngine.Vector2&,UnityEngine.Vector2&,UnityEngine.Vector2&,UnityEngine.Vector2&)
extern void UnityVectorExtensions_FindIntersection_m679EF9DB24788CA733A9B241B305BF94A1E9ACC8 (void);
// 0x0000055E System.Single Cinemachine.Utility.UnityVectorExtensions::Cross(UnityEngine.Vector2,UnityEngine.Vector2)
extern void UnityVectorExtensions_Cross_m4ABE9CEBFA9687AB7A79F2287ABA20A011A514C0 (void);
// 0x0000055F UnityEngine.Vector2 Cinemachine.Utility.UnityVectorExtensions::Abs(UnityEngine.Vector2)
extern void UnityVectorExtensions_Abs_m0091B636E0155F99A8DA16B61C9372C03BB67EDC (void);
// 0x00000560 UnityEngine.Vector3 Cinemachine.Utility.UnityVectorExtensions::Abs(UnityEngine.Vector3)
extern void UnityVectorExtensions_Abs_m00E8851E28863473A992945FDA86B4CA0F388D3E (void);
// 0x00000561 System.Boolean Cinemachine.Utility.UnityVectorExtensions::IsUniform(UnityEngine.Vector2)
extern void UnityVectorExtensions_IsUniform_mD55546C76B4A999CE446261E535AF27FC7AD76AB (void);
// 0x00000562 System.Boolean Cinemachine.Utility.UnityVectorExtensions::IsUniform(UnityEngine.Vector3)
extern void UnityVectorExtensions_IsUniform_mAC18FF2205B1497324CFF4EF53ACF5D2D64A89E5 (void);
// 0x00000563 System.Boolean Cinemachine.Utility.UnityVectorExtensions::AlmostZero(UnityEngine.Vector3)
extern void UnityVectorExtensions_AlmostZero_mDE7F8E130BF5949DFF14AE437C47F086F3E05652 (void);
// 0x00000564 System.Void Cinemachine.Utility.UnityVectorExtensions::ConservativeSetPositionAndRotation(UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void UnityVectorExtensions_ConservativeSetPositionAndRotation_mC29BA282577CCD7F3BAE25E82115003BE4E0B036 (void);
// 0x00000565 System.Single Cinemachine.Utility.UnityVectorExtensions::Angle(UnityEngine.Vector3,UnityEngine.Vector3)
extern void UnityVectorExtensions_Angle_mFA715DF39B2BA00E0236B1DD527A53CE159A4C8C (void);
// 0x00000566 System.Single Cinemachine.Utility.UnityVectorExtensions::SignedAngle(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void UnityVectorExtensions_SignedAngle_mA8EE238FB571BF476038E19AAF311CB42605282D (void);
// 0x00000567 UnityEngine.Quaternion Cinemachine.Utility.UnityVectorExtensions::SafeFromToRotation(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void UnityVectorExtensions_SafeFromToRotation_m911F765BB02BF6A73073D9FEF708ACED0D55E564 (void);
// 0x00000568 UnityEngine.Vector3 Cinemachine.Utility.UnityVectorExtensions::SlerpWithReferenceUp(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3)
extern void UnityVectorExtensions_SlerpWithReferenceUp_m7F71658D673D705E004E2C256CBF33911519A1EC (void);
// 0x00000569 UnityEngine.Quaternion Cinemachine.Utility.UnityQuaternionExtensions::SlerpWithReferenceUp(UnityEngine.Quaternion,UnityEngine.Quaternion,System.Single,UnityEngine.Vector3)
extern void UnityQuaternionExtensions_SlerpWithReferenceUp_m8D94F55CE71358BD2A6B38511E97BAB9EDC55464 (void);
// 0x0000056A UnityEngine.Quaternion Cinemachine.Utility.UnityQuaternionExtensions::Normalized(UnityEngine.Quaternion)
extern void UnityQuaternionExtensions_Normalized_m62143839CCE5FA1B02E7953C1ABBB217CD9465FC (void);
// 0x0000056B UnityEngine.Vector2 Cinemachine.Utility.UnityQuaternionExtensions::GetCameraRotationToTarget(UnityEngine.Quaternion,UnityEngine.Vector3,UnityEngine.Vector3)
extern void UnityQuaternionExtensions_GetCameraRotationToTarget_mFD825219F752B2C979529CDCD655530685428FE5 (void);
// 0x0000056C UnityEngine.Quaternion Cinemachine.Utility.UnityQuaternionExtensions::ApplyCameraRotation(UnityEngine.Quaternion,UnityEngine.Vector2,UnityEngine.Vector3)
extern void UnityQuaternionExtensions_ApplyCameraRotation_m608B85CD86C6BE2FDD571446FA8CA596142C437C (void);
// 0x0000056D UnityEngine.Rect Cinemachine.Utility.UnityRectExtensions::Inflated(UnityEngine.Rect,UnityEngine.Vector2)
extern void UnityRectExtensions_Inflated_mF5A4FB7F7E25389F1CCB0B0F77C550BECC5ED031 (void);
static Il2CppMethodPointer s_methodPointers[1389] = 
{
	CinemachineCameraOffset_PostPipelineStageCallback_m36D3DE5935DCBF3866D1A04DE6C86232F6870C94,
	CinemachineCameraOffset__ctor_m48BC91DAD0744C7C8940F71C5645FA1AAE20E3D9,
	CinemachineRecomposer_Reset_m721C26E1921CC614A0B9653470D76120DA4A1A88,
	CinemachineRecomposer_OnValidate_m0118F16A3F380F1603D53C8ABAEAD111D9F3D45C,
	CinemachineRecomposer_PrePipelineMutateCameraStateCallback_m0F3A514A395A6A72371C8D11120240BC780CBE0D,
	CinemachineRecomposer_PostPipelineStageCallback_m44B330DFCC057842F064E3FFA08F5BCBC9BD728D,
	CinemachineRecomposer__ctor_mEE6D1CF234B300888E4018BA0B9E907EDC2EB5A0,
	CinemachineTouchInputMapper_Start_mCA2D226EC0ADDA714D6D5739643B42B426CD89C3,
	CinemachineTouchInputMapper_GetInputAxis_m31434BB01472E0F418BBA87C7EEFC67BC0286F93,
	CinemachineTouchInputMapper__ctor_mB558B3890882BD7E91A6F7D6796471BDE8A1371F,
	CinemachineMixer_OnPlayableDestroy_m8EF4D80AF627D5197E3A56CFB5471851682A5B2A,
	CinemachineMixer_PrepareFrame_m81662AAB1B6192B14A843280FE2B175BB9E914C6,
	CinemachineMixer_ProcessFrame_m418F0FEE9F0A0859D7E9FA0466BCBD86E8187BEC,
	CinemachineMixer_GetDeltaTime_mA23D38C7845030D33A6D94306FB6DC68D6E4C0B1,
	CinemachineMixer__ctor_m43226AD5B71D782F9A289DADDFA605F14EC5BAAB,
	MasterDirectorDelegate__ctor_m83539FCF0AC43523B96FD137E8AA5A42AB2B118F,
	MasterDirectorDelegate_Invoke_mBC7EB8EBD0BDA2D2046F24D4980B6715F096AB5E,
	MasterDirectorDelegate_BeginInvoke_mB3C2C71E773F03BB818A8122FA2A6C0CB9EA4EB6,
	MasterDirectorDelegate_EndInvoke_mD3784473798E58BF357BE527A7F6344035ADF6B6,
	CinemachineShot_CreatePlayable_m876B363449F4CBD92B79CF0D4098E86BCDCE1FB1,
	CinemachineShot_GatherProperties_mC2D43CED0267E1DBCC60FB7612D7426FAB56EEF4,
	CinemachineShot__ctor_m080F3FCCB80E98AD3F356CEB1792ADF91A6ECA58,
	CinemachineShotPlayable_get_IsValid_m36F22868B393BA5DA3FC41EA6591F1D0A560AB4E,
	CinemachineShotPlayable__ctor_m87875C5233C927BCFE76D23359C6F2482862EE82,
	CinemachineTrack_CreateTrackMixer_m8EE1F8D1A8E629AE03F5E77C662398C071065C5F,
	CinemachineTrack__ctor_mA1D06F056F153E3AE21729382FB666101343AC06,
	Cinemachine3rdPersonAim_get_AimTarget_m23E3401D3A53DAC3A76EE4A1352A14FC0DCA0AE5,
	Cinemachine3rdPersonAim_set_AimTarget_mE4F573A3F1927B24028F96164D800720DB5B68EC,
	Cinemachine3rdPersonAim_OnValidate_m7BAF5D55CC15037CC1150E710297600BD4BE6BA0,
	Cinemachine3rdPersonAim_Reset_mCEF67490840ED3EFBFBB78C04D8E4C8099D0C29E,
	Cinemachine3rdPersonAim_OnTransitionFromCamera_mFD1F710BCC846BB1AC464C7F620B93F6F665DF84,
	Cinemachine3rdPersonAim_DrawReticle_m9464DCF9E9F904E35F13CD5AF9BEF2A291E3286F,
	Cinemachine3rdPersonAim_ComputeLookAtPoint_m08DB1A8A0EDC4CAB21C46784AEE17504D488EC37,
	Cinemachine3rdPersonAim_ComputeAimTarget_mAD4E37AB5D0E30973FA3BFDD256B58047664EB3E,
	Cinemachine3rdPersonAim_PostPipelineStageCallback_mB571F3E81E8927115D710714F1A29A6550684D39,
	Cinemachine3rdPersonAim__ctor_m2525440B9C8CAA21C979B22C606725A0E785EB96,
	CinemachineBlendListCamera_get_Description_m0941C9392EECA1206B6BF679E780695C616E9B07,
	CinemachineBlendListCamera_Reset_mDD50F2FF236E2328EFBD97C6A99B8D2F4ECB0C83,
	CinemachineBlendListCamera_get_LiveChild_mE2463EEF9842BA071B0D5698CA98FFCF7F775BB5,
	CinemachineBlendListCamera_set_LiveChild_m134EFC93CFB6638A8E089E722D07F95C783F3091,
	CinemachineBlendListCamera_IsLiveChild_m71AB8ED658ACA24CF3C2D25C8027C63C01CA8D86,
	CinemachineBlendListCamera_get_State_mFA582C59AC2C5C06679C57F2284D44A052BEE226,
	CinemachineBlendListCamera_get_LookAt_m33F5E145090555ECC93D032BD4526D31AB77F017,
	CinemachineBlendListCamera_set_LookAt_m38A9835EAE1E1A4899FBABDCCC66ECAC58B9E5E4,
	CinemachineBlendListCamera_get_Follow_m8AA5DA6AF429EE8185E2740D944CCD175AFF4BD7,
	CinemachineBlendListCamera_set_Follow_mF060900F24D08F87FF981547B8D3C87C3BDF2AEB,
	CinemachineBlendListCamera_OnTargetObjectWarped_mA952BA4606E3780159339C519346BAB07AAD1ACA,
	CinemachineBlendListCamera_ForceCameraPosition_m6F04BCFD9AC1D8341BCFB7681C128A65BDEB1D69,
	CinemachineBlendListCamera_OnTransitionFromCamera_m7F58275919D731A8AA08A5C092E48CA007353DD5,
	CinemachineBlendListCamera_InternalUpdateCameraState_m40AC5E255043ECF1E3F1A298AE6DE61D4ABD922B,
	CinemachineBlendListCamera_OnEnable_mB815DE4599D5FBB447020211EAB63AC68B93513B,
	CinemachineBlendListCamera_OnDisable_m864D1836A3089997C125E1A2333A9C861236FC9B,
	CinemachineBlendListCamera_OnTransformChildrenChanged_m9669FDD4827F2174D9272D191D94CFA07B78E91F,
	CinemachineBlendListCamera_OnGuiHandler_mD5504D8F57DFD851B783D05F613921BBE4ECB2CF,
	CinemachineBlendListCamera_get_ChildCameras_mD71F131580DF9BF8AF30C04A88411DCF72F42502,
	CinemachineBlendListCamera_get_IsBlending_m104363E79BFE66E0E0B31105DBC0A5E4084DC66B,
	CinemachineBlendListCamera_InvalidateListOfChildren_m9C0D36C4AB842E080526AC77BADF7490C352DFCF,
	CinemachineBlendListCamera_UpdateListOfChildren_m999C8C3A9C7A0D9CE621661F76893F9A8678CA91,
	CinemachineBlendListCamera_ValidateInstructions_mFCDD468A1417A70852DAE2FBF1EBB0FAAD34CC3B,
	CinemachineBlendListCamera_AdvanceCurrentInstruction_mC401379B7CD61F5220AAF2B0510CB2833CEBC99D,
	CinemachineBlendListCamera__ctor_m90660A98A6ED5C8ECFCDF652585F12EFA4D70172,
	NULL,
	NULL,
	NULL,
	CinemachineBrain_get_OutputCamera_m1568A7744F9BB298C982829FC39B109309372C31,
	CinemachineBrain_get_ControlledObject_m4CD88A4899AEF4F49102BDB05CBA9B765359038A,
	CinemachineBrain_set_ControlledObject_mD9F1F9159DDE12F32E750A5C240E2A8A58DA8A6B,
	CinemachineBrain_get_SoloCamera_m67BF8A681B5B36B2CA6A805D658B5A820566B8D9,
	CinemachineBrain_set_SoloCamera_m36932C5D48A60ED8C5D09FA4BA122597F0EABE98,
	CinemachineBrain_GetSoloGUIColor_m52EAD28FC43A8FB8E68D9B81D31196203717746F,
	CinemachineBrain_get_DefaultWorldUp_m6A39EB2B1E0E480FB48E43F5DB484146FEB8F2E5,
	CinemachineBrain_OnEnable_m1A8E38FFC7712CAFEBF612C52AB10D1252DBDC54,
	CinemachineBrain_OnDisable_m87D60FA550ECBAE892D61B0AD4C5C90CE921A271,
	CinemachineBrain_OnSceneLoaded_m941252E4FC98544162388E3A6E68CAECE069650B,
	CinemachineBrain_OnSceneUnloaded_m08B136F6DC08111E8DC3AFD55ABF060E29EAD520,
	CinemachineBrain_Awake_m872E4DAC67A4E15E61766E50B54D977BC83FDE02,
	CinemachineBrain_Start_m6105C964B1E34A10A420525C978E5A012A053171,
	CinemachineBrain_OnGuiHandler_m3505267B18EB9CE74F62F6B14552A5F2182743FD,
	CinemachineBrain_AfterPhysics_mA5199823EA8CCF0C5391E2D4A8AA46A93853E68A,
	CinemachineBrain_LateUpdate_mEAC5D1D033ECDBA15E1E018E7727943FD7D890EB,
	CinemachineBrain_ManualUpdate_m0AAF1ECD02C1B65B0768D85DD4D505CD5C3A41CF,
	CinemachineBrain_GetEffectiveDeltaTime_mDCB1FDB9DB624E5511DD9F5298794C5753977ED9,
	CinemachineBrain_UpdateVirtualCameras_m209254C78DF1F06E11EA4CF30C7F953BDCED0218,
	CinemachineBrain_get_ActiveVirtualCamera_m0AEE8E6E46E3235D9ADE864DA6BE7BB57F2D86D7,
	CinemachineBrain_DeepCamBFromBlend_mA590E041B46933535A30A3F007BA2B83E7BE40CD,
	CinemachineBrain_IsLiveInBlend_m2A0E5AD2048B66FA82E7CB01A83335907B1941CB,
	CinemachineBrain_get_IsBlending_m028DE1EDFAD04881374A33196A718E7673E7B2F3,
	CinemachineBrain_get_ActiveBlend_mC6FF3504F32F7C30500A98F22BD906D468406ED1,
	CinemachineBrain_set_ActiveBlend_mE00036C78A72075BDEC47F0BA1AB052317ACCCB3,
	CinemachineBrain_GetBrainFrame_mFEAD91A91F349B785E8FC4B5A1C5F500EC9DEA40,
	CinemachineBrain_SetCameraOverride_m9A267D3B457F1C7681D972FD090F4DCCA00E49B8,
	CinemachineBrain_ReleaseCameraOverride_m40840EAC3788CBA1D998861D7F670EB7F79D1730,
	CinemachineBrain_ProcessActiveCamera_m6DD3871A02BB3BADC96C9D9DAB459C5F95D53104,
	CinemachineBrain_UpdateFrame0_mAA20AD58FBC4C024898A7074C3E4A0BE84DD53BC,
	CinemachineBrain_ComputeCurrentBlend_mA8E475FA0505785CB41BB62A5074FE09F3352E27,
	CinemachineBrain_IsLive_mC5661731D686A2BD2E926BE5F872C8F3033D3058,
	CinemachineBrain_get_CurrentCameraState_m4FD443F016CFCA5FCDFFF17E95A93162D18847C2,
	CinemachineBrain_set_CurrentCameraState_mFAA3A0AB30A002E3B9CC0BA83F9FF119428693DA,
	CinemachineBrain_TopCameraFromPriorityQueue_m1AE36BA00968869CC730BA006AF4409471F96B62,
	CinemachineBrain_LookupBlend_m4C4810AA410AE7A9E3D55E8B1E1DE5478AAE3398,
	CinemachineBrain_PushStateToUnityCamera_m052D86E4AADB90FC7816637DCE4375D86C44B0D8,
	CinemachineBrain__ctor_m516DC64F86617E7E269C399E53E69F06BF176690,
	CinemachineBrain__cctor_m0922906BE096A29914CAFDDE952DE34C0F5A208E,
	BrainEvent__ctor_mCAE49277912EB5AFFBA23BBBD84697BC258A56D7,
	VcamActivatedEvent__ctor_m4917789E0C570A5217E093044D8E26D8AD1F7068,
	BrainFrame_get_Active_m6EB1C7313FF391608D67E752A0AD3541B3D5D5E0,
	BrainFrame__ctor_mC0DB41887D1ED757D4F74BF031E2AAF607F7BADA,
	U3CAfterPhysicsU3Ed__38__ctor_mE033F15A46D3FD0237ABADFEB3D782ADE681BD4E,
	U3CAfterPhysicsU3Ed__38_System_IDisposable_Dispose_m8703F2ECD5260EF844C340F13AD765F936A3C8E3,
	U3CAfterPhysicsU3Ed__38_MoveNext_m6056E41EEF22CA8302EE1A09F74147B962477299,
	U3CAfterPhysicsU3Ed__38_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD8D086ECFE9EF92884C38C6F9339DB9B074347E3,
	U3CAfterPhysicsU3Ed__38_System_Collections_IEnumerator_Reset_m9F3C31823E4B368049D2F255C5705D54FE9B06D3,
	U3CAfterPhysicsU3Ed__38_System_Collections_IEnumerator_get_Current_mAC87E4A1BB6BB77033032B82B7B6308F8BFD94C8,
	CinemachineClearShot_get_Description_m830D661CB2C22A60CCCA38A64CFCB64759660EFD,
	CinemachineClearShot_get_LiveChild_m68AB5E861C65827DA91695C012A8C5EF2F6CA49C,
	CinemachineClearShot_set_LiveChild_m3BB96A8921B39DCCFDDF2091E780AD0BE9A211B1,
	CinemachineClearShot_get_State_m643D23A83988EAD2D9CB4B9979CEBF1716E9D2F0,
	CinemachineClearShot_IsLiveChild_mA941A1F0A4D4D73CAAE6D2331F5B36B28BEF6F5F,
	CinemachineClearShot_get_LookAt_m324E396C55C0E70D186F124BBD66D099896ADD35,
	CinemachineClearShot_set_LookAt_mF45391B864BFF22B5C2D6A717F2D5D5AF9671941,
	CinemachineClearShot_get_Follow_mA100668F5F7EB1E9E458369281641609393742C5,
	CinemachineClearShot_set_Follow_mE6584C1C0517D5FF4743A6191968B43EDA7E6222,
	CinemachineClearShot_OnTargetObjectWarped_m9D58039F25A57EA74EB96A763BB698DE9B3165FF,
	CinemachineClearShot_ForceCameraPosition_m2CC2714CBC099553603FB2FAAA2FE749BA9D67C3,
	CinemachineClearShot_InternalUpdateCameraState_m21E11A40459CC3BAD2FC423929BA502004BB063E,
	CinemachineClearShot_OnEnable_mE4470ED652FFFC8F50A2D1AD1A0B9442DBB1C5A0,
	CinemachineClearShot_OnDisable_m83BB1AD09642334F2723A7A378DBCFB08BA0031F,
	CinemachineClearShot_OnTransformChildrenChanged_mD3CDAD6B945F552A33D22047282151F8BEC23ECE,
	CinemachineClearShot_OnGuiHandler_m3278977602C7A7D938A92BDA13782A57AFB32799,
	CinemachineClearShot_get_IsBlending_m23069403051CC3F2737C0DE0C23A47360C8562CF,
	CinemachineClearShot_get_ActiveBlend_mFB3601D76976133C2A35D860F7E54A6B0FD8661F,
	CinemachineClearShot_get_ChildCameras_mF4017037F2C43479CB50DC87BA28BB5436C2BF61,
	CinemachineClearShot_InvalidateListOfChildren_mFE7676A3C7D11C5BC7F204BC437D6F31692B81DE,
	CinemachineClearShot_ResetRandomization_mA4C0B56EE654F56A3E6219A4BDB8835E30A3510A,
	CinemachineClearShot_UpdateListOfChildren_m3BCC28E6A250B389B8FE737800409B3B6E235253,
	CinemachineClearShot_ChooseCurrentCamera_mE99EB0A510F96CBBF70A18692ACC962927641265,
	CinemachineClearShot_Randomize_m827ED70BADD0E80B15B02B3DEB15D5BFFE0F0726,
	CinemachineClearShot_LookupBlend_m0DA62E97306A03CF6C1A0CADF68A0D3E7F45BD26,
	CinemachineClearShot_OnTransitionFromCamera_mCD2B8D5BB280F63D0E7E26A32E9C9D8C8DA2DCBF,
	CinemachineClearShot__ctor_m08FE4B603DA5147A26D2F588496C2D29B4CA4407,
	U3CU3Ec__cctor_m862E9E374DE10AA80B867C44F2270190CAE8FB00,
	U3CU3Ec__ctor_m9BE881E5BAC3662017282D2C1215496CB6A6088F,
	U3CU3Ec_U3CRandomizeU3Eb__49_0_m60CCAD65D9F7AF30D47B1D1371FE7D708D009FFD,
	CinemachineCollider_IsTargetObscured_m96234E8EC6FBE238EF157CD24CEC943D0BCB5867,
	CinemachineCollider_CameraWasDisplaced_m5ED18CC78ED65A1CD9D46C1305102A66FB5EFA2E,
	CinemachineCollider_GetCameraDisplacementDistance_m2D776E08DF6628DE5D5DDAFE5854D28A2E4C36B6,
	CinemachineCollider_OnValidate_m44D1FA5E930E6EDEF78DD0AA50B0C0A31214EEF3,
	CinemachineCollider_OnDestroy_m9F8D62EC841589145D4216849773715675EC67EA,
	CinemachineCollider_get_DebugPaths_m75A49F8F027244F2D5421164CA8588670F3628D0,
	CinemachineCollider_GetMaxDampTime_m5CDDC02FB1FEB4B82A51BA9745D61DE0EFA23BF1,
	CinemachineCollider_OnTargetObjectWarped_mF64F1DDBA4302BF278BE4555C9718F8B2795A7DE,
	CinemachineCollider_PostPipelineStageCallback_m809856B0A1F1112AA1610C853672E5B9FEFBCD2F,
	CinemachineCollider_PreserveLineOfSight_m15D0A6B0A018DAD34254B7E091CAD3D3E0C1EF84,
	CinemachineCollider_PullCameraInFrontOfNearestObstacle_mC6EE17778ED949B252ECEEF01262A6391180D176,
	CinemachineCollider_PushCameraBack_mA652E6C1E69285E05F18DA6C4EEFBF6FFF150ED5,
	CinemachineCollider_GetWalkingDirection_mC5607290A0F1FB8EDAC4AE401280FCAE25DA56D7,
	CinemachineCollider_GetPushBackDistance_m327096D12B295C2A3D536767D22302B0D90722F2,
	CinemachineCollider_ClampRayToBounds_m1ECEE60359130905CFE336780028F27C584C223C,
	CinemachineCollider_RespectCameraRadius_mCB35CD04F62EDCBE0A154B3EC455D33029F380C6,
	CinemachineCollider_CheckForTargetObstructions_mBE0D7797C4F714F6532ECF0428225FCC7717EBE9,
	CinemachineCollider_IsTargetOffscreen_m92E26E85E88FE62A1C918DEF9728DE9FB46D5A7D,
	CinemachineCollider__ctor_m444FCEC0E8391FB73605BDF0C844292EA81BDF12,
	CinemachineCollider__cctor_m5A26E23CA4A900A4A0C35BFB46F4EBD893459106,
	VcamExtraState_AddPointToDebugPath_m5787D51C699C1557B53987E5134067F98C6B8296,
	VcamExtraState_ApplyDistanceSmoothing_m0BB6D66F3143716C66AAC3F7DD85103C7E6589E3,
	VcamExtraState_UpdateDistanceSmoothing_m585CE40738CF2D7E9EC0BE89C357AD84A40320AC,
	VcamExtraState_ResetDistanceSmoothing_mC71B210379ABEBC812529240E983852865E535B0,
	VcamExtraState__ctor_m1453192FF3335F6FCB28CBF592949C721553225E,
	CinemachineConfiner_CameraWasDisplaced_m4B54B58BECF591B468EFB069B776C6A647CAFCD0,
	CinemachineConfiner_GetCameraDisplacementDistance_m8DF8F6DB8A7FB9E1C6FF59F90CB2EAA54FFE5B54,
	CinemachineConfiner_OnValidate_mD68867689E0CCB06D68F538F50CA7E8279520E1F,
	CinemachineConfiner_ConnectToVcam_mFFA1FD919F341B7EEF2D2C6B6D9CEC32B3E98E55,
	CinemachineConfiner_get_IsValid_mE3539CF8E20004A17B9F2EFA432008AB6AFB47BE,
	CinemachineConfiner_GetMaxDampTime_m0131BF6E586F7B2A942C73860AF613239799C219,
	CinemachineConfiner_PostPipelineStageCallback_m9610D6CFAFCBAD9960646A4019F9D81C0BCCC374,
	CinemachineConfiner_InvalidatePathCache_mFA907955B4C61CBAA3ACD9A7AED13D621FA5A11F,
	CinemachineConfiner_ValidatePathCache_mDAF15DF5F5AACDBC66DCFFFAEF2B6051F37BC9D0,
	CinemachineConfiner_ConfinePoint_m0A14A9BF85C02366E350E1EC81A1DE148DC80B86,
	CinemachineConfiner_ConfineScreenEdges_mE8CC4E3F1B7174138AE889D6D30FEA594A8FDBD6,
	CinemachineConfiner__ctor_m9ECC25FEE5601874435238F3ECECCEFA7D81DCBC,
	VcamExtraState__ctor_m48E5BAB2FDF7BAD3C985A2748834A2E817A845CE,
	CinemachineConfiner2D_InvalidateCache_m23A7A98BCB8232C84FF3D926CF349507FE2AB1F5,
	CinemachineConfiner2D_ValidateCache_m8FB8D402430AAABEB978E0556BC38A0256749E21,
	CinemachineConfiner2D_PostPipelineStageCallback_m47AE9484EB1757CF28AB748E796712689A65EFB6,
	CinemachineConfiner2D_CalculateHalfFrustumHeight_mC47DB1A291F62784AB03D741DD0DB4ECF8B3A182,
	CinemachineConfiner2D_OnValidate_m0FB17BA22FDE2D9BC32275280FFC68C162700025,
	CinemachineConfiner2D_Reset_m074BE83016F529CFFD82ED0F34FA9B33DF1210B4,
	CinemachineConfiner2D__ctor_m9235E19358FC51F49B2068BA645C74BD8F4B2A66,
	VcamExtraState__ctor_mEEA50920B90E79DAFDCAAF5EAFD9837371AC1CE1,
	ShapeCache_Invalidate_m5D7E303E5E807F2388F06AD1054E5CCD8D36D6C3,
	ShapeCache_ValidateCache_mD71A58F6BBBAC79F5BB6560704C33935C77A76DD,
	ShapeCache_IsValid_m2AA4B5B68785B4D264FB2D8DECD0163D6FAAC0D9,
	ShapeCache_CalculateDeltaTransformationMatrix_m74CA864B0DDA47E56B35B3CA1380C35FEA603784,
	CinemachineDollyCart_FixedUpdate_mB5D80C62F5DF944DE98DAA79F23E9E677216D9AE,
	CinemachineDollyCart_Update_mF2C2A034A4659BCAE045E6D59E910F623A8B69A4,
	CinemachineDollyCart_LateUpdate_mE77BE2CAB8C334BF046D4C691F65C8DC591708BB,
	CinemachineDollyCart_SetCartPosition_m8D2EB52AEFEE154042511DC15A0CB6CDDDC15C71,
	CinemachineDollyCart__ctor_m4FEC8B62A80B5094F3115C0F4B41F86D6EF11B3D,
	CinemachineExternalCamera_get_State_mBABCF116AACDF9F7B1EC287AE7C1635528E7EED3,
	CinemachineExternalCamera_get_LookAt_mF881F376FB11ACA576E57B715D4034745816C915,
	CinemachineExternalCamera_set_LookAt_mD0250A3A73E2CC5944FAEAE32A26483770A5518B,
	CinemachineExternalCamera_get_Follow_m2916C53ED295FB44E3CE86AECE2307E65F19C069,
	CinemachineExternalCamera_set_Follow_m9E81314AF50F6886533FB8C8150FBE511EF3956F,
	CinemachineExternalCamera_InternalUpdateCameraState_m0F6CD90D9B9C26B6EF5DB147F76C860ACED29EE5,
	CinemachineExternalCamera__ctor_mC0CAEDCB42F896DA0DA3428A0EF7833DFC5FC19B,
	CinemachineFollowZoom_OnValidate_m13A1978C9D7F5EFC9BF01222449A20EB0E9093CF,
	CinemachineFollowZoom_GetMaxDampTime_mF2A9EFE150660B8EDD7A77C17AE91E1A038AE161,
	CinemachineFollowZoom_PostPipelineStageCallback_mA40A25C1B4DCDD0B6A379F79E51A301EB3E31455,
	CinemachineFollowZoom__ctor_mC5DAA40532992DFCA0F91904996B0B3897F1FB99,
	VcamExtraState__ctor_m0FB88B41FA5F8BD8063E9D409580F36352C45C1E,
	CinemachineFreeLook_OnValidate_m363CFBDA6195290F8CA67B2217CF1CF0F080AF08,
	CinemachineFreeLook_GetRig_mC818384734A05604CCA51B5D6EA87FD91692CE01,
	CinemachineFreeLook_get_RigsAreCreated_m0EF51109842812390E9FD4D4DA9539CAF415DC03,
	CinemachineFreeLook_get_RigNames_m2D9317645B40626F534D57764C904CB434416D66,
	CinemachineFreeLook_OnEnable_m5751340BED71741D4B1ED9EB5D613DC8CCEEEBB9,
	CinemachineFreeLook_UpdateInputAxisProvider_m3A81D0027CC71494F03A9F7719377DB881FD5606,
	CinemachineFreeLook_OnDestroy_m5C53E6195807FE9AEF38B2BF06B88B0F9CCEE566,
	CinemachineFreeLook_OnTransformChildrenChanged_mE19235181CB5C85CECE97956BBA126F38F348C95,
	CinemachineFreeLook_Reset_mAC8C0893352644CE2C0C70389AB32E8D20C6EB2C,
	CinemachineFreeLook_get_PreviousStateIsValid_m4CAC7225F4A310446049C315D2BA4905DEFA6D91,
	CinemachineFreeLook_set_PreviousStateIsValid_m07EFCE7F6A806488A86EF327582638ADA4255EC6,
	CinemachineFreeLook_get_State_m13B7AD3940C466FC4C874CD5F1A9CAA4B6A321F4,
	CinemachineFreeLook_get_LookAt_mE9065E51E77ACA8FE7CB8788F5D91F250DC5E0AC,
	CinemachineFreeLook_set_LookAt_m4DB3D49CF03F62C0585D34F051D06F2C8A13929D,
	CinemachineFreeLook_get_Follow_mB0013712098265B408811C0EA19C35179F1E4F65,
	CinemachineFreeLook_set_Follow_mC3D18085B595FAEE0549F5FB957E380B96ADD041,
	CinemachineFreeLook_IsLiveChild_m693A2AC305EAD54F31A5F3FA0494D242D1C66F65,
	CinemachineFreeLook_OnTargetObjectWarped_mA77D864BEC0A4204CBE065D077904941C29ED3CA,
	CinemachineFreeLook_ForceCameraPosition_m4C9B3A953CD78C0151638A6EB22E5723E2E2F18C,
	CinemachineFreeLook_InternalUpdateCameraState_mBC6EFA124C0307703CC6F169A3A4C3AEDA6B8C16,
	CinemachineFreeLook_OnTransitionFromCamera_mAD0C40F31D468A93C2B98EC909B8DD942A0EA21F,
	CinemachineFreeLook_RequiresUserInput_m3A9F28FA422DC8CECCB7C9B948DB1413177C8F0E,
	CinemachineFreeLook_GetYAxisClosestValue_mF6FAD00F83ABD48C48ED1B601960B78EE1D515FA,
	CinemachineFreeLook_SteepestDescent_mD0C5E94C99FEB356B454D145AE4DC24BDC3E285C,
	CinemachineFreeLook_InvalidateRigCache_m32D43BE156C871062C4AB803167753025541915F,
	CinemachineFreeLook_DestroyRigs_mB5D2ACF7E82A9274864492651FF1E89F52CEF61F,
	CinemachineFreeLook_CreateRigs_m7F1B92C30B61F914D287F9C21204E4D43D9CF695,
	CinemachineFreeLook_UpdateRigCache_m00120D105154A191F750E816B03B96E7F536465D,
	CinemachineFreeLook_LocateExistingRigs_m52CEC6667817F4620E9DD2E8C3B5FF455AACFDB6,
	CinemachineFreeLook_UpdateXAxisHeading_m0B3B9A759E52F9B1315784D2D69144BF6290F23C,
	CinemachineFreeLook_PushSettingsToRigs_mB5BABAC99655EF23A3C9A0C7AF8F5AE0736F4764,
	CinemachineFreeLook_GetYAxisValue_m3006D2D07A4A8D430C3FD2CBD51B74B8D75D8E77,
	CinemachineFreeLook_CalculateNewState_m1F68499B410474DAD5C866BAE393F9184DC3C59D,
	CinemachineFreeLook_GetLocalPositionForCameraFromInput_mEF44FF027954B4E5392A082E9EE82879B8489062,
	CinemachineFreeLook_UpdateCachedSpline_mC36FEF90EF6D07349BF33559D9B9B84DB7004473,
	CinemachineFreeLook_OnBeforeSerialize_mEEEFD1F60C989CF81C9D7C057FCA2CA187892B07,
	CinemachineFreeLook__ctor_mA79F3B7F572AFECE180B79550161A5CA8F99D2BA,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__AngleFunctionU7C47_0_mCFC89952A9D256117954C562141B92F4A2864CFD,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__SlopeOfAngleFunctionU7C47_1_mDF48B916ABBB4ED512EFFC07EB1192E7046FFF78,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__InitialGuessU7C47_2_m8E7D6612A2BAB6E1E8378B3DBC739BCAA0408924,
	CinemachineFreeLook_U3CSteepestDescentU3Eg__ChooseBestAngleU7C47_3_m0B66CC7A9760FA805C041D04C7A526B0CADBA8EB,
	Orbit__ctor_mEB696AB489A6AE5AB5E7D9A366CC741BD8CA23A1,
	CreateRigDelegate__ctor_m6F6CFEC727ADF8F761C0056334415C9ED1549E89,
	CreateRigDelegate_Invoke_mD0C181884C0203123DE69968919E4E757D0891A9,
	CreateRigDelegate_BeginInvoke_m4EAA983AAAB1A3D011BAD83EE4C2F78C0FD3F292,
	CreateRigDelegate_EndInvoke_m3E5499B7F9B4885E2EB7C8B47E3D6AD1CB8A0814,
	DestroyRigDelegate__ctor_m780F29C1662666F2141E7006CFB0C8F0652675F6,
	DestroyRigDelegate_Invoke_m0123F51EB7D308EDBBA8C97D1766CA6644C0BB26,
	DestroyRigDelegate_BeginInvoke_m79C7D4F44884457F4626AF19E71F7C4E4D012B21,
	DestroyRigDelegate_EndInvoke_m7903709A25979E37D5542712A29559AB5984345B,
	CinemachineMixingCamera_GetWeight_mB558B38DFD8EBE6D7ADA9E8ADEE2693E64FF5D42,
	CinemachineMixingCamera_SetWeight_mC5610C703FF2805EBEA1F154BBCE444DDD242194,
	CinemachineMixingCamera_GetWeight_m83BF99075E8598CBDCFE55582A0A7FC77B9A9104,
	CinemachineMixingCamera_SetWeight_m2BF760E1983299AE8F5F5B9D01DAB3EE1332061E,
	CinemachineMixingCamera_get_LiveChild_m26D809A14332652C435B82A94EE0282E0561F817,
	CinemachineMixingCamera_set_LiveChild_mD28EB775418289F251C97828FD7E5BF55F39C81F,
	CinemachineMixingCamera_get_State_m44E23B7EEA1FD0097519789D7DA2AE7626D8F955,
	CinemachineMixingCamera_get_LookAt_m960F48555B39A69B413D665CD807CA2DE57DA7AD,
	CinemachineMixingCamera_set_LookAt_m6C2A62904E3B6AC74AFD6B0990F9B79F3F015C57,
	CinemachineMixingCamera_get_Follow_m54220CF80B21E7625F10D7EBCF3B2F552C055049,
	CinemachineMixingCamera_set_Follow_m71453E2740CEF75552DEAB376B40AD533A8C8FF3,
	CinemachineMixingCamera_OnTargetObjectWarped_m5762D3BDA766EB34FD869C5C858FF6C03CF935E0,
	CinemachineMixingCamera_ForceCameraPosition_m5DEFC312F29680754ED0F18260B4834F546F77B9,
	CinemachineMixingCamera_OnEnable_mA1CA5ED5B17241E0E896B3DA0A016B5A748A8C05,
	CinemachineMixingCamera_OnTransformChildrenChanged_m450976DCA651635EA5D669F4E5CCBF384A5FB61B,
	CinemachineMixingCamera_OnValidate_m2CA0AF2221650AE7A37D2709A554CE12B4456E0F,
	CinemachineMixingCamera_IsLiveChild_m91A57CA82BB9F6EF14A0E42839E3365C390B0BD4,
	CinemachineMixingCamera_get_ChildCameras_m7F3535370ECD871E57E965197E774E2F12247D16,
	CinemachineMixingCamera_InvalidateListOfChildren_m51313DE550C0BB626467A05BEDF34B7120874BD5,
	CinemachineMixingCamera_ValidateListOfChildren_m57026964CD2CE933128EACC1EB1C2550316D938D,
	CinemachineMixingCamera_OnTransitionFromCamera_m276EA6CE8376BE27DDCF3EF7DF89BE4A492FF505,
	CinemachineMixingCamera_InternalUpdateCameraState_mED94FAACA5FA2E23DFD1A925B176E69C6825C35D,
	CinemachineMixingCamera__ctor_m0B1F309D2D31EB2803EB13DE6CAFABB66865E54C,
	CinemachinePath_get_MinPos_mBA8D84B82E247993FE9B877B1AEC594E1E3D7058,
	CinemachinePath_get_MaxPos_m83D806FD19414D91B55CF8D96CBF6408090CB1BD,
	CinemachinePath_get_Looped_mAF77015A018DD14449E5798FB2EECC608465F947,
	CinemachinePath_Reset_m3D879DC9DC1148FDFB117998B7DF784D559D4241,
	CinemachinePath_OnValidate_m09378574E3D52E520FF10A675E41753CEF20610B,
	CinemachinePath_get_DistanceCacheSampleStepsPerSegment_m7B01348994DB96DD02D21782AA1EC501F1022248,
	CinemachinePath_GetBoundingIndices_m75531D6E4BE5271966BCBE5719272BDD52D7D31A,
	CinemachinePath_EvaluateLocalPosition_mFA1EE82F5816919E5938FF33F05B85A831E0E470,
	CinemachinePath_EvaluateLocalTangent_mF16B5FCDFFE4FAF04E2F3BB061F6CD54E7A814E3,
	CinemachinePath_EvaluateLocalOrientation_m9DD2FF1FCA7A124F47616F83E3349395E7EC2BA6,
	CinemachinePath_GetRoll_mF083C5544E3986F2E8BF9E2B2B264A46041B387E,
	CinemachinePath_RollAroundForward_mAB9F97C90959DCC810F5A0373DED357E9FCAFBF3,
	CinemachinePath__ctor_m2AC66AF8FA31A0F8CA731812E8BF30FDFD33E90F,
	CinemachinePipeline__ctor_m88F828229691C91F2DFF546084A80215324B9BF2,
	CinemachinePixelPerfect__ctor_m4422D053361DF8CD626CB21B60EF4507600365B6,
	CinemachineSmoothPath_get_MinPos_m0EB8C61879F4C2829CDC4B29F5B9B87D6C142F2D,
	CinemachineSmoothPath_get_MaxPos_mD2A934FC5741EEF3E529BEAEB4D7D8ADC458B462,
	CinemachineSmoothPath_get_Looped_mA3CAB564800423C040DF4685BDB585FEA28E069E,
	CinemachineSmoothPath_get_DistanceCacheSampleStepsPerSegment_m8167360E02FD34C85F80758C7F7654A53294A239,
	CinemachineSmoothPath_OnValidate_m8F7D11F9B015CCE4B6C1AB859AE8ACB0488FDACD,
	CinemachineSmoothPath_Reset_mD9A09F954797E3D506B9F44DA87ECD9AA6BD69E2,
	CinemachineSmoothPath_InvalidateDistanceCache_mFB359B4FB36D7FA2636769A2091C9C63E9E929A1,
	CinemachineSmoothPath_UpdateControlPoints_mBF744AB3B5D5E83B6DBC4769188A8D9615CFAF23,
	CinemachineSmoothPath_GetBoundingIndices_m5ABF4523E4FEAEC313961640BBD3776823458AA0,
	CinemachineSmoothPath_EvaluateLocalPosition_m56B739A27AF201BED9E37E3FE54E4E5057FED809,
	CinemachineSmoothPath_EvaluateLocalTangent_m4C16F43B9455B1496F9881369FE3A5A06413B0B7,
	CinemachineSmoothPath_EvaluateLocalOrientation_m692FFED8959B97DE3C889F5FB2CD0D1CB505120E,
	CinemachineSmoothPath_RollAroundForward_m793878EE596319781A69AED4641503616F6BD70A,
	CinemachineSmoothPath__ctor_m803C3FD43A12B6D618F661D748EFB66D7D8D5CE9,
	Waypoint_get_AsVector4_mA3935DAA3EBC69BEF2DDD1AA4DA077A0B520FA97,
	Waypoint_FromVector4_m4BC6E89C093AE2B43B656C0B016927580FA0523A,
	CinemachineStateDrivenCamera_get_Description_m7DAF9ACF241B7726B73AE384C54F69B9D544C898,
	CinemachineStateDrivenCamera_get_LiveChild_m516C9074D2AD76558E48C1D237B0C4598325EA21,
	CinemachineStateDrivenCamera_set_LiveChild_mE87CE48A20127BE305780929D56EEA8824967D2B,
	CinemachineStateDrivenCamera_IsLiveChild_m5ED62A5DD53C1F2896B79CC1D92B66DF9A1035D8,
	CinemachineStateDrivenCamera_get_State_m022C21ACA96103A7D7EDA5166BBE692E66B5C7C1,
	CinemachineStateDrivenCamera_get_LookAt_m2EF675C4940B5DC5027AEE6FCE781E6616B7D12E,
	CinemachineStateDrivenCamera_set_LookAt_m7288F2C8A0E2817CC2602A6D8C4CF47684828F1B,
	CinemachineStateDrivenCamera_get_Follow_mEB2BEBF5DD846A0E8C9043878CA65756D994A31B,
	CinemachineStateDrivenCamera_set_Follow_m68B6BA289A4F82A12CDC5C90607E0B199929688C,
	CinemachineStateDrivenCamera_OnTargetObjectWarped_m5A48D9430DE68881A8461861C6C328C409642B81,
	CinemachineStateDrivenCamera_ForceCameraPosition_m8CD7FA67FC7F315DBA29E55B232BA0CF58D65766,
	CinemachineStateDrivenCamera_OnTransitionFromCamera_m0274DB41D0267BF7F98485C8237D3DA5DE33A9B0,
	CinemachineStateDrivenCamera_InternalUpdateCameraState_m228EA511EF58839BF7D568D5ABBFA798C66E7BC3,
	CinemachineStateDrivenCamera_OnEnable_m9A483CA25DF77C94320EBE910B68510CBF3BA028,
	CinemachineStateDrivenCamera_OnDisable_m59B90104BF9A354FB8E0AE420256DA254369372F,
	CinemachineStateDrivenCamera_OnTransformChildrenChanged_mD5BAF106BBAA3C3486F27E3033B7E239F9C64F4F,
	CinemachineStateDrivenCamera_OnGuiHandler_m3B5FB8E32B3DD5117D5CF526C60954FE3362C094,
	CinemachineStateDrivenCamera_get_ChildCameras_mD8D60F4EA1F55C19391664B0AAFB7C52069A5495,
	CinemachineStateDrivenCamera_get_IsBlending_mFCDE3B87EE0174789D932F463D24D14DF7439487,
	CinemachineStateDrivenCamera_get_ActiveBlend_mBF5C9CD7F163E2B80AB71D95F698E8B8CF0BD551,
	CinemachineStateDrivenCamera_CreateFakeHash_mAF4399836F328EB3C463A7B2A7770A5AB2139438,
	CinemachineStateDrivenCamera_LookupFakeHash_mBF568073EFB952D5BA865BAE8276784223E1A0E6,
	CinemachineStateDrivenCamera_InvalidateListOfChildren_m07ED390483719F9053DB9117792E820F275B0FA0,
	CinemachineStateDrivenCamera_UpdateListOfChildren_m6CC5B12DF863B54139D694443F28397107605FEE,
	CinemachineStateDrivenCamera_ValidateInstructions_mA7871412AAEBC15A8FA1C8773FEC36D353819FEB,
	CinemachineStateDrivenCamera_ChooseCurrentCamera_m903D1018B149F87E1D4A04BEC484B48167198C0F,
	CinemachineStateDrivenCamera_GetClipHash_mB11C513BC9DB5F594476FC4344DE0CD9DB24CE43,
	CinemachineStateDrivenCamera_LookupBlend_m93E3D457BD2927474EC7CCB819AD0066D68E20A0,
	CinemachineStateDrivenCamera__ctor_mDD326FD6ABFBC350851FAF6B34E15AFB379E9301,
	ParentHash__ctor_m6CD157CE916B29AC191604A0283607464CCC0DDD,
	CinemachineStoryboard_PostPipelineStageCallback_m4C51937C12AC6ECFD2B3F9EA412A70C67E9D43EE,
	CinemachineStoryboard_UpdateRenderCanvas_m7353541228B6BD3B22EA978086F8733413B78984,
	CinemachineStoryboard_ConnectToVcam_m53BF8C555BCDBF2898BBEC9BAFA6A5FFF04FBF79,
	CinemachineStoryboard_get_CanvasName_m677394CD1576D23EFA8E63B4CB5782B65BA44D00,
	CinemachineStoryboard_CameraUpdatedCallback_m96F15CE066264D8F7AB1C2046C416FAC345E234B,
	CinemachineStoryboard_LocateMyCanvas_mAB2D252AAA3C187099D16446889579B184E8AABD,
	CinemachineStoryboard_CreateCanvas_mE4DFBBDB8F034263F315169F6026BF4292026F6C,
	CinemachineStoryboard_DestroyCanvas_m809C169B130220231748965F4BFB3FBC51338FC2,
	CinemachineStoryboard_PlaceImage_mC8B2650D936D682C2C031D89FF260DCD4F92FBDB,
	CinemachineStoryboard_StaticBlendingHandler_mCB24F0CFB83E0D0E42A6BEE8EA45681FDA02336A,
	CinemachineStoryboard_InitializeModule_mB460164515FB890392CD51A78F9D6FC1029571F7,
	CinemachineStoryboard__ctor_m13A6C495D5EE7EE171921D6A313B6D95083ED19E,
	CanvasInfo__ctor_m350FBA5D640FFCC2883C94DD425CBBA36DD30449,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CinemachineTargetGroup_OnValidate_m4F3F25B43C64B450CB8B2D2B493D969168B15805,
	CinemachineTargetGroup_Reset_mFEEC71DBC62A2D72609BE4AAE08D8535BD86CD57,
	CinemachineTargetGroup_get_Transform_mEE90686C94ECD8C321A1949D991608A1D6B3B4FA,
	CinemachineTargetGroup_get_BoundingBox_m9DAA8563350BEF0A06952DBCABCEB9C76A4192BF,
	CinemachineTargetGroup_set_BoundingBox_m6A764BCB851DEB901428606CB4C6E35A422E40A9,
	CinemachineTargetGroup_get_Sphere_mF3A2B9159CB6C6D430B73BF4BB54259FFF077CD5,
	CinemachineTargetGroup_set_Sphere_m7247E4BA3C06380A2A36EDD6871D5959E00D8F1B,
	CinemachineTargetGroup_get_IsEmpty_m306811C02A8D1B747E2CECE27ED6C81779034BF8,
	CinemachineTargetGroup_AddMember_m35E32FE8C1D431B8E9F9D926401426C712EAA157,
	CinemachineTargetGroup_RemoveMember_mEE444661E6C4AE23B5E5632CE4F1C806CEDA3063,
	CinemachineTargetGroup_FindMember_m763EA250DE5BFE070C2CA5751B99CAE734925683,
	CinemachineTargetGroup_GetWeightedBoundsForMember_m66E0FB67C1A1A678A206DDA7869E48533264A185,
	CinemachineTargetGroup_GetViewSpaceBoundingBox_m0DEE6D2517C2D3A0030E2EEEC44A71A08B0275E2,
	CinemachineTargetGroup_get_CachedCountIsValid_m954A5F1908D564DF36ECDB6A00285A6C30EB68BA,
	CinemachineTargetGroup_IndexIsValid_m1681AC168E9FE07272BCFEDD0C530597E98D56F3,
	CinemachineTargetGroup_WeightedMemberBoundsForValidMember_m733C92D75CDABB957D2F636E8AC7D928A4A0D63A,
	CinemachineTargetGroup_DoUpdate_m9DD03ECB2BB8D0D2FF159EE022D977BCE8F60D7E,
	CinemachineTargetGroup_UpdateMemberValidity_mB3DC6E7119136DEB47DF38BE9847D85A0945A173,
	CinemachineTargetGroup_CalculateAveragePosition_m53F12641C1D35197937BB9E57BD78B33A706B666,
	CinemachineTargetGroup_CalculateBoundingBox_mFA34E575D2243D685DE7594EC4C1559F872C6F32,
	CinemachineTargetGroup_CalculateBoundingSphere_m0BD49CCCE6992F00F263C15922CDEA35665E748D,
	CinemachineTargetGroup_CalculateAverageOrientation_m118DA64E33B831FF711D8A7CE25FC4E43AE02FA3,
	CinemachineTargetGroup_FixedUpdate_mF769342842AD70E39A9FC717F2D240FB0726E2A4,
	CinemachineTargetGroup_Update_m724F76214501E5240D28C641F8661B84F4FD556D,
	CinemachineTargetGroup_LateUpdate_m17DFCC2B7BB2F3EF24CB037253AB862A8C11D1E7,
	CinemachineTargetGroup_GetViewSpaceAngularBounds_m59037A85730646005B794AA40134C42CBA905421,
	CinemachineTargetGroup__ctor_m79E577054443EB393BF0D415CB21EB12EC87AF35,
	CinemachineVirtualCamera_get_State_mD9A4B9A276A896CE61CF4A3175C0C50AFAAFA281,
	CinemachineVirtualCamera_get_LookAt_mC71D170A52A0C7EBB8B512B41EEE9F67175440A8,
	CinemachineVirtualCamera_set_LookAt_mA999C37E7B4936DF8F7EDD13D48E568000B20212,
	CinemachineVirtualCamera_get_Follow_mE5A461072B1C46F33736F19CB47080FEB3A2008B,
	CinemachineVirtualCamera_set_Follow_m66CCC0DB5243B92AB976471EA1D2265E1D8B9011,
	CinemachineVirtualCamera_GetMaxDampTime_m059DBDCA5F7F954957F7FA5A86BAC04D81DB2079,
	CinemachineVirtualCamera_InternalUpdateCameraState_m2158DF36FDDDCC24EE79D9CF019BBAAAE5445597,
	CinemachineVirtualCamera_OnEnable_m62FDA7C6D4F84F0514F758F0B1EF40D9B84F8AF4,
	CinemachineVirtualCamera_OnDestroy_mAA93044AA45B8EE2242D23DABDBF77A727ACA0E6,
	CinemachineVirtualCamera_OnValidate_mD0D0029F130143E4B37AA96EA068A32FC5CB94C1,
	CinemachineVirtualCamera_OnTransformChildrenChanged_mD904BF65BA30DF6149DD53BE8F3AC2F64DD6DA43,
	CinemachineVirtualCamera_Reset_mDD3327DD7A2EEA32324A1EE66AD1395825FC4D11,
	CinemachineVirtualCamera_DestroyPipeline_mA8791466E5B9F0C01B2B79FC4587EBE54BFF408A,
	CinemachineVirtualCamera_CreatePipeline_m2D6EBA76ABACA143FD32240A9F2EFA3E61112021,
	CinemachineVirtualCamera_InvalidateComponentPipeline_mF6DF2D4F33B01A989AE3BD1CFAFC51BF35C97C4C,
	CinemachineVirtualCamera_GetComponentOwner_mB144AE753EBD703CCC5A868925DAC4A76B889DD7,
	CinemachineVirtualCamera_GetComponentPipeline_m500E721EB58184151E203BF7BB83104586BE7923,
	CinemachineVirtualCamera_GetCinemachineComponent_m26F31BE8B9E3891C9E889F884FCA94FF3B6A2C38,
	NULL,
	NULL,
	NULL,
	CinemachineVirtualCamera_UpdateComponentPipeline_mDC1BF9D6E71CA7CEFEB8539E13BAD7E12DAB11A1,
	CinemachineVirtualCamera_SetFlagsForHiddenChild_mE805AF7C54CF7D4517F230702B793D142F667C8B,
	CinemachineVirtualCamera_CalculateNewState_mEAC1B5563F4C5BD1868E591DBE70EC359C27AE31,
	CinemachineVirtualCamera_OnTargetObjectWarped_m562D2944CECA26CF64C38DC4A59CA72CB67E033C,
	CinemachineVirtualCamera_ForceCameraPosition_m24428AB9DFFF32E9A93DB9B9CCCE6E25B7637C1F,
	CinemachineVirtualCamera_SetStateRawPosition_m580BC430C8985CAD9750499F2E2597AA37F33623,
	CinemachineVirtualCamera_OnTransitionFromCamera_m8D77970ED45B1FF23674FB4090483AE87A5A62FC,
	CinemachineVirtualCamera_RequiresUserInput_mB1354A1D7D4C6D4F7914127C3BFE8E2AF20C8763,
	CinemachineVirtualCamera_OnBeforeSerialize_mBA1994115C29336108E879C25AD723F8D9CD90A4,
	CinemachineVirtualCamera__ctor_m3D68CEFD2C0746D59587AA63E8DD2E436CAA008E,
	CreatePipelineDelegate__ctor_m1418B88041BF669A6692C2B815A8913C01EA7895,
	CreatePipelineDelegate_Invoke_m64652CFF99A748B459CC4B834CE86FF147616188,
	CreatePipelineDelegate_BeginInvoke_mB61683EAF5CD48DD6AFE3080DBD1738BD0180656,
	CreatePipelineDelegate_EndInvoke_m5CAEF82C0E0E204CDFDEB4E6A99BAA098B0050B3,
	DestroyPipelineDelegate__ctor_m33BC3713FE7D6659FDF1BB0BAF060F70032EBF60,
	DestroyPipelineDelegate_Invoke_mE4428F322828BD410B9C74A0358DF87D3A1983F9,
	DestroyPipelineDelegate_BeginInvoke_m2902F0B20085FB754D94A373177CBE6D9E7E42E5,
	DestroyPipelineDelegate_EndInvoke_mBCC8462D17B7FDB6058C446202AAEBBDB9515D46,
	U3CU3Ec__cctor_m1637EA016B94B271146109003881DB288843D058,
	U3CU3Ec__ctor_m0BD6B99048AA4888057E840317CE80F3789BBE8D,
	U3CU3Ec_U3CUpdateComponentPipelineU3Eb__37_0_mA4D694C9F9805040B7715B6A1FDD6B1A2D17F499,
	U3CU3Ec_U3CRequiresUserInputU3Eb__46_0_mC341C64060ED18E061411546F35522CB18FA77A6,
	Cinemachine3rdPersonFollow_OnValidate_m34565B92F078C01A788E839FD887B50F4043CE84,
	Cinemachine3rdPersonFollow_Reset_m61AD95A9B447BCC760EDB5F6AF6EE9AFD23F065B,
	Cinemachine3rdPersonFollow_OnDestroy_mE3EBAEDC7F1108559BFB0207EAE6E3605DEAAEAF,
	Cinemachine3rdPersonFollow_get_IsValid_m3DA263484276CC7C240C2C3170966CB74597861B,
	Cinemachine3rdPersonFollow_get_Stage_m8932622C583CBBDE9A4CF4614D622F76E0880CFC,
	Cinemachine3rdPersonFollow_GetMaxDampTime_mE928D264574DE70999AB305FA793D028936D6BC2,
	Cinemachine3rdPersonFollow_MutateCameraState_mEE2F997F216076C29851BA224DD63B0CC3F47C42,
	Cinemachine3rdPersonFollow_OnTargetObjectWarped_mD0303D4A6D9EA4D13150C8C5E93AC843F04B0919,
	Cinemachine3rdPersonFollow_PositionCamera_m13334AE8E5681B0F83EB4DC65607CCDEBBE7BC4A,
	Cinemachine3rdPersonFollow_GetRigPositions_m030DC36FC5FC04F030AAE5DD1DDE3C586F73C534,
	Cinemachine3rdPersonFollow_GetHeading_m4D3086385158A48B3E83FD7960931CF2EFD52535,
	Cinemachine3rdPersonFollow_GetRawRigPositions_mDE2296B2034978F905A1C9CBAA202EFB174CB1D5,
	Cinemachine3rdPersonFollow_ResolveCollisions_m0803F98237E6C6D08D13173E1FECBDD506860BA4,
	Cinemachine3rdPersonFollow__ctor_m239FDBE524C3BED33399C39822BBB7A63EFE95E9,
	CinemachineBasicMultiChannelPerlin_get_IsValid_m8330E1B15909306345A589EFD1BA1A9AA223E0F3,
	CinemachineBasicMultiChannelPerlin_get_Stage_mF7751E50B715EDFACEE2CE2A56999B0B3F40F024,
	CinemachineBasicMultiChannelPerlin_MutateCameraState_mD65925FD4858E9650DB79BFB8CBD75D0E2224CC4,
	CinemachineBasicMultiChannelPerlin_ReSeed_m386B1BA6AEFB26B878A7431484D412F9FB2E9696,
	CinemachineBasicMultiChannelPerlin_Initialize_m1ADAFB3D2CAFBEBC0018D71B44BDCD24074EAEC2,
	CinemachineBasicMultiChannelPerlin__ctor_mEE8548285D383571E5AC2DB0740D8FD906BF8A50,
	CinemachineComposer_get_IsValid_mF1833F36F4B8823131C599CADEB5EE0A3CFCF062,
	CinemachineComposer_get_Stage_mF5E0C634D954BFFAB5A2EBFB4B4DA326D6853A6F,
	CinemachineComposer_get_TrackedPoint_m164861743F7BD7E49747B46076F228CBD8785F33,
	CinemachineComposer_set_TrackedPoint_mC2806265609C1BADBE1F83DD18F800BDA064D5A6,
	CinemachineComposer_GetLookAtPointAndSetTrackedPoint_m5810F9F4FEC4860FE749CB7260E78E8BEE41E671,
	CinemachineComposer_OnTargetObjectWarped_m14DFF01ED1173B5902E80C9A55AD2C1998481789,
	CinemachineComposer_ForceCameraPosition_m190442A4F145C4B298B785DAE08EC8358B924B70,
	CinemachineComposer_GetMaxDampTime_m1D830B2C6BDB743F6C546C27AA62A60704BC4CA0,
	CinemachineComposer_PrePipelineMutateCameraState_m6A2121831D76E0CD191FC63A7C63167AB917190B,
	CinemachineComposer_MutateCameraState_m50DD037C33A1BF4956C47F8ADA6F6CBADDDA4B3A,
	CinemachineComposer_get_SoftGuideRect_mFFE86E73B085263B4B15F2E5BD8053F8C033E8E1,
	CinemachineComposer_set_SoftGuideRect_mF24C9DED070606ED93AC69CC0F2AB72BB55A1ADA,
	CinemachineComposer_get_HardGuideRect_mA2B70FA82432B7D2874E5213E3F9086CC152E69F,
	CinemachineComposer_set_HardGuideRect_m868567C4C94ED2BE86B092E4F69C548F98B167A5,
	CinemachineComposer_RotateToScreenBounds_m01D1A38D82DF6AE50EFF13027781D15DED32D7EF,
	CinemachineComposer_ClampVerticalBounds_m65C191E116F577A8F7F1383C99875779254B934C,
	CinemachineComposer__ctor_m90D1EE7F962886981F03D129849E4214A106DCD8,
	FovCache_UpdateCache_m3462592E7672B43BEB32686E0F62B7C17F0E2999,
	FovCache_ScreenToFOV_m84AEDE8D18A7CE6A911AB93E622316E126980056,
	CinemachineFramingTransposer_get_SoftGuideRect_mCDC60214B6A81FBD8AAF9F6DECAEC86A562C504A,
	CinemachineFramingTransposer_set_SoftGuideRect_mEEE1DEC1C703C7C8D54A3C8388EB659E32B30F23,
	CinemachineFramingTransposer_get_HardGuideRect_m83469B076C3529941A2FD36E35FFE410EA3D7BA5,
	CinemachineFramingTransposer_set_HardGuideRect_m215B19AF350146BA8E7C394D75EAD67C46BEF10E,
	CinemachineFramingTransposer_OnValidate_m28F166F10297E84E587FC092E2E5DAB42A821AF8,
	CinemachineFramingTransposer_get_IsValid_mDE0B8E801C5BDDA9643075A935B8FF10151C11CE,
	CinemachineFramingTransposer_get_Stage_m406D870FC51C1E3D0F463CD3F8124D4C13A78302,
	CinemachineFramingTransposer_get_BodyAppliesAfterAim_m29E5668CF169FFABBB9CEEB03E9D733EAE1C693B,
	CinemachineFramingTransposer_get_TrackedPoint_m893C86296D7D0C01FCD28D85D14B38124F9AFB52,
	CinemachineFramingTransposer_set_TrackedPoint_m32FD1D5F85F4BDBFC3BF6DBF5CBC7A8D1DB44FDD,
	CinemachineFramingTransposer_OnTargetObjectWarped_mAD4EE7D5CD54543EF73BF8D8DCD1781A57A6CCB4,
	CinemachineFramingTransposer_ForceCameraPosition_m7D0A6F764D394716B8F6700367A8F6DA53076546,
	CinemachineFramingTransposer_GetMaxDampTime_m581B0A2F6493CCF2C3F1B0E68E8F0180EEB51B85,
	CinemachineFramingTransposer_OnTransitionFromCamera_m88B1E40E14D5612AE78AEFF401BC71442DF761AE,
	CinemachineFramingTransposer_ScreenToOrtho_m07AF0DD2BFAEF10102EFEDBB9D87F31EAFA35D41,
	CinemachineFramingTransposer_OrthoOffsetToScreenBounds_mB27FBC07BF36E7BBACD39AAE05C8D7D3B62A8A4E,
	CinemachineFramingTransposer_get_LastBounds_m6D98D46A49E2196A98E2B7E76C0061AC8310B45B,
	CinemachineFramingTransposer_set_LastBounds_m42F030170155BAC06C2B040E44F4FCB25251EF93,
	CinemachineFramingTransposer_get_LastBoundsMatrix_mB1296133E5C0BDD6B9C0879888C468C559BE95BB,
	CinemachineFramingTransposer_set_LastBoundsMatrix_m13FAE68552F3910750A134D22AE4AF6845C0301D,
	CinemachineFramingTransposer_MutateCameraState_mCF6C11F8E364980D95EFFEDCE1BDC11FD1877734,
	CinemachineFramingTransposer_GetTargetHeight_m5CD0304B16E7442B6BA592E7915FE7C2F57D4A64,
	CinemachineFramingTransposer_ComputeGroupBounds_mD7044C4EFA049F1BD91607D7EB5FE2F26E7A78D2,
	CinemachineFramingTransposer_GetScreenSpaceGroupBoundingBox_mFA7FD0E686444062F3A538809B553D27B1C292AF,
	CinemachineFramingTransposer__ctor_mDC6AE4489F2CBA1B667DDE193E2C1D1C3D3332D5,
	CinemachineGroupComposer_OnValidate_m4F578A19AB48C00C385A8AB096DFD5E8C8991D77,
	CinemachineGroupComposer_get_LastBounds_mC2ABA5C693EB4C5AC2676461601D5F9DC5615623,
	CinemachineGroupComposer_set_LastBounds_mE2FCF71321530F97627893A8BA652B959D19110C,
	CinemachineGroupComposer_get_LastBoundsMatrix_m67F9243F621C6474E2090615DDE98B6E69B81E52,
	CinemachineGroupComposer_set_LastBoundsMatrix_m917FDDE19382BCDA1626CF4BB5E118E43C1D13A3,
	CinemachineGroupComposer_GetMaxDampTime_mED0FCE86105021DEFD27DC6546387EE1AEBEAFA0,
	CinemachineGroupComposer_MutateCameraState_mBA96192C982AF7399B01AD3FCE14D48F6C27373A,
	CinemachineGroupComposer_GetTargetHeight_mE81E9435860ADF221E7DD164A4ADF411AB4C740A,
	CinemachineGroupComposer_GetScreenSpaceGroupBoundingBox_m23D17E79BBF0D8E56FFD0269BC4C5C8F33BD251A,
	CinemachineGroupComposer__ctor_m971E14E2A389C00A5DB8E27648BC6143D96CDFAC,
	CinemachineHardLockToTarget_get_IsValid_m3283683207CBE04A66BDE3CC3731D04AD4E11D7F,
	CinemachineHardLockToTarget_get_Stage_m67CC2097CE1F227F1A4080108D68E8C9D6E21896,
	CinemachineHardLockToTarget_GetMaxDampTime_mCC6C1B1C21332DD63B0CC7F435280080B0B76B70,
	CinemachineHardLockToTarget_MutateCameraState_m511263D61277FC8FF50CEE06B367F0B75CCA8D52,
	CinemachineHardLockToTarget__ctor_m905CAEB127D9D192FEAAA8F014F2C096F450F4C8,
	CinemachineHardLookAt_get_IsValid_m2281D65323EDD1BFA20E167912C262447A29D901,
	CinemachineHardLookAt_get_Stage_mC4BD7DB16560529621EEAEFFA65006A030832907,
	CinemachineHardLookAt_MutateCameraState_mE2A0FF4E4AE1F96D12E53368316B7F14ACD71E2B,
	CinemachineHardLookAt__ctor_m673A99093725D7083A4C47B1C2328BB05647B70B,
	CinemachineOrbitalTransposer_OnValidate_m3782FFE204D5E142643EC56897EE4EDD4CD91100,
	CinemachineOrbitalTransposer_UpdateHeading_m237761CC9CA559C83FA849BA7FB15661911A953A,
	CinemachineOrbitalTransposer_UpdateHeading_m8718BA600DA5134C0E38C8646DBC2506AB4472AB,
	CinemachineOrbitalTransposer_OnEnable_m6C7E95C1EAE2BACB03E324BBE303DBFFE14CDAF6,
	CinemachineOrbitalTransposer_UpdateInputAxisProvider_m2FA2059A198A20A0730E6BCAC2D572005513971D,
	CinemachineOrbitalTransposer_OnTargetObjectWarped_mE2BFEBB6D56EB26F27F01CBF307D1EBF4B060B5E,
	CinemachineOrbitalTransposer_ForceCameraPosition_m58355A8C31130A765A8D0B8E03CFFAC74A375195,
	CinemachineOrbitalTransposer_OnTransitionFromCamera_m20B42EEE01538F55F944E042459D5FC87B6CC204,
	CinemachineOrbitalTransposer_GetAxisClosestValue_m12E53A2B675F5EF62F5FC89AD55A3F398C005AFF,
	CinemachineOrbitalTransposer_MutateCameraState_m1AB5EA636D64DC31FBC22AA18878307B645514C1,
	CinemachineOrbitalTransposer_GetTargetCameraPosition_m67992ACDFA01B5C8150D7AC9488086FABF473652,
	CinemachineOrbitalTransposer_get_RequiresUserInput_m4B493CC95DFD622F1389A7C11ABD68041B216448,
	CinemachineOrbitalTransposer_GetTargetHeading_m7CDCBC39F6AF29C82492EC52B529A3936CFD6219,
	CinemachineOrbitalTransposer__ctor_m8BD1ED063A460BEF9B0A489B63769DA9CD1511FC,
	Heading__ctor_m8BA2E53862E9957B1942EF8A55E5C8284ACDAAAB,
	UpdateHeadingDelegate__ctor_m60911D320DFD3CDA2C31C8CC7E030A3B47EFF3F6,
	UpdateHeadingDelegate_Invoke_mD63AFD811D3492ECF335D17B0B858E3655D8019A,
	UpdateHeadingDelegate_BeginInvoke_mF9371D7AA17A9372F2FAB2891F8E66CA67FE5AAE,
	UpdateHeadingDelegate_EndInvoke_mCF8E24E08925233FAA0FB6E5AFAFEFCF67FBE8CF,
	U3CU3Ec__cctor_mDB41F389E9ACDC6A49924D0DDF6BF908E627676B,
	U3CU3Ec__ctor_m86741AB1B49B0E3932CA01086C2B7FAFC221C361,
	U3CU3Ec_U3C_ctorU3Eb__30_0_m9216ED998310150D666FF45C1BD6868BF4BF02DD,
	CinemachinePOV_get_IsValid_m05C868F4435523397654A39A1BF8593CF0F59ECF,
	CinemachinePOV_get_Stage_mFE8B3BB72F863545A8347D3CA587EE97D9A9EA5D,
	CinemachinePOV_OnValidate_m016AFFEFBEFECF40D1507E5AF33A6C0E1013D228,
	CinemachinePOV_OnEnable_m3A517E2080784B6C7A31A3227796D3B994FF647B,
	CinemachinePOV_UpdateInputAxisProvider_m061C1326E834985C26CA2D74F90D2E52C590FC4D,
	CinemachinePOV_PrePipelineMutateCameraState_mBA43F716320C330EE8502DC1F49CD30512D8DF0B,
	CinemachinePOV_MutateCameraState_m7D3F0F0979A4D487630A47A0BDB8B6C01F58A4EE,
	CinemachinePOV_GetRecenterTarget_m222F334C80D4ABBD48B9284A6EFCF6C0B853460A,
	CinemachinePOV_NormalizeAngle_m5A686B3609FF6019E1B4BBC07F1A374FD1B17B4A,
	CinemachinePOV_ForceCameraPosition_m454958C55A58DD989A25D0443138AADBF608BB52,
	CinemachinePOV_OnTransitionFromCamera_m491BDC05FF82D94CD9F0F5E381FABD26B836D32F,
	CinemachinePOV_get_RequiresUserInput_mF3866C5A3BF1A75C3EBF06998987999FC37A558B,
	CinemachinePOV_SetAxesForRotation_mDBC52583D2371432C6CE2DFE61689D7C906710BC,
	CinemachinePOV__ctor_m362B77E97F02F0B022F654161A5FA5120BD0DD17,
	CinemachineSameAsFollowTarget_get_IsValid_mC6D1503DFD8DC214605C36C1CAE502935D15BFEA,
	CinemachineSameAsFollowTarget_get_Stage_mC807B568193EE1879B9A384DD9867E7FB1FFEA48,
	CinemachineSameAsFollowTarget_GetMaxDampTime_m043AF23A9A983ECE05922C0472DB7FC1BF8542FB,
	CinemachineSameAsFollowTarget_MutateCameraState_mE752F9E346D7A3380316A3446D5210988467B4F0,
	CinemachineSameAsFollowTarget__ctor_m4CB9434EC6F54E7AB3287343A9C5416C8079BD09,
	CinemachineTrackedDolly_get_IsValid_m280F6EE398F406E248920DAEC9A2FB4C0A78CD20,
	CinemachineTrackedDolly_get_Stage_m71C0F0AEBCCACD06836E5186C06F8E157DEB44D4,
	CinemachineTrackedDolly_GetMaxDampTime_m8387A78C47A4689A44BC60168DAD135BF2F14E20,
	CinemachineTrackedDolly_MutateCameraState_m520EE451BF883F43059C628A4FEED072C6ACFECE,
	CinemachineTrackedDolly_GetCameraOrientationAtPathPoint_m8F4DB6F44E986BE7FC8C2C55FCC1556995DB4D54,
	CinemachineTrackedDolly_get_AngularDamping_m5ED59BCFD88587E5AF232BB5D779B3FE03832DE9,
	CinemachineTrackedDolly__ctor_m632C7211074603AA91B9313A426A224C1E9490ED,
	AutoDolly__ctor_m8DEA29EE4AE5C67F12B07FB0C51EEC0810FDDF20,
	CinemachineTransposer_OnValidate_mFC57EE74F157499D7CAC4D30CC1D7A04ED6FC33E,
	CinemachineTransposer_get_HideOffsetInInspector_mD7DBED85FE7830CDCD7BD3782022D88EC77F7774,
	CinemachineTransposer_set_HideOffsetInInspector_m9D1049D2BCA245506F7768F1D1CDF53548FE528F,
	CinemachineTransposer_get_EffectiveOffset_mF79BE447AD9A91A1829011B346B5AF18F6E1CE25,
	CinemachineTransposer_get_IsValid_m700545C70F86F2083F9FD2C1E97DC68FB8FC98C1,
	CinemachineTransposer_get_Stage_mAD7ABE84591669BA748174CDB9880821BB0A132C,
	CinemachineTransposer_GetMaxDampTime_m91977B2D8B63655ABA75BE4E9EFE6C68A0A5A094,
	CinemachineTransposer_MutateCameraState_m5B36F2ACE48727E2893C57FFEAD3162A6ECCAF65,
	CinemachineTransposer_OnTargetObjectWarped_m9E0D9DA06D752FF81CB08EDE999759FF47DEF741,
	CinemachineTransposer_ForceCameraPosition_m8E10E86DEDAF9FE53266FDB72F53E6D2083965B4,
	CinemachineTransposer_InitPrevFrameStateInfo_m5640D1D85D4260B279D374618B009740EF6EC260,
	CinemachineTransposer_TrackTarget_m509CF4F1D4319A21D55CEAA20802DA09B46E2AC5,
	CinemachineTransposer_GetOffsetForMinimumTargetDistance_m3AF6061743759E9C4BF3280862AA8841449A3172,
	CinemachineTransposer_get_Damping_m0BD9EBB7534A2DB4AB31AEB2BBAC3DF1D01BF366,
	CinemachineTransposer_get_AngularDamping_m489A52D7C6AFD2B34710F4E97299EC2A18E5CDBE,
	CinemachineTransposer_GetTargetCameraPosition_m504AE0BA123B7A208257661232FF2A40AB408B92,
	CinemachineTransposer_GetReferenceOrientation_m3CBF0CBBB1639E68901C407E2A6A739D079915AE,
	CinemachineTransposer__ctor_m66F1121D2339FDEDC9743EC432749AFB3CA846BC,
	AxisState__ctor_m09348C6ABBA887484BF7D3961D4FB582C0E5A4F6,
	AxisState_Validate_m1245D61F6D9A031C27F75F4B49E78A52AA91BDE5,
	AxisState_Reset_m329065EBC9963460CD7733144EC5F47D107967C9,
	AxisState_SetInputAxisProvider_m9FBC0D9C885EDF31C4FFDA8A70029C5FC9089C85,
	AxisState_get_HasInputProvider_mD82DACE6E188BCFE1B0B5FCB1328BF8FA738B091,
	AxisState_Update_mE86F039B78105160E5C13153B456E3A988AF28B4,
	AxisState_ClampValue_m2985D75E8FF57E3F88BF31B24CC719511507837F,
	AxisState_MaxSpeedUpdate_m59BC1A91869A0D4A07E53DA4ED4172D5FBBF1DBD,
	AxisState_GetMaxSpeed_m323DC3125D2C40B79B0C041CBE7F5F126329E489,
	AxisState_get_ValueRangeLocked_m25A67A9600BCC5AFD35CA1A2C57AE0CFCB76E6B1,
	AxisState_set_ValueRangeLocked_m367AD65F7E97A0DFF0DE1CA0C74AEEBCCC36D000,
	AxisState_get_HasRecentering_m24F7A4CEF751588924C04AAB32BD1B59389BA4DC,
	AxisState_set_HasRecentering_m978B18A62A74813CC75078114997E708B6877D85,
	NULL,
	Recentering__ctor_mD885C396DC27C43D79A1FAA42F5ADD7D05CF2476,
	Recentering_Validate_m3F5EE15AE52BB8FF2B69E3963851CEE2600340D3,
	Recentering_CopyStateFrom_m1DB1F919E2F17C4913D1F2605E71630004138D89,
	Recentering_CancelRecentering_mB79FB4BE6A929EA524224E11C885AFBA1C212D90,
	Recentering_RecenterNow_m0A012C8E8ABA1B3D00765C8C0FDC3A96C3DB102C,
	Recentering_DoRecentering_m7B1730622484A958AF9FD87F2056A388D96EA01A,
	Recentering_LegacyUpgrade_m17A3ED97851377053B2385331ED85BE3DA3D4D7D,
	CameraState_get_HasLookAt_m2581CDE02E0998E65DF1AA58B170AAB84CBFD0AC,
	CameraState_get_CorrectedPosition_m2F96F0F6D3AE57BCEDE566FCE49D1488CA057089,
	CameraState_get_CorrectedOrientation_m04987B71E708B14A28973FFF81645C8834FD04E8,
	CameraState_get_FinalPosition_m4D482D1F3E008068C2151FC24FD85CB6F603AE12,
	CameraState_get_FinalOrientation_m65D23E9A3C9264408AB177483C74FD609EFAB4B3,
	CameraState_get_Default_m21CC49BBB9A1FF0D582E3CEEC9C1F63C3F068DF8,
	CameraState_get_NumCustomBlendables_mA7FC428A3F135FA88769EC45E2C5521F2D1169DB,
	CameraState_set_NumCustomBlendables_m599C74DAA99E17F8B5EF87CFD0A6238A81D05AD3,
	CameraState_GetCustomBlendable_mE19B33F6CEC1B42ACAEB34A0601E48A80577498E,
	CameraState_FindCustomBlendable_m141410A5E7FF4B985E2D3979D72BF80F398DE57C,
	CameraState_AddCustomBlendable_m1DA24CB5A397752C33B6A1773CFF38F02505AD3C,
	CameraState_Lerp_m0BAAA9D6CC903E2656ACE05FDE2C930FE24D3E98,
	CameraState_InterpolateFOV_mB963D73A017B25550381245B5976BBE7DE77502F,
	CameraState_ApplyPosBlendHint_m61F39F5911D34F49768E601B72C17CDBFFB26D1C,
	CameraState_ApplyRotBlendHint_m25B6966D775F36A71929C4EA0404328034EBA09E,
	CameraState_InterpolatePosition_m0754A646434C49674356B584F9BDBB67B0D4F707,
	CameraState__cctor_m53E682EB8F41BE2DA635516A8A6DCEF8EEF8DA0F,
	CustomBlendable__ctor_mF38BF574AF05E415A01A2A46E506DE6B5086B303,
	CinemachineBlend_get_BlendWeight_m0FFFD553C4A1176490E443AF34DC8AB87F0763A7,
	CinemachineBlend_get_IsValid_m3C10BCF867EF0AA96AAF0A70FF0990808FB7C81C,
	CinemachineBlend_get_IsComplete_m927128CEC49DCADF02A6258F8D636B0957446686,
	CinemachineBlend_get_Description_mC4378A79CCE5E2FF0FA5A175B6AB3DF7E6A6374C,
	CinemachineBlend_Uses_m7EC8B1160B3D24C5609684B486D485B2DD806A26,
	CinemachineBlend__ctor_m36DEF2F2190A7392298D71CDC78C6A032FC8FC1D,
	CinemachineBlend_UpdateCameraState_m07AC58D1D550924255FC4B13BF6BBDC903B44493,
	CinemachineBlend_get_State_m6667F2BD63E27F3A1FD5130CD23FA9CA11BA5DDC,
	CinemachineBlendDefinition_get_BlendTime_m05485E3F00A40AD789C1A1C457153C8EEF13EF45,
	CinemachineBlendDefinition__ctor_m24EFAC96EEDA53F43590F285C0B637771E6C947D,
	CinemachineBlendDefinition_CreateStandardCurves_mC0C71CFA64286A2ED1DC190CFC2C9FAE37E9E2CF,
	CinemachineBlendDefinition_get_BlendCurve_mC33A778E56621A57C341B1882DE76D85CEBF82C0,
	StaticPointVirtualCamera__ctor_m1F7B42796DD737014056877CA31EB5A1218A4112,
	StaticPointVirtualCamera_SetState_mDCA6D489E0E6B7811CF02DDB0B0E27C22BED4207,
	StaticPointVirtualCamera_get_Name_m747211FC8B4092AE0A9B06A94BF68E2427A15810,
	StaticPointVirtualCamera_set_Name_m3894662EFE90B15664D4935B05CA90BF4EC5D530,
	StaticPointVirtualCamera_get_Description_m904B81566906FBE32DA4542531EEC6F7E1FD9FEA,
	StaticPointVirtualCamera_get_Priority_mB670BDA879230CC102785CD19F621B796BB449A9,
	StaticPointVirtualCamera_set_Priority_m5EE3522D9AF59085624CD6D3530064088D4C9848,
	StaticPointVirtualCamera_get_LookAt_m30E0403A476E774BDB4B19F78BFE56D95811307C,
	StaticPointVirtualCamera_set_LookAt_m10EA988D83BCF4732C4FFE0FB4591A32827FD3EB,
	StaticPointVirtualCamera_get_Follow_m20B11900677A6D382CDF3F43AC9BCCEDB5DF9914,
	StaticPointVirtualCamera_set_Follow_m63241367CE4DCB42050FBB0399C9BBBEEC40509D,
	StaticPointVirtualCamera_get_State_m8E219127A5A4308451758CACE2920C0641729419,
	StaticPointVirtualCamera_set_State_mF717B76100CB091E5B0B5A182D8AED92043F5B1E,
	StaticPointVirtualCamera_get_VirtualCameraGameObject_mCE0937D2A4C7F11ABEEEAFAF3E61BDC4B7D94726,
	StaticPointVirtualCamera_get_IsValid_mDCEB5041AF5D5AFE66C6DAC9CC660494EEFC468F,
	StaticPointVirtualCamera_get_ParentCamera_m96007C431649F6BC9C26EB8963F3888A567F182F,
	StaticPointVirtualCamera_IsLiveChild_mE43BF024220758FDAB88C4C479E4BC0B40AA971D,
	StaticPointVirtualCamera_UpdateCameraState_m63CB470789BF6A60C8764B6B1E11895D22E60D94,
	StaticPointVirtualCamera_InternalUpdateCameraState_m54647D9C3B4FDA845188A9B53B059CF9415557C7,
	StaticPointVirtualCamera_OnTransitionFromCamera_mA48D18E6527B557E81D69175371AC3A37915B02C,
	StaticPointVirtualCamera_OnTargetObjectWarped_m6E8C515C93A349DE5598C2FE5D06B26E335A1DF7,
	BlendSourceVirtualCamera__ctor_m260866EC0863C4B16331D7539AA3852E90DEEC5C,
	BlendSourceVirtualCamera_get_Blend_mAEA739F5A13237AF89E38325902ECA8316FC5719,
	BlendSourceVirtualCamera_set_Blend_m08A91575E7F63681916FD0FDB9093DE271A71AFA,
	BlendSourceVirtualCamera_get_Name_m0CCAC6597DBED7F0C14E55AE6BD3AE1C0FFC4EBA,
	BlendSourceVirtualCamera_get_Description_m66034400A8D85B99EC18607B71A094C0242E072A,
	BlendSourceVirtualCamera_get_Priority_m965C7A7F7D4DB371F4008904E49F66AF3BDE8636,
	BlendSourceVirtualCamera_set_Priority_m5D5784B3554D81A89AB942149CBD0A6DA8141A9A,
	BlendSourceVirtualCamera_get_LookAt_m00F5A7F9583090E8C08E44297E4DCDA38D10FC84,
	BlendSourceVirtualCamera_set_LookAt_m8FAD0D9F6A9EBB41AD2DB77A298EFFB12D7DDF79,
	BlendSourceVirtualCamera_get_Follow_m0C785D15EAC52228AEC14483B5AEA51A34CE9ED2,
	BlendSourceVirtualCamera_set_Follow_mEA0CDA042703FCCDF0B8D1EBFB44850417EED799,
	BlendSourceVirtualCamera_get_State_m4317D28A3F0EB829B1A008C0BD453A0E25C41547,
	BlendSourceVirtualCamera_set_State_mD9C8228DBCC8AFB62BD349ADF5FE0CF4449AC52E,
	BlendSourceVirtualCamera_get_VirtualCameraGameObject_mF259156E6AB334749619FB1C2201DCD2169277C3,
	BlendSourceVirtualCamera_get_IsValid_m7ABB6F9CC7D4BBF3605B4585E2B0F0B156F0401D,
	BlendSourceVirtualCamera_get_ParentCamera_mEEBA9CBE7E371DFCF07F1E3CE4B3A4113E4E894A,
	BlendSourceVirtualCamera_IsLiveChild_mEC6D0D9A7823969F0045B77FA84B6E04F798B952,
	BlendSourceVirtualCamera_CalculateNewState_mA5A2FB2600449ADD557442807A24B098D7D1C249,
	BlendSourceVirtualCamera_UpdateCameraState_mD67B44073F7EA49860018B880F79EF829180BFCC,
	BlendSourceVirtualCamera_InternalUpdateCameraState_mEDE2337FEDCC7987D211F3738B491402F3BC3873,
	BlendSourceVirtualCamera_OnTransitionFromCamera_m750F9688DA0DFBF1640E3F90D367D56490166523,
	BlendSourceVirtualCamera_OnTargetObjectWarped_m3107B7DAB47DE606823928F9A57DD9F13CE2A766,
	CinemachineBlenderSettings_GetBlendForVirtualCameras_mD1C44B6D389EA35A1AC08F3BA4DEDC1D185070D5,
	CinemachineBlenderSettings__ctor_mCD4EFC8540B918D3884B62BD5AB63AAF86433F7A,
	CinemachineComponentBase_get_VirtualCamera_mB83A44E630B22D8CD9A75521079ABC1691120223,
	CinemachineComponentBase_get_FollowTarget_m656475012F330FF1C680CD7E62C81D2E7EC4AB74,
	CinemachineComponentBase_get_LookAtTarget_m7E6CF239A3905B1130A5C38B0E5668EB32D1BB04,
	CinemachineComponentBase_get_AbstractFollowTargetGroup_m91BD623311234A96B2D146A8AB6574567C8C9714,
	CinemachineComponentBase_get_FollowTargetGroup_mE756D61F3DC852F90E1292620499B5367F810A31,
	CinemachineComponentBase_get_FollowTargetPosition_m1039B11144B61D09459CACDA7A7E38626A601CC2,
	CinemachineComponentBase_get_FollowTargetRotation_m9C7A5F1A91CCBC93B69F934060F9D4C08FA547F3,
	CinemachineComponentBase_get_AbstractLookAtTargetGroup_m83547AD312D71E3080F9C6948DF4C5DA7B6B6054,
	CinemachineComponentBase_get_LookAtTargetGroup_mC3A9F692727989A9E76109FB2C250E934A641BEC,
	CinemachineComponentBase_get_LookAtTargetPosition_m79CE45A7F4D4A82BC47B01434F5EB35C91DC99A8,
	CinemachineComponentBase_get_LookAtTargetRotation_m49CBE00226BB55772DB73775412AF782892B8251,
	CinemachineComponentBase_get_VcamState_m17C5F4CFD04B41EA7559216C8C50CB980140D9A2,
	NULL,
	CinemachineComponentBase_PrePipelineMutateCameraState_m05A1AC832D6F7FD16DE67103E8CB0535950BB79C,
	NULL,
	CinemachineComponentBase_get_BodyAppliesAfterAim_mB9687B7FEFB9827154DE70F8139BB52D84B2B0D2,
	NULL,
	CinemachineComponentBase_OnTransitionFromCamera_m6FCB5461D89C12185703C5701848413DFB99CD56,
	CinemachineComponentBase_OnTargetObjectWarped_m3E083DBF03C47860948F0BB3A013B241AFDAF9A0,
	CinemachineComponentBase_ForceCameraPosition_m3D22002EC0B4F5C1AF7CC283C00BA43D22120878,
	CinemachineComponentBase_GetMaxDampTime_mA2159FA6C923F49F9729286A70C304298440B060,
	CinemachineComponentBase_get_RequiresUserInput_m45F5AFF5AB81E3E6562E2A5F624768A73E6A7170,
	CinemachineComponentBase__ctor_mFA2A3C88B75CD71B7F359220C38B253AC1353B19,
	CinemachineCore_get_Instance_m437A8089CC851778BA1ABABA3041B24B8D8B7E9B,
	CinemachineCore_get_DeltaTime_m0ED50D97C4B5327468BFA2A426E4F8B2F4078F48,
	CinemachineCore_get_CurrentTime_mFA05B621BE52910A8C3A304CB715257011C9A782,
	CinemachineCore_get_BrainCount_m464F67F700D9EF3D2F486C14C215578AFC8BE080,
	CinemachineCore_GetActiveBrain_m0CA11E913913E3A09CFE0C93C612C98936D480D8,
	CinemachineCore_AddActiveBrain_m48DD0D8000E2EE00E9C4BF642F5CDF04EF5AB819,
	CinemachineCore_RemoveActiveBrain_m20CECA6E425E777D991B9AF73A691D05B412EF3E,
	CinemachineCore_get_VirtualCameraCount_m991909A066AD9BEE7B18512F6F2CB6B36F2ED86A,
	CinemachineCore_GetVirtualCamera_m4D2512A0C993348EFC8A76729802C8BBF6209F0B,
	CinemachineCore_AddActiveCamera_m80475EB1F23E16DD2CF235BD9076715402BAD5DD,
	CinemachineCore_RemoveActiveCamera_m879CC4BCC026A83C761ACDDA9604766086B53361,
	CinemachineCore_CameraDestroyed_m15FC49C579C1933A8D3CD481B23ED229AABB25E5,
	CinemachineCore_CameraEnabled_mF0183BD36CDF19EB604E6C966F78030D53A08DDB,
	CinemachineCore_CameraDisabled_mFFD409EFB372B7C90824BDE0C576EFF56F61285F,
	CinemachineCore_UpdateAllActiveVirtualCameras_m89205DECAC84E31CFF4420DDB36BDFC70BE37B53,
	CinemachineCore_UpdateVirtualCamera_m8AD0E1834C19A941F246C0BD9661ED575CAA84B1,
	CinemachineCore_InitializeModule_m3AF5A969E84D95E78CD79107015F033FF4EB364F,
	CinemachineCore_GetUpdateTarget_m3007FAAA1292808633CDFF235EADB297B8506282,
	CinemachineCore_GetVcamUpdateStatus_m6864417D5726747911A8FD36CFE4BF7BC2551ADC,
	CinemachineCore_IsLive_m6F2EBE598087857FF7D04A078563E9972CA52678,
	CinemachineCore_IsLiveInBlend_mFD1402FFF3B5D0CD0EC90914F89672724F49F778,
	CinemachineCore_GenerateCameraActivationEvent_mD2A009E3CE9D80962BA7E902A9BD3B22E161C021,
	CinemachineCore_GenerateCameraCutEvent_mED951E792CC6811DFF12F79B389C3237094FACD6,
	CinemachineCore_FindPotentialTargetBrain_mD4D554DCF27AE5BD2BAEB9D09E37BFBDD9D79B3B,
	CinemachineCore_OnTargetObjectWarped_m39BF4DE7271E080415FDB67E1C32C8E0C94702E1,
	CinemachineCore__ctor_m2F77D083353903E3CC95FC46D88EFE627D6FC2D4,
	CinemachineCore__cctor_m206AFF5C9C6BE921CCFBB41D19902D33EF70EC37,
	AxisInputDelegate__ctor_m8D7FED117FA4DDE1BBDC08B5158E73E2FACFF7B6,
	AxisInputDelegate_Invoke_m1C36C70E105C8A9091AED921BB6E7053C99F39CE,
	AxisInputDelegate_BeginInvoke_m7A4D0E95E70B542E40AF139F3FB6986F2973F30C,
	AxisInputDelegate_EndInvoke_m03AB17EB531ED61AFA1262B48356151A0D162F24,
	GetBlendOverrideDelegate__ctor_mE8D07530859FC280F4A642B0B05322C3568EB0C9,
	GetBlendOverrideDelegate_Invoke_m95589B7FBA20E761B45993D16D9D930F84E39D84,
	GetBlendOverrideDelegate_BeginInvoke_mC0BD5CA7322A5B456D5955B571EB11D5D6485603,
	GetBlendOverrideDelegate_EndInvoke_m299DDA873DCE525E30CCB9621E6EB9BF9F897061,
	UpdateStatus__ctor_mD7EB6573750445A83891060458B7305AB1899583,
	U3CU3Ec__cctor_m88CAF6DA4D43FD43D6B42F407696C49FE93DF489,
	U3CU3Ec__ctor_m0FE2AE12D8ED3BA533A9FE180F290766D49099AD,
	U3CU3Ec_U3CGetVirtualCameraU3Eb__30_0_mD00C1D63B243EAC04AF7754294C5C9998BBB1DEF,
	CinemachineExtension_get_VirtualCamera_mD9E9C61D2B18DD4B1ECF1B6A12EE5FD3B152376E,
	CinemachineExtension_Awake_mF3E9E30D4CBBC656B8758FDAF759B5DFA1774C9E,
	CinemachineExtension_OnEnable_mAABA4125E1F4271A991D234F62771AD496E9EF98,
	CinemachineExtension_OnDestroy_m856A803E3DAE93CD0AADFA9B687A430BD24616DE,
	CinemachineExtension_EnsureStarted_mDC4AE5D72929029A5F995886E4E5298269173FA9,
	CinemachineExtension_ConnectToVcam_m2052046FF2E7EB0CBB72680EAA521B5C63CDB0EC,
	CinemachineExtension_PrePipelineMutateCameraStateCallback_m5011C9CC6618F64C0A02C9BFB7E2E572E7DA3BA1,
	CinemachineExtension_InvokePostPipelineStageCallback_m67F6FE82D6B103A0B77281CE76F2BC9DBCAFE0C0,
	NULL,
	CinemachineExtension_OnTargetObjectWarped_mBA612C74EBA9DC865CC6A719C5B08C4C20D785D5,
	CinemachineExtension_ForceCameraPosition_mA120130FCDE1C615EF0C6BBA3548C7B85855CD3F,
	CinemachineExtension_OnTransitionFromCamera_mE638BC9E573F2F32B6D6A01EE6734DF8C54E46D2,
	CinemachineExtension_GetMaxDampTime_mB9B8F4B9F9B418766F43C1660840B07465672823,
	CinemachineExtension_get_RequiresUserInput_m1BA5050417CEAF7E438AADFC1F3F847AD3C64FC6,
	NULL,
	NULL,
	CinemachineExtension__ctor_m8955F80D4A62DE7C3DA510CFECEF722346241C94,
	AxisBase_Validate_mD6017BA404C55814A0E55DD7D036FA666EE038CB,
	CinemachineInputAxisDriver_Validate_mC2EFECCBF1C729D83650ECCB9EC02BA70A3692FC,
	CinemachineInputAxisDriver_Update_m8AFFF82834DDE9F93045956D8A9EEEA933766FD4,
	CinemachineInputAxisDriver_Update_m24BE353BA761E2D8A9EE55CF6274D17C31EB3F76,
	CinemachineInputAxisDriver_ClampValue_mA2A92688571EA4584213869F0C7CA9A7699B2747,
	NULL,
	NULL,
	NULL,
	CinemachinePathBase_StandardizePos_mCD78307D06204C7543CB99D3F2FB5F320B996423,
	CinemachinePathBase_EvaluatePosition_mD21133E8D0CDCBBDE460D6887C77EA998E78AF80,
	CinemachinePathBase_EvaluateTangent_m97E5547FC4721284BB6A39980A5D4F3721BAFD3C,
	CinemachinePathBase_EvaluateOrientation_m4A61E042185BAD353F6C9658E2B12FE93A456034,
	NULL,
	NULL,
	NULL,
	CinemachinePathBase_FindClosestPoint_m57D4703AEE2B706B72070E39406CFF0DF5BE5F81,
	CinemachinePathBase_MinUnit_m0B6884D7FD0928C74C702C191368EF0B37712737,
	CinemachinePathBase_MaxUnit_mD6C8BEEF736AF66618CD9FEA69D61CC5C9854F76,
	CinemachinePathBase_StandardizeUnit_m0BD23E05F3EF8C163CB4F7830B2336B3D0713460,
	CinemachinePathBase_EvaluatePositionAtUnit_mCE1B51BBCAEFF5A65A68F1D3113390F7BC223843,
	CinemachinePathBase_EvaluateTangentAtUnit_m0EA566D1633A62430B68960B5AF3E882F9F2219E,
	CinemachinePathBase_EvaluateOrientationAtUnit_m28859D88DD40B298B14EE6D04A6358534E09C0A7,
	NULL,
	CinemachinePathBase_InvalidateDistanceCache_m5A8B12C547975C78D5167E08B823DCD800799878,
	CinemachinePathBase_DistanceCacheIsValid_m513365FFC2B3206A2C0687DD7F0F3C698C031240,
	CinemachinePathBase_get_PathLength_m7416A92ED48925E5C49D4D6B70C13AEEA4A90660,
	CinemachinePathBase_StandardizePathDistance_mA05975615F90DFD7E4F000026B3C6200DA02591E,
	CinemachinePathBase_ToNativePathUnits_m71355B86B0027D58831E4B9489CCFEE69B7E9158,
	CinemachinePathBase_FromPathNativeUnits_mEFCB692BFEC5A048AF23D9BA3EC74A4255D5D867,
	CinemachinePathBase_ResamplePath_mDB5434016BE1079B26B3DF4C4371B446875648C1,
	CinemachinePathBase__ctor_m8BEE8B0F31272FDA797F0459F992A3B491570A3F,
	Appearance__ctor_m3600590C110F6BC2BBA3F48A4334B8A38A030796,
	AxisStatePropertyAttribute__ctor_m1D11BE55127A1BACC4CB41824FF44D0E00A0DC87,
	OrbitalTransposerHeadingPropertyAttribute__ctor_m5B7E2CE0AC1A499F6E98EFAAE60778B2F21E8928,
	LensSettingsPropertyAttribute__ctor_mFF909C9CF0455F425DCA867724371F4F4EF66A79,
	VcamTargetPropertyAttribute__ctor_mE0BC017BF1E9F5AEC1B8BCFCED347AA3D88D8A29,
	CinemachineBlendDefinitionPropertyAttribute__ctor_mA824C842C2AB60CF5A7528ADFA275AF270B8ADB9,
	SaveDuringPlayAttribute__ctor_m1F7704B00AFB92A9175DFDEC562EFF62D768807A,
	NoSaveDuringPlayAttribute__ctor_m04387BDD20964808880B69859589C3CE10BFE22E,
	TagFieldAttribute__ctor_m37CB058BFE10C3277F3180F177571BC7F985C6B8,
	NoiseSettingsPropertyAttribute__ctor_mE969AACC17EE1AC1C654D65EB7C1E036BB4E16C4,
	CinemachineEmbeddedAssetPropertyAttribute__ctor_m5AA2CEDCBE161BBB0F5B93AB0691E7D72FBA8F32,
	DocumentationSortingAttribute_get_Category_mE12739E1F69EC05F32AB71A8AC8252DC7B8D722C,
	DocumentationSortingAttribute_set_Category_mBF1A4C6A80D15E9A772468A2B4981A7658FCFD5B,
	DocumentationSortingAttribute__ctor_m31E87AB883A8DF0AF3B9D233D4AAE4FE5964DE6F,
	CinemachineVirtualCameraBase_get_ValidatingStreamVersion_m3FA3A728FAF24AA79D08CB23CB7416D250280C45,
	CinemachineVirtualCameraBase_set_ValidatingStreamVersion_mE99F8D7C363F7259CD22F03D43428AA9E14ED433,
	CinemachineVirtualCameraBase_GetMaxDampTime_m4D72BD93F9B3B32BDFB20976CE7A1E8B41567047,
	CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m215A089B8451330FA8D7D6E4DB8E38400AD9E7CF,
	CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m871E131EE59CEEC1B5691F5DC570B18816530C97,
	CinemachineVirtualCameraBase_DetachedFollowTargetDamp_m12B68094CE823031220DD1E2EAB52AAD0AC25412,
	CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_mFB6FAA90EB2A5263D19E3D91C30C072C972E849E,
	CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m3919D9F0DA00F12FA05608A3EBE1345E938E5DE3,
	CinemachineVirtualCameraBase_DetachedLookAtTargetDamp_m8ABF51D39DFC31B7EF4FBCC0139ACDB64FD7F94E,
	CinemachineVirtualCameraBase_AddExtension_m84615F482D5461685B226D4B57539FA2E7F8EE20,
	CinemachineVirtualCameraBase_RemoveExtension_m7047B670478FDAF97D48F2906539F250D354EF6C,
	CinemachineVirtualCameraBase_get_mExtensions_mF66637306356FAB2C06F53097534FC7AA54ECD00,
	CinemachineVirtualCameraBase_set_mExtensions_m14B06AC70E1112D5D87372C2D868EA55B7B218E5,
	CinemachineVirtualCameraBase_InvokePostPipelineStageCallback_m55457276F8291B6645FE4F7250C84DE97DDFDECF,
	CinemachineVirtualCameraBase_InvokePrePipelineMutateCameraStateCallback_m6468DFDE19E8860EC1EE36073D4BEE179D609CB1,
	CinemachineVirtualCameraBase_InvokeOnTransitionInExtensions_m0D02A9178DEE05F756D00FDBA9A4A5B6EE3BB225,
	CinemachineVirtualCameraBase_get_Name_mD5D510F82C6F26960F8036AC76A6B107E0304DD0,
	CinemachineVirtualCameraBase_get_Description_m6D0D078084943330192565A884AA823576E68792,
	CinemachineVirtualCameraBase_get_Priority_m273769ED137982DE43BB658BBE704BCAA55E5246,
	CinemachineVirtualCameraBase_set_Priority_m233ED0376CE0BD1244CCA52DF4532C8988DC05AC,
	CinemachineVirtualCameraBase_ApplyPositionBlendMethod_mD956666402D6A533C23C8AE5AD6DB5C875C2FDCD,
	CinemachineVirtualCameraBase_get_VirtualCameraGameObject_m85F9D37C4395161F7C4023FDCD9858EC8952D617,
	CinemachineVirtualCameraBase_get_IsValid_m5BB3331E066DF4C9A02FA433F785C5B64C3330C3,
	NULL,
	CinemachineVirtualCameraBase_get_ParentCamera_m1A8F8096F8BBD5B994873B756069EA00AEDD5DCD,
	CinemachineVirtualCameraBase_IsLiveChild_m21C2756B555D31CD1EE71FCE06A76254C9E206ED,
	NULL,
	NULL,
	NULL,
	NULL,
	CinemachineVirtualCameraBase_get_PreviousStateIsValid_m4B2B6DD649ACD80A30AA0ACE19449EE49C13DF7B,
	CinemachineVirtualCameraBase_set_PreviousStateIsValid_mA7F5F377366606747B15F5A35EE3E6DA7DAEDDE8,
	CinemachineVirtualCameraBase_UpdateCameraState_mC16F582BFC88FD8E3BC61EEFC11EA243A4289836,
	NULL,
	CinemachineVirtualCameraBase_OnTransitionFromCamera_m867C0945DA41A144EE64BDF095F13CE1EB1B3493,
	CinemachineVirtualCameraBase_OnDestroy_m57F48C3BC5A324EDE38FD6A59FB1A8165EC823B5,
	CinemachineVirtualCameraBase_OnTransformParentChanged_mE18E59ADAD6A7CFC9DB45C0D18D09EA0E9B1D46C,
	CinemachineVirtualCameraBase_Start_m3855F6FFCEC24D4700C39D8798D8558FD5524003,
	CinemachineVirtualCameraBase_RequiresUserInput_m1C2BE14E1F00E4EBD5BD1C9C4AF56E47AEF161A8,
	CinemachineVirtualCameraBase_EnsureStarted_m5BE1C9CC37F882E1B76E2C84BC6B789BF14451D0,
	CinemachineVirtualCameraBase_GetInputAxisProvider_mC735C4764E6CB8469D115142D842729C95D9C39E,
	CinemachineVirtualCameraBase_OnValidate_m53411A67E10ECB12B114F5FC47C1026E0BEDB1D0,
	CinemachineVirtualCameraBase_OnEnable_m56F225786F42BD1069930B91D8448A0779F71F4B,
	CinemachineVirtualCameraBase_OnDisable_mF7435377931FB3F6EC410752D62C18392C47917A,
	CinemachineVirtualCameraBase_Update_mF88D942EB1867E4A52BF819B17FF5BEDE62853E7,
	CinemachineVirtualCameraBase_UpdateSlaveStatus_m84F59A7B5AD0A8C77ADD3F59AEE050DF0F21DB90,
	CinemachineVirtualCameraBase_ResolveLookAt_mDAFDBB0AC2E3CF214A3093116560E55B681BC172,
	CinemachineVirtualCameraBase_ResolveFollow_m9A01047C3AB01393D582E1879EC53213D38AE7E8,
	CinemachineVirtualCameraBase_UpdateVcamPoolStatus_mA377CAB45CBB5AF28FE084955BC2CF4C7C944883,
	CinemachineVirtualCameraBase_MoveToTopOfPrioritySubqueue_mDB771BE69809F79B86410E4102E30C13F0E9044D,
	CinemachineVirtualCameraBase_OnTargetObjectWarped_m918462E96A2EEC9848F6C47B0F35E0D5C0DBF333,
	CinemachineVirtualCameraBase_ForceCameraPosition_m3EE879AD97E2BDB01606CB878C6E789078271492,
	CinemachineVirtualCameraBase_GetInheritPosition_m7D3E2A6630CAE96C0EB4DFD35DC28A1D30CF4703,
	CinemachineVirtualCameraBase_CreateBlend_m8CCA4253F833A686FEA07989BB3D068A246FF2CA,
	CinemachineVirtualCameraBase_PullStateFromVirtualCamera_m0629C5BA281A84DFA090D4B28CFBCC8E234BB298,
	CinemachineVirtualCameraBase_InvalidateCachedTargets_m66FC53DA8DB6FC7BC34EF2B9E6CDF68DB74F3D3C,
	CinemachineVirtualCameraBase_get_FollowTargetChanged_m4CB9C2AA28F8B2898B82BBF51348C6670110ADF2,
	CinemachineVirtualCameraBase_set_FollowTargetChanged_m60A2DC1564B3D6A17821605470D9919C590FE0D7,
	CinemachineVirtualCameraBase_get_LookAtTargetChanged_m6D2FF4FB863501796CB778CB7AABA0126E57C134,
	CinemachineVirtualCameraBase_set_LookAtTargetChanged_mA9FFCF3EC189C5CD35BE0E5B450939B4A8BB1D8B,
	CinemachineVirtualCameraBase_UpdateTargetCache_m0472352417911B6F5E21A85A2BBDA72ECEB85BAE,
	CinemachineVirtualCameraBase_get_AbstractFollowTargetGroup_mF66D843C00156E41B1C9C14FFBA0C4429D29AEBF,
	CinemachineVirtualCameraBase_get_FollowTargetAsVcam_mBABF664226D707405BAAA123F29619FAF6758053,
	CinemachineVirtualCameraBase_get_AbstractLookAtTargetGroup_mC8E5278181AF27E945676602C10DB78E6EE15DAA,
	CinemachineVirtualCameraBase_get_LookAtTargetAsVcam_m51058DC76D5F1833C294015E89A65A64DB36A41F,
	CinemachineVirtualCameraBase_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mBAC03B3A1F38032BA1E5ED1E824EECF6127BAC43,
	CinemachineVirtualCameraBase_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m3834C2540CDE8082263DD7587FDDDF3649306D99,
	CinemachineVirtualCameraBase_LegacyUpgrade_m12831751E9E0697F2A87F5B837C714C242796BFE,
	CinemachineVirtualCameraBase_OnBeforeSerialize_mAFFE51C4E0C07640C3104CF1EC0B808D3A1176C1,
	CinemachineVirtualCameraBase_CancelDamping_m3E85FF31183FA04DA877ACF8708C3985EC51B998,
	CinemachineVirtualCameraBase__ctor_m1BACC836C669C0C52C8A891BAB34E09821F21C46,
	U3CU3Ec__cctor_m83F07ABBB95DE062E87E35CA344B44AF2F9C7495,
	U3CU3Ec__ctor_m1B1FF86140C80F51640B1FBB220C7086D68D0D30,
	U3CU3Ec_U3CRequiresUserInputU3Eb__66_0_m1178F3D4B17F28B357018AF317FF0BDDDD399F89,
	ConfinerOven__ctor_m1C4C18216256E2888451CD5123F9170C805CE8CE,
	ConfinerOven_GetBakedSolution_mF4174B4DFFAB8831F94627D68558CCD505FB3273,
	ConfinerOven_get_State_mA6308A899FB1F875B90CD6D1647071F3380F5470,
	ConfinerOven_set_State_m1AB5EBA915E1EF83FAFA334DA7DD59388CE3AE6A,
	ConfinerOven_Initialize_m71FEAC5752F454084810F5EA33F04B2550B712A1,
	ConfinerOven_BakeConfiner_mB6F563C71AE4CBECBEE74F4A76770B09EE74348F,
	ConfinerOven_U3CInitializeU3Eg__GetPolygonBoundingBoxU7C24_0_m17DC6FA0F5288A71A59D241BE2F58BAF75D7F51E,
	ConfinerOven_U3CInitializeU3Eg__MidPointOfIntRectU7C24_1_m25F63BF3258DE5F2324DA5351CB65A83744063EF,
	ConfinerOven_U3CBakeConfinerU3Eg__ComputeSkeletonU7C25_0_mF1E1E6E7DF36056216EA606F5B4BA90BB5F63003,
	BakedSolution__ctor_mA1172BA1CEFBBF4467D64FFF62D0A2C386AC15F4,
	BakedSolution_IsValid_m3EF3F690673A2328E948D0C5F7252B5775F5B7F9,
	BakedSolution_ConfinePoint_mE43B748BAE673FA3E44EAB0EF05478C0FE4AA949,
	BakedSolution_FindIntersection_mC5B880D5AB3CFCA35837A27BC906CAEC0B2C8ACC,
	BakedSolution_U3CConfinePointU3Eg__IntPointLerpU7C9_0_mB5D748A6BD7AA0AF2B829EBCC5CE96CD771B6F41,
	BakedSolution_U3CConfinePointU3Eg__IsInsideOriginalU7C9_1_m5864C528E7CB650C265F883630E8A06B39BA809C,
	BakedSolution_U3CConfinePointU3Eg__ClosestPointOnSegmentU7C9_2_mB4893132D90DA9B406DBD6D9E85F6DB487FE361C,
	BakedSolution_U3CConfinePointU3Eg__DoesIntersectOriginalU7C9_3_m387046A6D2E2A143D05848F635EC8D48070A805D,
	BakedSolution_U3CFindIntersectionU3Eg__IntPointDiffSqrMagnitudeU7C10_0_mF1681FA711188BB13B1BEAE931206FE45A1A8838,
	U3CU3Ec__DisplayClass9_0__ctor_m1BDB065538D7B8869BB31B5CF477CEDB77B950EF,
	U3CU3Ec__DisplayClass9_0_U3CConfinePointU3Eb__4_mA501F2C6999E9021FC3A4D5CE9A5786FD26F79D9,
	AspectStretcher_get_Aspect_m506D4C16F8E6AE36198ACCAE2C4AFEED14552272,
	AspectStretcher__ctor_m84A17187C183823205C2C732202DCBBAA2350852,
	AspectStretcher_Stretch_m1265459BD5A34090D4174D1D69509C2C53D36A92,
	AspectStretcher_Unstretch_mA27250710163BEFDB2E5F0E074F41B878A7AB08E,
	PolygonSolution_StateChanged_mB1E13BE21168ACB0EA459D4757A52799724CD7A0,
	PolygonSolution_get_IsNull_m55D141117498A201C12B86647EB9B17E7BDDA801,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LensSettings_get_Orthographic_m198D9052494017EEE832066A64F81ADD2B75C17D,
	LensSettings_set_Orthographic_mDD2CDEBC91693B4A25E92DB0DEDE8698DD115EB6,
	LensSettings_get_SensorSize_m1D1F2A7226C400F0062842864C67608E2DCBBD4B,
	LensSettings_set_SensorSize_mD43BCB83C6FBE95F48DAD8993E8DA53927F5CA04,
	LensSettings_get_Aspect_m47C88E8BFBCFA1394AF0259DF528CCC4786A2555,
	LensSettings_get_IsPhysicalCamera_m6AD402DF51FEFD48DC4813C53C10D034B16F9386,
	LensSettings_set_IsPhysicalCamera_m818868B1BD841C9DBD8B9DCBBAE69A14D099D5E9,
	LensSettings_FromCamera_m3A279A7BFABD4F4D4ADA789C44122E5608626693,
	LensSettings_SnapshotCameraReadOnlyProperties_mBFE20278097AE1A1D51AC775347F47AB84E32F4D,
	LensSettings_SnapshotCameraReadOnlyProperties_mEF4FCF04C9AE103AAFE9CDB4531154896BFD660E,
	LensSettings__ctor_mA347110802F0FE6F33CD4D92461CADF6B1B93F59,
	LensSettings_Lerp_mC7E1E297C8E3750B6997616993572DDBCEDDC590,
	LensSettings_Validate_m2C9ABCED7FE02F0C7B575CFADB77493DEEA03B01,
	LensSettings__cctor_m85AA3809A95F2865AA4677633CBDF7EB7917CF6E,
	NoiseSettings_GetCombinedFilterResults_m5F4C940ACF6542F9F08BDE0D4E3E1622AAABD739,
	NoiseSettings_get_SignalDuration_mC7F19EFCFBABC1DA89A8B227811C88AA1BE7A40F,
	NoiseSettings_GetSignal_m1C4C44CCE78C53D1EECE65EDD5077449964DE65E,
	NoiseSettings__ctor_mD5112AED5CE668F76D5260B5BB9FD0104A7756C6,
	NoiseParams_GetValueAt_mDDE2123C634874F162FAFAD1270E443FD9C13F7B,
	TransformNoiseParams_GetValueAt_m541D9F21055D8F6F727E24C5A3D2C26B7044D4F0,
	RuntimeUtility_DestroyObject_mEEBC4EE2A429B4E4D00EC07BA62044C9FF8E8A18,
	RuntimeUtility_IsPrefab_m98872C6B07F84AAF4C91F87F490562A4935ACF9D,
	RuntimeUtility_RaycastIgnoreTag_m7532A8E173E44D2952124031C7973B87FE322B49,
	RuntimeUtility_SphereCastIgnoreTag_m0C12A9B6676E60CB82EF263F23F86296456DC4BD,
	RuntimeUtility_GetScratchCollider_m91C5D854E7110AF1A5F570FFC246BB18181B8AFA,
	RuntimeUtility_DestroyScratchCollider_m4364DE7BEF85A175A66261E184D0A58BA4F62D5C,
	RuntimeUtility_NormalizeCurve_m0EAB5ED8DAFF226807EDD5A2D0B3876FBB5263F1,
	RuntimeUtility__cctor_m7D751C759A76C02DBF3757EB06AFB5DB01914E0F,
	NULL,
	NULL,
	NULL,
	NULL,
	SignalSourceAsset__ctor_mC0043F9E30AE0206A5124C695C6F511EA43B83DA,
	TargetPositionCache_get_CacheMode_m01290EDF84037C27C4ACDD03ED9F0E0D16215AD0,
	TargetPositionCache_set_CacheMode_mF13D07A7F20356D8F2631BF7C238AF542CA2DE77,
	TargetPositionCache_get_IsRecording_m5B0EF7A6DC7DCC938FA7600255944B1353CC982F,
	TargetPositionCache_get_CurrentPlaybackTimeValid_m93FCE647A563303A9390A2409B58FF9702CC9F09,
	TargetPositionCache_get_IsEmpty_mDBD8C0F5EAE840127C82EC96E682119CE1E4D67C,
	TargetPositionCache_get_CacheTimeRange_m3594D3F35DCD95F2A0A1133E871343D3AD37EC5A,
	TargetPositionCache_get_HasCurrentTime_mCB98DBD74426664E1D6E7F5F5DEE8AA2D38352C9,
	TargetPositionCache_ClearCache_m8A8D824AC7D0D548512228B7934901D36091BE63,
	TargetPositionCache_CreatePlaybackCurves_m61C5C2979270EE52949A1FC610A1C1DE53E99FD3,
	TargetPositionCache_GetTargetPosition_m86454680691707EEE6E70984ED39E00821B60B2E,
	TargetPositionCache_GetTargetRotation_m882B2051C458EB18BCF332AFB00112484F31C9EF,
	TargetPositionCache__ctor_m2CCD00DBBBA03A2852EDBD724FC1A386F9F9B814,
	CacheCurve_get_Count_mCC8846F251F4F9C02B89FA7A6FE14FCA89C74DBD,
	CacheCurve__ctor_m06133A4F30CD9E04416FDF29D25BC8110A86E6A8,
	CacheCurve_Add_mF90F9432E39BD237C462FF82B83DD3DF1A48A0BA,
	CacheCurve_AddUntil_mBF186D5A7181FBC89C6870525C47887BBB8DD571,
	CacheCurve_Evaluate_m5C353080800042A73C91D598EC009771A77ABB8C,
	Item_Lerp_mE80C14D381D6951A33443B59D2B4439911D98363,
	Item_get_Empty_m09EC22FCDCCA6A75C1FFE623F2AE7789D55A7E2B,
	CacheEntry_AddRawItem_mB7B07DE2B9D0E5152DFD3788547F22FF4C2688D9,
	CacheEntry_CreateCurves_m01DAEFFC5B38FB88A32CD9738CCBD0F7E168F385,
	CacheEntry__ctor_mED77E8B3231C201C296CBFE39CA995FD45AC1EFA,
	TimeRange_get_IsEmpty_mB51E1F97CF61DD08C502385B2AFC72C6A7F68BCB,
	TimeRange_Contains_mF974E5E36DD20BD63B5B09564B09800AB30CF6B6,
	TimeRange_get_Empty_m5DA39A592AAFE2830C9EBED3C119E1CABE417D10,
	TimeRange_Include_mC1E0A1214F4C3A6FC7F15728A3EB9C398B96F1B2,
	UpdateTracker_InitializeModule_mE6A6DDA8C4EF428E778BD799277089B43F3E933B,
	UpdateTracker_UpdateTargets_mA2095E5739702F84976027C24FA0B8AC37562BA9,
	UpdateTracker_GetPreferredUpdate_mCA810B7D3B87B3A511764993265DFDC2F246C3E8,
	UpdateTracker_OnUpdate_m89A8FE83B9271FE8D7F03CA25F20DE6BD06D489A,
	UpdateTracker__ctor_mBE6F79EBEEE48D9C75839E7CF7952105F4E1C6C1,
	UpdateTracker__cctor_mE2A4318EC8695B882C24BB0252A9A7FB46581637,
	UpdateStatus_get_PreferredUpdate_m31E0BC5E8BDA920C47D2D04EE51BB8060BAE3FD9,
	UpdateStatus_set_PreferredUpdate_mD1E743C821F5D01F8643FBB4C11E274CC266D91A,
	UpdateStatus__ctor_mB7FBA4B732BDB2DCDDCF719176D7FDCB1C8D3EA1,
	UpdateStatus_OnUpdate_m1F8A371233566E0E68A29136C571951AC06C9CC4,
	CinemachineInputProvider_GetAxisValue_mA8EE8963FE6381D4BEA6AD72D32C23EDAB211056,
	CinemachineInputProvider_ResolveForPlayer_mBC7763E40BD8E978D1C030402B8A47FF13A668A9,
	CinemachineInputProvider_OnDisable_m64B4EAD22EC11413F99123C76E3BC2AC59C1FE7A,
	CinemachineInputProvider__ctor_mD407437EB55A2B44CCBC5212077431E81B122A66,
	CinemachineInputProvider_U3CResolveForPlayerU3Eg__GetFirstMatchU7C7_0_m3ADD855E00E795B9BE33D798EA2DD3D8E09EB7B8,
	U3CU3Ec__DisplayClass7_0__ctor_mF98AC1D50BE3684CFF345BF9C2309B14C50C544E,
	U3CU3Ec__DisplayClass7_0_U3CResolveForPlayerU3Eb__1_m2258459485E7ECD621E7DF2486752AF5105C5526,
	CinemachineTriggerAction_Filter_mCB26261B2A07B948A5ECDC34528692F0576CEF3F,
	CinemachineTriggerAction_InternalDoTriggerEnter_mE10EA16EC009B9A98D636DC641FAE6C98E10A50E,
	CinemachineTriggerAction_InternalDoTriggerExit_m551E998CCCAD85A67717E915FB9EB5EDBA20F3CA,
	CinemachineTriggerAction_OnTriggerEnter_m9025492203BE275D711E0A8B35296E36CC22C7D7,
	CinemachineTriggerAction_OnTriggerExit_mE43C1B91D9A3BD5883790F5A3BA86D5297ADC9F6,
	CinemachineTriggerAction_OnCollisionEnter_mB33BB207F8E16E38E54B77EDE8B9716C1B1945CF,
	CinemachineTriggerAction_OnCollisionExit_mB5D84FA567A5E2C652785CFEDDC19952F4DDEE57,
	CinemachineTriggerAction_OnTriggerEnter2D_mD951D1E1A931BFAFB451F31F5A2EE206670A3336,
	CinemachineTriggerAction_OnTriggerExit2D_mD813BBBF15C31020ACC81895354D2773DDBFEB91,
	CinemachineTriggerAction_OnCollisionEnter2D_mB267C61D975BC34CA85A89DE04175BAD29F08A5F,
	CinemachineTriggerAction_OnCollisionExit2D_m8138F3F5653B6019A914598CD8A5AD4F8076BC85,
	CinemachineTriggerAction_OnEnable_m283DC186365A62AB5D92E6824A697C95C842E85E,
	CinemachineTriggerAction__ctor_m750E55C64E2E3A0E9D5A3ABD6F7567974CCFDC06,
	ActionSettings__ctor_m1713D8512FDCD108FF5F41EE681F79311452DB49,
	ActionSettings_Invoke_mF55BAA376882270B52604D6E67EC1EF1F15F8A01,
	TriggerEvent__ctor_mA394074CF7871DB4571DB21160835BEE47169C9B,
	GroupWeightManipulator_Start_mF07981B10129DBE29CFEE1F9ADE65BBE6DD9C9A4,
	GroupWeightManipulator_OnValidate_m4B99CE8E84D3798C03521FB3B5DEDE9C2D4A1DC3,
	GroupWeightManipulator_Update_m3E00D3BEE2D51348E7648A78B8F74CC90349043C,
	GroupWeightManipulator_UpdateWeights_mE2A29DD6CF0FC460CFDD84E71FAD6372C27F47D4,
	GroupWeightManipulator__ctor_m4B4614CE1F0E71BAD1F5335351198F058E0C5008,
	CinemachineCollisionImpulseSource_Start_mD780C5C503490C38F898548C6FB0A7D5C623AF1D,
	CinemachineCollisionImpulseSource_OnEnable_m10190FA2B9F936C0C6FB898DF37334045A8E905A,
	CinemachineCollisionImpulseSource_OnCollisionEnter_m9732E95ACF98DEA64FED280AF703213D0F83CAE6,
	CinemachineCollisionImpulseSource_OnTriggerEnter_m467BE3D2033373E84402CC1E1212A302EA01A25D,
	CinemachineCollisionImpulseSource_GetMassAndVelocity_m2F5253142AC35C57B515057564D6ECD076D61008,
	CinemachineCollisionImpulseSource_GenerateImpactEvent_mEBA5FE1D601106C72CF2AB94A7A743EB61EC74DD,
	CinemachineCollisionImpulseSource_OnCollisionEnter2D_m454EAECFE909B7CE136DF6DCE9A120AD6F1C8236,
	CinemachineCollisionImpulseSource_OnTriggerEnter2D_m547EDA9A08B6FD2293E2DCC177B9B5F0C4B317FE,
	CinemachineCollisionImpulseSource_GetMassAndVelocity2D_m11EA7DF2EB01937666501439E973F242EC9A7E38,
	CinemachineCollisionImpulseSource_GenerateImpactEvent2D_mA36033B447E28159B7762C3FE1042D5EB2164F8B,
	CinemachineCollisionImpulseSource__ctor_m3C8D3ED88B1665A036669198ECFE3AEA06C74DEE,
	CinemachineFixedSignal_get_SignalDuration_m9FE84FB633C279195165B615AC7C5D82AC4624C0,
	CinemachineFixedSignal_AxisDuration_m797DC90D6AFC528AF8B94DFBB98B4C53EA8C561A,
	CinemachineFixedSignal_GetSignal_mA2E81C1A66FDD3644F089E61B12A64D6272C9EB6,
	CinemachineFixedSignal_AxisValue_mD7C8B430549A9BCD3968864805CA52A3385B2AF4,
	CinemachineFixedSignal__ctor_m6E4EB146C0D95EEE4E904171259E075DF24DC84E,
	CinemachineImpulseDefinitionPropertyAttribute__ctor_m33901999C6EBE50D4ED2C2A04BB515F97C7D7368,
	CinemachineImpulseDefinition_OnValidate_mC9E8C5D4227DB0C7467BBDE0004C424B72C083ED,
	CinemachineImpulseDefinition_CreateStandardShapes_m0D05D8A81C5CE6473AC7F8A52BFD502FFECB2924,
	CinemachineImpulseDefinition_GetStandardCurve_mDFBFBA8FDF8DCC1BCC7F7498E1908DB4189D40E7,
	CinemachineImpulseDefinition_get_ImpulseCurve_mAA1558EAB001196015FCCE60BB8DF3200AEC5C4E,
	CinemachineImpulseDefinition_CreateEvent_m12158346403D3428D6DBC0D3D22DDDCF551E5B41,
	CinemachineImpulseDefinition_CreateAndReturnEvent_mCBF4EF79D6393E9D33F9504D30E876AA479C22A0,
	CinemachineImpulseDefinition_LegacyCreateAndReturnEvent_m5213920FF8EAF05E85F9D1D92A94E40AE04A087C,
	CinemachineImpulseDefinition__ctor_m9A793CB4F91B42D9A7D38C1953A0A081A04B4045,
	SignalSource__ctor_mD776DEC5B0B9B1370EC2E238B46C5AD936C9B0B5,
	SignalSource_get_SignalDuration_mFFE7C306FADED4391BD9545AF68ECFA6CA95B751,
	SignalSource_GetSignal_mAB4EB25F3B71C410FDF51FA4A2115D5EEFBEA1FE,
	LegacySignalSource__ctor_mDC721B7D0B692CDD699AB2012C53D81E0864AA8B,
	LegacySignalSource_get_SignalDuration_mAEA4137DACAED4BA10E1161309A87C8305904373,
	LegacySignalSource_GetSignal_mC5FAAE1D894534894FE2312D28EE5314919C6D34,
	CinemachineImpulseListener_Reset_m7B09DF77C46498F557E7DFD9342ACE488AE9FCF6,
	CinemachineImpulseListener_PostPipelineStageCallback_mCEF55A67F8286EA75D6B8930474DFD4F70B6F315,
	CinemachineImpulseListener__ctor_m0C42D66C35F6EC6323B195B56B5F4DD2B287CB5A,
	ImpulseReaction_ReSeed_m0E5974B5A368FE95D4137FB5A87BF71699535DB5,
	ImpulseReaction_GetReaction_mF1A9A410B1F05368B416656512188311E44E8CC3,
	CinemachineImpulseEnvelopePropertyAttribute__ctor_m62808EE32019CC07FAB95B0A2DB0E20A61D5DDF5,
	CinemachineImpulseChannelPropertyAttribute__ctor_m26E7D8A1C8D42789392B0CBC9BF4755CA01D2E49,
	CinemachineImpulseManager__ctor_m29FD2A34BDDE38151BEEADBEDE5A966E91FFC143,
	CinemachineImpulseManager_get_Instance_mCBBB7EE190D29704E4F44241A50BC2F6F23EB179,
	CinemachineImpulseManager_InitializeModule_m7383E0726196C358A3DBC6574F9082010E318FEE,
	CinemachineImpulseManager_EvaluateDissipationScale_m0913FA74400EE4645D1F48867FED336DD6C03239,
	CinemachineImpulseManager_GetImpulseAt_m60503248A6E51D9CB74F28148A982D8FA5C57D04,
	CinemachineImpulseManager_get_CurrentTime_m67D3ABCDB387670ECEF30D37A8E3CBDA5EB530F4,
	CinemachineImpulseManager_NewImpulseEvent_m10EB7672F67FB33CD98758F3CED27E168241508C,
	CinemachineImpulseManager_AddImpulseEvent_m248DA95ECD499D4B501E282B5886389C02F0D223,
	CinemachineImpulseManager_Clear_mA6DC571A238EB0C4C7B07C06D2263B5743FB82BA,
	EnvelopeDefinition_Default_m57F738873D011A83AAC0BFF7DCED3A74BAC13E06,
	EnvelopeDefinition_get_Duration_m3CF863DC6B71344BE096AD9CBFC6C86AD2FF634C,
	EnvelopeDefinition_GetValueAt_mAF39D22894C1B21FDA3A5D7C50AF4AEB393BEE6D,
	EnvelopeDefinition_ChangeStopTime_mFE038CADEFEBC54B1262B69139C2C8C209CE1196,
	EnvelopeDefinition_Clear_m70B8BFD4F76297739F4D6EC3044D46832196D4EF,
	EnvelopeDefinition_Validate_m2DE55071F0E90DAF09281B7735FD96A93D278C37,
	ImpulseEvent_get_Expired_mCA721141A1BF8EABF33B58335F3BF081EE517160,
	ImpulseEvent_Cancel_m9B2DA20770168C09A4245378190B67A574444BC2,
	ImpulseEvent_DistanceDecay_mD470E344C6B95A38CCFFD8EA0869E98D30141C16,
	ImpulseEvent_GetDecayedSignal_m0A606723EF1C6867ECAC33BBBDCE4826BE5AB78F,
	ImpulseEvent_Clear_mBE18EB8EBB0F8F266AB084B44307155CE823C17A,
	ImpulseEvent__ctor_mC5F7C05F235EB65CED1FF6E0FBC6C83D9BCBF81B,
	CinemachineImpulseSource_OnValidate_m5AF490404689D1FD50FA16C1A3621FF85B4E9D28,
	CinemachineImpulseSource_Reset_m6D1F0F55B77C67D7D03992E820A189D52DF9EDB9,
	CinemachineImpulseSource_GenerateImpulseAtPositionWithVelocity_m943E5F58B6A439998A9C427F42FEBF61094F82C1,
	CinemachineImpulseSource_GenerateImpulseWithVelocity_mF16A92675C0D88821A81722EE1CCEA704F17258F,
	CinemachineImpulseSource_GenerateImpulseWithForce_mB82D805193E1A63D2EBAE187F450E110FDFB2780,
	CinemachineImpulseSource_GenerateImpulse_m0AE0716BE48B2DE1186BF9757917BB0BD8E9999A,
	CinemachineImpulseSource_GenerateImpulseAt_mC9E45E6ABBB7111E7553994C17F3AA71A3E025A8,
	CinemachineImpulseSource_GenerateImpulse_m700534622EE72CDDAFEBEFAB2E60F88654660B6A,
	CinemachineImpulseSource_GenerateImpulse_m64F06385534151C880B380839786BC1C1B59D5C6,
	CinemachineImpulseSource__ctor_mAC87ADE7ACFB4405EE58B0C6AA0AE5D93EA3915E,
	CinemachineIndependentImpulseListener_Reset_m73C4B1F95F2A15991E711CC74A4AEE221108AF29,
	CinemachineIndependentImpulseListener_OnEnable_m5439B23A0659AC177EB940684D9C1D17BC03F470,
	CinemachineIndependentImpulseListener_Update_m6869DA3648BEE15F08D80FBC4EEF6842D7023C6D,
	CinemachineIndependentImpulseListener_LateUpdate_m7F9FFDFE7E2C8ABFF7DC8D9B723DAC97B966ACDC,
	CinemachineIndependentImpulseListener__ctor_mE2B8C7909A17100C4E1F08A3B2889187C7E58261,
	DoublePoint__ctor_m87B0A4A8419F7E7608DF989FFBD0E92295A0A72F,
	DoublePoint__ctor_m1DD41CFCE7D02EDD548BC15B67AF3A5FF8919DAB,
	DoublePoint__ctor_mC072F91863E140D4884B37BD4072B89E6ACAD877,
	PolyTree_Clear_m9590AC2AB10E2126CDA65C9824B31FC88E191185,
	PolyTree_GetFirst_mACA8C473F4D95CBDA2F146C9648514B60D8931C7,
	PolyTree_get_Total_m8DC091670D07F6D08D9AAE2C1991C305464F5C5E,
	PolyTree__ctor_m25085F6B77087E89C73FFB4335CD09B9848E8246,
	PolyNode_IsHoleNode_mFE61220C7F4EB0C946AD5D87241DD51162D736ED,
	PolyNode_get_ChildCount_mC79FA922B67A88E6E940285881BDB16109D57C67,
	PolyNode_get_Contour_mE6F44528DF76BE1EF3CF01FC0F7D3957DE8B8AAA,
	PolyNode_AddChild_m0AE9AA1CD1E9EB371362350CAA9304F95A2D2DA5,
	PolyNode_GetNext_mFCD1626A75F96BF0AF694F9D3796E7F889AA2E40,
	PolyNode_GetNextSiblingUp_m69D8EDA536BA118464D65E808DCE56325EF2F2ED,
	PolyNode_get_Childs_m35BF478E71CEEAEC1015E536C4144BDC1877C5FB,
	PolyNode_get_Parent_m6DF789E40F975877A3439B525B8456883DFF401A,
	PolyNode_get_IsHole_mDBB3A947DDBE08C1270D230F56DBD51066C2D37B,
	PolyNode_get_IsOpen_m3EAD7E4249B97146F59766B32A6D00EC0E766A0A,
	PolyNode_set_IsOpen_mAD635554DCA3C131384AC9FB2D63F8E7D2132974,
	PolyNode__ctor_m7DF908F626C3C169AEBAF5DDE0AAC631F0153E85,
	Int128__ctor_m99DFFF77A5A8617353DAB1869A052B84E772B858,
	Int128__ctor_m8C6D7B59F2A217F10B2117D900C9890B7BA677B5,
	Int128__ctor_mC6B960C84486E99631A6B965A0D0357EFC13756D,
	Int128_IsNegative_mC7705DD6A31CD27D6F86081FB8E63A61FC51DBEE,
	Int128_op_Equality_mF1F30DB2596C14A9FC5571B629156D0AEFE92456,
	Int128_op_Inequality_m02FCB8EE852ED36C0C9E915C42641CA84C436328,
	Int128_Equals_m3A1780C1A972388F69135020EA85391611CF4777,
	Int128_GetHashCode_m03339EA88F9C7443DD7F84339389F35A70703484,
	Int128_op_GreaterThan_m849AB9CDB36CF25794F5FE1A318FAFA40D0DCBE1,
	Int128_op_LessThan_m9FE58A2EBB1CDB6B8B25FC546BEDF387F0C7AC39,
	Int128_op_Addition_mD0A0D022CBA36F57EA01A2AFBAE91041818348F8,
	Int128_op_Subtraction_m3786F47F422FA9FD0AD62DC0B102B065704BCF24,
	Int128_op_UnaryNegation_m5D1CBD65C37A7819B4F2B398F894768719D043D7,
	Int128_op_Explicit_m76E4BD7F4E70AD040E37C08649DCBB9FDA5D89AD,
	Int128_Int128Mul_m09B7DC6DFEAFA7226B5B0DC71BA6EDE669E48EBB,
	IntPoint__ctor_mD85ED4713EDE2C713D37C9CD776B791F21C048E1,
	IntPoint__ctor_mA161A7BD0009BE271681C45C59B291FDB224616E,
	IntPoint__ctor_m64D954CC64AF3DEB13E5E64F544E46789983BDEB,
	IntPoint_op_Equality_m1895B2D711859DD40BE3B629806686F2EB454FC5,
	IntPoint_op_Inequality_m6755E58DA3258E1041F8C8C1A0DBB6AA20497EC4,
	IntPoint_Equals_m6E23677EC9306F8D66DD717865D49A3E9A9BAB5E,
	IntPoint_GetHashCode_m19A1D74A3CD370690B8C8751C6762CDB47736788,
	IntRect__ctor_mE1409649D5D928E097BE8C8B4B771DC881052464,
	IntRect__ctor_m40FEB4C1F3B4B8123E190855A65E09E81D766961,
	TEdge__ctor_m8B8B5C7EDFCE49053A6831B431DD8778DA48A793,
	IntersectNode__ctor_m531399DCA2D444AA95E847A819983CF287A38A93,
	MyIntersectNodeSort_Compare_mA615ABEEB4A5BD5FC383393FEAE2ED508CFE2DC4,
	MyIntersectNodeSort__ctor_m0319149B8A1830A064E146C75B97ECF76507BCA6,
	LocalMinima__ctor_mE19D8A32FAECDA7B52BD732D58E68E0DD486A0EF,
	Scanbeam__ctor_m6A54606B58D387BF7CEEA2F05C2604FFC0CF97CE,
	Maxima__ctor_m831FD8937546D1F859449A8BB3AD2467476CE9D3,
	OutRec__ctor_mE1E0B900C419695A0EC929A77C823DD7B806D715,
	OutPt__ctor_mB35AA5D0B77A19A8611ED4C8B748BE311B413C80,
	Join__ctor_m0275B6A7878993163832BBF4D62AEDAB9058D665,
	ClipperBase_near_zero_mDAFA25942FF6D795F79D917BD5A178F5085E0504,
	ClipperBase_get_PreserveCollinear_mF23688F07E4141A6BB90080B464208DF71B73328,
	ClipperBase_set_PreserveCollinear_m53E7942B8B6AE1A1C850791D7FB63C094BD3AB63,
	ClipperBase_Swap_mF999B5BD7A715214B574ED4F7A74092F67F9A836,
	ClipperBase_IsHorizontal_m018DF969F9BD306E460CBA58013819081441C411,
	ClipperBase_PointIsVertex_m59600D27C1BACB19FC117EF15967FA90933D8A33,
	ClipperBase_PointOnLineSegment_m0FD505D256EC78BFBAFEC71564228375FFF90D18,
	ClipperBase_PointOnPolygon_m0D2EBFA14EEFE915C44B56321B5BBD18DC44D536,
	ClipperBase_SlopesEqual_m2FD334595102DCBC8C3DEBCD7A84BCF4A30F8A4F,
	ClipperBase_SlopesEqual_mAA286348A715F1648DDF94B0D6D79AA678A438F7,
	ClipperBase_SlopesEqual_mFEBD4FD4B503A8D4CB75DD1E0B870B71E1BC580B,
	ClipperBase__ctor_mDD73F9BB8C4F62B9182AD325A825ECFC5B0FC2B8,
	ClipperBase_Clear_m3392AC2BFE9E6F6F31BE1302250A427D6B067619,
	ClipperBase_DisposeLocalMinimaList_mAAE9BA56E0950FF2C34D33C4D5DBA769E028F939,
	ClipperBase_RangeTest_mA4C55F148BD7F493C28F0504FEF4DA2290D47877,
	ClipperBase_InitEdge_m982226D0C1C630392C77A166BEAC4761A8F93EC5,
	ClipperBase_InitEdge2_mE657B680AC1506D145CB96A57D7B7ED14EAB5EB2,
	ClipperBase_FindNextLocMin_m45C5FE3F29B6B82782CDCF8AFF7A24C5EE3C397F,
	ClipperBase_ProcessBound_m6A494AFF34846AF6948C68F98400404423E62AD4,
	ClipperBase_AddPath_m34810D1B9F21314A52EB4A1B77F454EE19CAA62F,
	ClipperBase_AddPaths_m3A4E3756B561A396C16E4658743F61D088FA2EEA,
	ClipperBase_Pt2IsBetweenPt1AndPt3_m8DAAA16021926CF945ED648C6B39AE2E2B54B344,
	ClipperBase_RemoveEdge_mEE738D2187081283AF322CF5F9FC9E231AB2E17A,
	ClipperBase_SetDx_m471055292D10981774F3433FCBE57E7E508CA16E,
	ClipperBase_InsertLocalMinima_mD4018919209DC3F43CEDA9FD7284F71F629A564B,
	ClipperBase_PopLocalMinima_m4E164FB1C3B362046B58791AC93C616A5E054247,
	ClipperBase_ReverseHorizontal_m763FCD506B30C857C7C100FD33F2CB0BBEC75444,
	ClipperBase_Reset_mC6F18D6EBC923A5D2BDBCAACDCCF2BA3E6A70B52,
	ClipperBase_GetBounds_mC13AB297ABF4C0D5C98672C33C9B8358242A0ED7,
	ClipperBase_InsertScanbeam_m69EAA6C8EF670077B1CA2FE21CE5CC8060294417,
	ClipperBase_PopScanbeam_mD7D3E1613AE0AD4A4E1C317C5A3B60D9142F40FF,
	ClipperBase_LocalMinimaPending_m5D90C289A0E26A71881F938546EB817F038B0ACF,
	ClipperBase_CreateOutRec_mE25562A65CFAD14C0D80ECC335FC513D73F13073,
	ClipperBase_DisposeOutRec_m6A228CBBF968B111B5ACBC23BE7CE16C1E4F0D24,
	ClipperBase_UpdateEdgeIntoAEL_mC31E817EFC95348D1F3898522D45FB4041431F1B,
	ClipperBase_SwapPositionsInAEL_m60BE707FB4A2F4F7AB84F60C86E9421A9E23A268,
	ClipperBase_DeleteFromAEL_m7D98BBD07D917A74E6C0F683D33B1D01D2DDE1F1,
	Clipper__ctor_mFE1DEBE1F52B653F893824C803EC03A94560C146,
	Clipper_InsertMaxima_mA2E8085C09D3A5519556B24A3C619D2087DA8421,
	Clipper_get_ReverseSolution_m58207CBA9E1BF47F359F847CE77963A18D3FE70D,
	Clipper_set_ReverseSolution_m4C3D2FEE37DDCCB590395F6A7199C8510813834A,
	Clipper_get_StrictlySimple_m3BF161D9925CF5125B2D8786A8EDF317D11EF46B,
	Clipper_set_StrictlySimple_m01963B17681B30F7189B6C54CCA2D222B5537007,
	Clipper_Execute_mBAA9C5AE591C28C14F8E235A726810482235D5D4,
	Clipper_Execute_m21D40DF8C4615CEC73E449737C33394FA2057F61,
	Clipper_Execute_mD4963862505A6417805A36BDC8EE3551AF5227A0,
	Clipper_Execute_mDBF9988159022C1F340702FED6E189758BD23D52,
	Clipper_FixHoleLinkage_m8383DDB3534A268C9D0BCE5A923FCF4B1284C83B,
	Clipper_ExecuteInternal_m71E3C0DA830D24D2F6BDAB1B5D01E8C87DABDD8F,
	Clipper_DisposeAllPolyPts_mF4263E2B3EDF1A70EA2A11C865C64C13FA097D9D,
	Clipper_AddJoin_m8ADF8728F8BD9FE7631356696FA27CB7C110E70A,
	Clipper_AddGhostJoin_m990DB3A2075C093C710D575BBC37BA4A4C969C6B,
	Clipper_InsertLocalMinimaIntoAEL_mA8C63CB0128DB1A1C703BAD42D8CC0E08E1685B6,
	Clipper_InsertEdgeIntoAEL_mC2B55AE5665316899504CBCA4F7F2D3390A5C019,
	Clipper_E2InsertsBeforeE1_m88B8A274A20CA89FFBC22FD18CE23758FF70945D,
	Clipper_IsEvenOddFillType_mE55640494B101C66E086F351AA619705F7ABC0C5,
	Clipper_IsEvenOddAltFillType_m4E105EE325099625299F946B0BE5675868BE2BA3,
	Clipper_IsContributing_m05CCF9C25A8F921D8EE4A1B88E810AD844E74012,
	Clipper_SetWindingCount_mC49B8B45D72F8BA8A1B6E4FD58D5305B158B8E21,
	Clipper_AddEdgeToSEL_m5D29FF86F0F6B1FA0CC46FE4E86780244E8CF0B5,
	Clipper_PopEdgeFromSEL_m1A0A7CACEF17CBF53D2D5404064EB11D2426D161,
	Clipper_CopyAELToSEL_mBE2FFC8FFF11AA874A8F542E437DB2FA824B32F7,
	Clipper_SwapPositionsInSEL_mD3379C551074FD2F32E777F91952245DEA9E9594,
	Clipper_AddLocalMaxPoly_mC531749CA70DF137D954703614E013FCFD6068D4,
	Clipper_AddLocalMinPoly_m3214E0184F98D4553AED2B6DB0CC1C6A4BA6331F,
	Clipper_AddOutPt_m46F920591D075726FB133415FB8D46A51301407C,
	Clipper_GetLastOutPt_m4E969A4052E60A00532A658AF980E03CC6DA4131,
	Clipper_SwapPoints_m87CAA96126A2EC872972D1675CD6A9DECDD96037,
	Clipper_HorzSegmentsOverlap_m5FA9498ACDD1B422C8024DA1A7BBB24BAC552790,
	Clipper_SetHoleState_m0C85355B78FE5FEE59358DCA9241F8D6FD386529,
	Clipper_GetDx_mC6E8199B23B026C8AE8C793B05AED49AF0EA8355,
	Clipper_FirstIsBottomPt_m0BC35D9AAC75A173CB303ACCD69EFB2EE72F1391,
	Clipper_GetBottomPt_m54E17AA4BE26A3CC71C96BBAD7ACBC3DA61DA18F,
	Clipper_GetLowermostRec_mA8822C6550166EF7904D4B4C20ED66102F064F7D,
	Clipper_OutRec1RightOfOutRec2_m6F9DFB83D02704D771F43FB7E25088060393CF9F,
	Clipper_GetOutRec_mA981F566088446A10C95006E435C4BFB43A98BC1,
	Clipper_AppendPolygon_m737BF9DD8CA443E80402E3E7ABC2C32EFD839F1C,
	Clipper_ReversePolyPtLinks_m10B9631CBBD7C03F36B509D8F5BDA2BE7B6D6FC5,
	Clipper_SwapSides_mD8D5422E34AAED12D88D5FAEC03E3562FDFE4413,
	Clipper_SwapPolyIndexes_m8D0C37CBD6054A5AD310E5FA0642D0EFFF32D445,
	Clipper_IntersectEdges_m38A4C149A9CAA52BBB887233919F6D3770B0C6CB,
	Clipper_DeleteFromSEL_m1E414DF925CB7E80E8C88B7208954F86CD6BAC6E,
	Clipper_ProcessHorizontals_m2D749E6B682EB52D3815374C4C1194B40E81315A,
	Clipper_GetHorzDirection_m2746F275D04EBDAE2EA2C70C4BC402C3B587F4AE,
	Clipper_ProcessHorizontal_m23F417A997A068313227F265CC9A713CAB63F56C,
	Clipper_GetNextInAEL_m7A6526C1C5278EC4ACE3A69700E6D82E36A4B274,
	Clipper_IsMinima_mDBA5FABD10350ECB457547A781997F9E165D099B,
	Clipper_IsMaxima_m15A892565E5C8644F6F1F5EE948B4EBDDE7FCD9C,
	Clipper_IsIntermediate_mDB13F1C11F973F437A65A406235DF8B2A6B0D419,
	Clipper_GetMaximaPair_m8F0A19E8344154A1A55DA93D025C143CE8876820,
	Clipper_GetMaximaPairEx_m9356317DFEFF8514EF0391D0BDA06802879B98A2,
	Clipper_ProcessIntersections_m561C929E8754747190448077F511FC1B74093FD3,
	Clipper_BuildIntersectList_m6B5DA2A81AF660F5471A530B97383E223BEECB66,
	Clipper_EdgesAdjacent_m8CB2FDA541E506262090D1B39A8585158A4CDD50,
	Clipper_IntersectNodeSort_m7D01712EAA690EC202397BBA1ACD1844C762DDBB,
	Clipper_FixupIntersectionOrder_m11A6732B2BAE536BDCEFD38A652944B153C2BE9F,
	Clipper_ProcessIntersectList_mB3ADFF851F65B6F8CC05E1E5CB46265779E862E1,
	Clipper_Round_m045C6DD247A771A5626C5E50CA5C133836D294B2,
	Clipper_TopX_mEAAE42DE6C0AD711F8BC435773AD506749A62102,
	Clipper_IntersectPoint_mE17B6245017EAD8BEA10EDBAF03A182AEABCCFEE,
	Clipper_ProcessEdgesAtTopOfScanbeam_m7AF18AD101770BC5E9FF00A769F2AA855D803866,
	Clipper_DoMaxima_mFAFCF9A9E0A4CB22C57EBDEC6564493006160F18,
	Clipper_ReversePaths_m2BF00831C6F40C9DA37A2EA51B2E5E7735E9F2E2,
	Clipper_Orientation_mCCFCAC82598BB2359FAC352A25CC7CD9DF71E406,
	Clipper_PointCount_m5DAEA2750B49726731544F0F61B694295D5A4FC8,
	Clipper_BuildResult_m8DEC825FFD16A1CBFAE17FEB01E21A6010850DF8,
	Clipper_BuildResult2_m115358F60E247678C58F113E4AADB97BFA2789A0,
	Clipper_FixupOutPolyline_mE8755CFCB5009D731A43764719EDD8A34BDECAD3,
	Clipper_FixupOutPolygon_m1E6DBAE927150B9971F4115A5764DC6992969660,
	Clipper_DupOutPt_mF94EE9A7F893922E4E8EEEBAAA8B91E06E748D12,
	Clipper_GetOverlap_m40BDFF02F99393EC15CA74B5C6F680F2198D05D7,
	Clipper_JoinHorz_mA2DA78D8763F8883DEB40BA70E79D306CB306FF4,
	Clipper_JoinPoints_m8DE3553CA0E63A41839CCECB808BAAD4CD30CA70,
	Clipper_PointInPolygon_mBEDDABD4FDE3DCA3142EE3D6341E5B41E243ADB6,
	Clipper_PointInPolygon_m6B65511BF890F9C19B1CE324C3F4A7016EF3404B,
	Clipper_Poly2ContainsPoly1_mB4ACD78F9B8E488BF07BF33B77C7494796A22ED4,
	Clipper_FixupFirstLefts1_m65E2AA48CBB7DF4953C93306CC38293E73751B37,
	Clipper_FixupFirstLefts2_m3628DAEB783FFA0C15CC88FE7D5A43031780BD19,
	Clipper_FixupFirstLefts3_m5FE8223D4C067F2405FB9E5397CAC2E91B799EBB,
	Clipper_ParseFirstLeft_mB314AD38EE3F6A8E49A1D7889FA2F60EF818F3AD,
	Clipper_JoinCommonEdges_m1144D7D13B127B352BEE7B56E194E6FA5223144E,
	Clipper_UpdateOutPtIdxs_mC571A93028E6202AB3531A1F6583EDEF411E70AB,
	Clipper_DoSimplePolygons_m2B00FD05078E3E8689721DC7C62FA9EEBF9744A4,
	Clipper_Area_mA39460C41233FBD033C7222201D33F5A56DC4661,
	Clipper_Area_mF674418737AC1594BFB2CD37162839CBC117A13E,
	Clipper_Area_mFA662DAA8CD3045CAAE005E591ACC6782AB49164,
	Clipper_SimplifyPolygon_m5D964DFAFE6251CB5230892673D1825829A41608,
	Clipper_SimplifyPolygons_mC43520F605202A488565AB6AC98609C829BC27B7,
	Clipper_DistanceSqrd_mE09149587060D3E1E2613F6A466F22BFAC784871,
	Clipper_DistanceFromLineSqrd_m83EB8AD5E4740D6D2C9E05A3A50B6AB7B93125C2,
	Clipper_SlopesNearCollinear_m995CC508199595093168D5AE1B06C6B029092DE4,
	Clipper_PointsAreClose_mB37FFCEA3A853FD6D1FB9EB698C6ED444B5438DB,
	Clipper_ExcludeOp_m0BF04A50C9B809D52528BA1C2F3E1488B1D4A469,
	Clipper_CleanPolygon_m983259D79EA94E281D1F6EC14BEFDC191BBDEAEA,
	Clipper_CleanPolygons_mAB8185A21A022A9A5240A2378F651D8A34B8FBB1,
	Clipper_Minkowski_mD85E9DDB9CAF5B69BBA8B4DC2E4E2D58FB81F32A,
	Clipper_MinkowskiSum_m29ABD52399BCF104E8685795B86F0E1A2A28C1C1,
	Clipper_TranslatePath_mF158D477E0B6F15F4A22EC080212529506ECB08E,
	Clipper_MinkowskiSum_m02D0DB720CCF4A9A76EFC0BFF6B0BA477D1B6D3C,
	Clipper_MinkowskiDiff_mE2847469BCCD1BD028179E51C352DF12D9473C04,
	Clipper_PolyTreeToPaths_m9E06A3B3F6527EF442C636FB195AC7E67A0C38AF,
	Clipper_AddPolyNodeToPaths_m94EC1CD2E0DBD0759A9C448163D82A373025BEE4,
	Clipper_OpenPathsFromPolyTree_mF1221E00B6D25AFAECFA106E1CFC43137BB42EE4,
	Clipper_ClosedPathsFromPolyTree_mFDF300DCFD546952007701D71C87A90E86CE0874,
	ClipperOffset_get_ArcTolerance_m25B298CB94489BE792C4EF8870B396D2158C3E92,
	ClipperOffset_set_ArcTolerance_mF1025B552D6EB0FA28D7E6807397DC0DC4DC93B0,
	ClipperOffset_get_MiterLimit_m12626F15B3A2B944F81E05ADBB263C8C3479D7DD,
	ClipperOffset_set_MiterLimit_m81F85F0942E5A1B4A131627D7E7DFCF46BFCD859,
	ClipperOffset__ctor_mEC075A2F9B5E587A9D06CF9E45A555417C69D158,
	ClipperOffset_Clear_mED8C219339D64A95987DB230DFE13C32FEC9CF87,
	ClipperOffset_Round_m5FB50144D5D0ABDE18741EC99B86BAF660F25F4F,
	ClipperOffset_AddPath_m30CDF70BFA54E89D99CBDD267D513347A6D46EEA,
	ClipperOffset_AddPaths_m48B4B426171D6EEAE676FCDA8E55F6FBBCEE66A8,
	ClipperOffset_FixOrientations_m32685E41D88FB8B7FC06282DA7C128E197C7C518,
	ClipperOffset_GetUnitNormal_mE79775AAC5E47077FE6EC3003830E30BA5D42786,
	ClipperOffset_DoOffset_m21188ACCDBCEA566BFD6DEE0D03F9B5340CEF009,
	ClipperOffset_Execute_mB6E649005853A610D808D8E5F0CBB8EDE436D7C7,
	ClipperOffset_Execute_mAD1CC573A536C2365E3AE2290B6AB3EDA1D2FA03,
	ClipperOffset_OffsetPoint_mA364CFD851041996876AA12D1E32CB8CD56FD35F,
	ClipperOffset_DoSquare_m6FF4AC6A3D3A432776BEA5EC3869CC1EED8807D8,
	ClipperOffset_DoMiter_m2797F6E5BA1D568908043B4883ED4850EFAF39C3,
	ClipperOffset_DoRound_m87BA50E7B0346922ACC73C69B6D28E9099E42692,
	ClipperException__ctor_mD885137DA5F4345C11209699548F776B93180F1A,
	CinemachinePostProcessing_PostPipelineStageCallback_m4E796F5B2758A2E4DB5CCE4FCFCF6545BC248033,
	CinemachinePostProcessing__ctor_mCB4146D6BBBE2356147A9DB6D2E0F349B89A908A,
	CinemachineVolumeSettings__ctor_mB23ECA31DF611267B81182121229A3B5EF12014B,
	CinemachineDebug_ReleaseScreenPos_m6C54E91372A22F7D171D8D91C454DF06E95A6593,
	CinemachineDebug_GetScreenPos_m1C7476476BC6BB8D907CCF2D4B946C8B029872D8,
	CinemachineDebug_SBFromPool_mAA83D56A38ECFD61FC135792DC0778A7B152938D,
	CinemachineDebug_ReturnToPool_mBFBCCF1AEE29E2BD115AF5BCD172BBB8B95C5EA7,
	CinemachineDebug__ctor_mAF77C21C69B520883959BEC3DE58BBDD83CE5F8E,
	OnGUIDelegate__ctor_mB8767C1FEE32279209BC7F763E7C133C62B92FB0,
	OnGUIDelegate_Invoke_mA8B9CF3C0FA6CD716557C7E66D18F061E6410464,
	OnGUIDelegate_BeginInvoke_m7F8DBBFCF5F26BB61396A8DACB90A543E942EE20,
	OnGUIDelegate_EndInvoke_m333ABE0CEA01B43E00AE0D77A7CE8279DDF2604F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GaussianWindow1D_Vector3__ctor_m24BDA3F6806B2C7687313112EF321052C4FFA574,
	GaussianWindow1D_Vector3_Compute_m50CFE925DFF71745A8032D15E19D650B6A4AB4A4,
	GaussianWindow1D_Quaternion__ctor_m34E6A4BE5AEDDB27919FB447706477666FDEF7BE,
	GaussianWindow1D_Quaternion_Compute_m7482A5084DE01B7B0AAF60567BF9FBBE2C3A1738,
	GaussianWindow1D_CameraRotation__ctor_m93E64892C4CC9FFE4D20A5AE0EDB3761E0D357C5,
	GaussianWindow1D_CameraRotation_Compute_m5D42413D3C9A040A0E75B1B3FFFE24DA04628AD2,
	PositionPredictor_IsEmpty_m61F4928BCB8526CD0A823F2B2A46FDE04905C97B,
	PositionPredictor_ApplyTransformDelta_mDA012CCA329F143DDF342616369F0E75B2E2C97A,
	PositionPredictor_Reset_mDA454522FB1823437E5538169D712A2E18F956C5,
	PositionPredictor_AddPosition_mB5EFA6BB6598A9D52D1CE6BD50400E56938C433C,
	PositionPredictor_PredictPositionDelta_mC16231F75C5C088B5CC2444D3C2FA12F6DACC4AD,
	PositionPredictor_PredictPosition_mB280F23A4D236037F339758BDEC4AD2DE89AB18F,
	PositionPredictor__ctor_m98DC334F817608D8CA4FA09966193AA59A16DB25,
	Damper_DecayConstant_m66A0B4920185F5E89CB4ACA436BBA7DDDB7AFBBC,
	Damper_DecayedRemainder_mFDDE3E237019D299FCB912A50836D5B89714F048,
	Damper_Damp_m3245F3453DB74DE11801C6EE15F163D5EAFC0CE4,
	Damper_Damp_m3FF6416E2538374C5805A252094351151793F5E7,
	Damper_Damp_mBBBCCE7F67FF9678EE42AA0B0EBD7BC4FFEF2FB9,
	HeadingTracker__ctor_m65E930C6FC3B44B9DE66B61332E4A960A14BE25B,
	HeadingTracker_get_FilterSize_mEF06A6674D9D5FE8F1802922DECACF11BA7BE151,
	HeadingTracker_ClearHistory_m220EDE26052383AFAD5F74F885541BF3797352A5,
	HeadingTracker_Decay_mCBB06FF8B9BC4A154A4EFC9DA910854ED0FB25F8,
	HeadingTracker_Add_m9FC794FA982A8598BC1FA0DB46EFAA7507CB861D,
	HeadingTracker_PopBottom_mCB0D0A7AE4238580CB7FFAAD17497F7B532B57AE,
	HeadingTracker_DecayHistory_m9E2B8A0731C6C492AE78B36925860F4A3EFA1BB7,
	HeadingTracker_GetReliableHeading_m3277A5C1F94F1269E38655527EB71AACF594F695,
	SplineHelpers_Bezier3_mB25BD7261EDA22A9FB162738403E6DC66F70F326,
	SplineHelpers_BezierTangent3_m0CAB33B99E4DD03F36C592C5A95BCDC52C16BF27,
	SplineHelpers_BezierTangentWeights3_m2513B683B43808D72E1F58CFC79DBF1AEBB07A39,
	SplineHelpers_Bezier1_mAA7872DF66FA529E419AE4B19F25BDD9B28A7041,
	SplineHelpers_BezierTangent1_m41F1633A4094E5701EB543B445C8C1051CC4AA21,
	SplineHelpers_ComputeSmoothControlPoints_mF56B274A09DF5E4E77BC1BD1903C423FE9F1391A,
	SplineHelpers_ComputeSmoothControlPointsLooped_m8B1901AC903B71584D7A4F381F723F2DF41D319F,
	UnityVectorExtensions_IsNaN_m9E064699098E04ADD8B174395C1902E85BBCC179,
	UnityVectorExtensions_IsNaN_mAED27A1EFF752377901140C13A7B586561F23745,
	UnityVectorExtensions_ClosestPointOnSegment_m5AF0D62D87EF8EF307260D87EA85BE2A4C35B85D,
	UnityVectorExtensions_ClosestPointOnSegment_m53E511FE9498F8B6FE6A0A001F2EE476BF4235A5,
	UnityVectorExtensions_ProjectOntoPlane_m7B4042CE802D9E43593F9290EBCFFC1E1F1568A6,
	UnityVectorExtensions_SquareNormalize_mA45A9518904E5EF647E7D598B9ADA28EBF5D8E03,
	UnityVectorExtensions_FindIntersection_m679EF9DB24788CA733A9B241B305BF94A1E9ACC8,
	UnityVectorExtensions_Cross_m4ABE9CEBFA9687AB7A79F2287ABA20A011A514C0,
	UnityVectorExtensions_Abs_m0091B636E0155F99A8DA16B61C9372C03BB67EDC,
	UnityVectorExtensions_Abs_m00E8851E28863473A992945FDA86B4CA0F388D3E,
	UnityVectorExtensions_IsUniform_mD55546C76B4A999CE446261E535AF27FC7AD76AB,
	UnityVectorExtensions_IsUniform_mAC18FF2205B1497324CFF4EF53ACF5D2D64A89E5,
	UnityVectorExtensions_AlmostZero_mDE7F8E130BF5949DFF14AE437C47F086F3E05652,
	UnityVectorExtensions_ConservativeSetPositionAndRotation_mC29BA282577CCD7F3BAE25E82115003BE4E0B036,
	UnityVectorExtensions_Angle_mFA715DF39B2BA00E0236B1DD527A53CE159A4C8C,
	UnityVectorExtensions_SignedAngle_mA8EE238FB571BF476038E19AAF311CB42605282D,
	UnityVectorExtensions_SafeFromToRotation_m911F765BB02BF6A73073D9FEF708ACED0D55E564,
	UnityVectorExtensions_SlerpWithReferenceUp_m7F71658D673D705E004E2C256CBF33911519A1EC,
	UnityQuaternionExtensions_SlerpWithReferenceUp_m8D94F55CE71358BD2A6B38511E97BAB9EDC55464,
	UnityQuaternionExtensions_Normalized_m62143839CCE5FA1B02E7953C1ABBB217CD9465FC,
	UnityQuaternionExtensions_GetCameraRotationToTarget_mFD825219F752B2C979529CDCD655530685428FE5,
	UnityQuaternionExtensions_ApplyCameraRotation_m608B85CD86C6BE2FDD571446FA8CA596142C437C,
	UnityRectExtensions_Inflated_mF5A4FB7F7E25389F1CCB0B0F77C550BECC5ED031,
};
extern void ShapeCache_Invalidate_m5D7E303E5E807F2388F06AD1054E5CCD8D36D6C3_AdjustorThunk (void);
extern void ShapeCache_ValidateCache_mD71A58F6BBBAC79F5BB6560704C33935C77A76DD_AdjustorThunk (void);
extern void ShapeCache_IsValid_m2AA4B5B68785B4D264FB2D8DECD0163D6FAAC0D9_AdjustorThunk (void);
extern void ShapeCache_CalculateDeltaTransformationMatrix_m74CA864B0DDA47E56B35B3CA1380C35FEA603784_AdjustorThunk (void);
extern void Orbit__ctor_mEB696AB489A6AE5AB5E7D9A366CC741BD8CA23A1_AdjustorThunk (void);
extern void Waypoint_get_AsVector4_mA3935DAA3EBC69BEF2DDD1AA4DA077A0B520FA97_AdjustorThunk (void);
extern void ParentHash__ctor_m6CD157CE916B29AC191604A0283607464CCC0DDD_AdjustorThunk (void);
extern void FovCache_UpdateCache_m3462592E7672B43BEB32686E0F62B7C17F0E2999_AdjustorThunk (void);
extern void FovCache_ScreenToFOV_m84AEDE8D18A7CE6A911AB93E622316E126980056_AdjustorThunk (void);
extern void Heading__ctor_m8BA2E53862E9957B1942EF8A55E5C8284ACDAAAB_AdjustorThunk (void);
extern void AutoDolly__ctor_m8DEA29EE4AE5C67F12B07FB0C51EEC0810FDDF20_AdjustorThunk (void);
extern void AxisState__ctor_m09348C6ABBA887484BF7D3961D4FB582C0E5A4F6_AdjustorThunk (void);
extern void AxisState_Validate_m1245D61F6D9A031C27F75F4B49E78A52AA91BDE5_AdjustorThunk (void);
extern void AxisState_Reset_m329065EBC9963460CD7733144EC5F47D107967C9_AdjustorThunk (void);
extern void AxisState_SetInputAxisProvider_m9FBC0D9C885EDF31C4FFDA8A70029C5FC9089C85_AdjustorThunk (void);
extern void AxisState_get_HasInputProvider_mD82DACE6E188BCFE1B0B5FCB1328BF8FA738B091_AdjustorThunk (void);
extern void AxisState_Update_mE86F039B78105160E5C13153B456E3A988AF28B4_AdjustorThunk (void);
extern void AxisState_ClampValue_m2985D75E8FF57E3F88BF31B24CC719511507837F_AdjustorThunk (void);
extern void AxisState_MaxSpeedUpdate_m59BC1A91869A0D4A07E53DA4ED4172D5FBBF1DBD_AdjustorThunk (void);
extern void AxisState_GetMaxSpeed_m323DC3125D2C40B79B0C041CBE7F5F126329E489_AdjustorThunk (void);
extern void AxisState_get_ValueRangeLocked_m25A67A9600BCC5AFD35CA1A2C57AE0CFCB76E6B1_AdjustorThunk (void);
extern void AxisState_set_ValueRangeLocked_m367AD65F7E97A0DFF0DE1CA0C74AEEBCCC36D000_AdjustorThunk (void);
extern void AxisState_get_HasRecentering_m24F7A4CEF751588924C04AAB32BD1B59389BA4DC_AdjustorThunk (void);
extern void AxisState_set_HasRecentering_m978B18A62A74813CC75078114997E708B6877D85_AdjustorThunk (void);
extern void Recentering__ctor_mD885C396DC27C43D79A1FAA42F5ADD7D05CF2476_AdjustorThunk (void);
extern void Recentering_Validate_m3F5EE15AE52BB8FF2B69E3963851CEE2600340D3_AdjustorThunk (void);
extern void Recentering_CopyStateFrom_m1DB1F919E2F17C4913D1F2605E71630004138D89_AdjustorThunk (void);
extern void Recentering_CancelRecentering_mB79FB4BE6A929EA524224E11C885AFBA1C212D90_AdjustorThunk (void);
extern void Recentering_RecenterNow_m0A012C8E8ABA1B3D00765C8C0FDC3A96C3DB102C_AdjustorThunk (void);
extern void Recentering_DoRecentering_m7B1730622484A958AF9FD87F2056A388D96EA01A_AdjustorThunk (void);
extern void Recentering_LegacyUpgrade_m17A3ED97851377053B2385331ED85BE3DA3D4D7D_AdjustorThunk (void);
extern void CameraState_get_HasLookAt_m2581CDE02E0998E65DF1AA58B170AAB84CBFD0AC_AdjustorThunk (void);
extern void CameraState_get_CorrectedPosition_m2F96F0F6D3AE57BCEDE566FCE49D1488CA057089_AdjustorThunk (void);
extern void CameraState_get_CorrectedOrientation_m04987B71E708B14A28973FFF81645C8834FD04E8_AdjustorThunk (void);
extern void CameraState_get_FinalPosition_m4D482D1F3E008068C2151FC24FD85CB6F603AE12_AdjustorThunk (void);
extern void CameraState_get_FinalOrientation_m65D23E9A3C9264408AB177483C74FD609EFAB4B3_AdjustorThunk (void);
extern void CameraState_get_NumCustomBlendables_mA7FC428A3F135FA88769EC45E2C5521F2D1169DB_AdjustorThunk (void);
extern void CameraState_set_NumCustomBlendables_m599C74DAA99E17F8B5EF87CFD0A6238A81D05AD3_AdjustorThunk (void);
extern void CameraState_GetCustomBlendable_mE19B33F6CEC1B42ACAEB34A0601E48A80577498E_AdjustorThunk (void);
extern void CameraState_FindCustomBlendable_m141410A5E7FF4B985E2D3979D72BF80F398DE57C_AdjustorThunk (void);
extern void CameraState_AddCustomBlendable_m1DA24CB5A397752C33B6A1773CFF38F02505AD3C_AdjustorThunk (void);
extern void CameraState_InterpolatePosition_m0754A646434C49674356B584F9BDBB67B0D4F707_AdjustorThunk (void);
extern void CustomBlendable__ctor_mF38BF574AF05E415A01A2A46E506DE6B5086B303_AdjustorThunk (void);
extern void CinemachineBlendDefinition_get_BlendTime_m05485E3F00A40AD789C1A1C457153C8EEF13EF45_AdjustorThunk (void);
extern void CinemachineBlendDefinition__ctor_m24EFAC96EEDA53F43590F285C0B637771E6C947D_AdjustorThunk (void);
extern void CinemachineBlendDefinition_CreateStandardCurves_mC0C71CFA64286A2ED1DC190CFC2C9FAE37E9E2CF_AdjustorThunk (void);
extern void CinemachineBlendDefinition_get_BlendCurve_mC33A778E56621A57C341B1882DE76D85CEBF82C0_AdjustorThunk (void);
extern void AxisBase_Validate_mD6017BA404C55814A0E55DD7D036FA666EE038CB_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_Validate_mC2EFECCBF1C729D83650ECCB9EC02BA70A3692FC_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_Update_m8AFFF82834DDE9F93045956D8A9EEEA933766FD4_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_Update_m24BE353BA761E2D8A9EE55CF6274D17C31EB3F76_AdjustorThunk (void);
extern void CinemachineInputAxisDriver_ClampValue_mA2A92688571EA4584213869F0C7CA9A7699B2747_AdjustorThunk (void);
extern void AspectStretcher_get_Aspect_m506D4C16F8E6AE36198ACCAE2C4AFEED14552272_AdjustorThunk (void);
extern void AspectStretcher__ctor_m84A17187C183823205C2C732202DCBBAA2350852_AdjustorThunk (void);
extern void AspectStretcher_Stretch_m1265459BD5A34090D4174D1D69509C2C53D36A92_AdjustorThunk (void);
extern void AspectStretcher_Unstretch_mA27250710163BEFDB2E5F0E074F41B878A7AB08E_AdjustorThunk (void);
extern void PolygonSolution_StateChanged_mB1E13BE21168ACB0EA459D4757A52799724CD7A0_AdjustorThunk (void);
extern void PolygonSolution_get_IsNull_m55D141117498A201C12B86647EB9B17E7BDDA801_AdjustorThunk (void);
extern void LensSettings_get_Orthographic_m198D9052494017EEE832066A64F81ADD2B75C17D_AdjustorThunk (void);
extern void LensSettings_set_Orthographic_mDD2CDEBC91693B4A25E92DB0DEDE8698DD115EB6_AdjustorThunk (void);
extern void LensSettings_get_SensorSize_m1D1F2A7226C400F0062842864C67608E2DCBBD4B_AdjustorThunk (void);
extern void LensSettings_set_SensorSize_mD43BCB83C6FBE95F48DAD8993E8DA53927F5CA04_AdjustorThunk (void);
extern void LensSettings_get_Aspect_m47C88E8BFBCFA1394AF0259DF528CCC4786A2555_AdjustorThunk (void);
extern void LensSettings_get_IsPhysicalCamera_m6AD402DF51FEFD48DC4813C53C10D034B16F9386_AdjustorThunk (void);
extern void LensSettings_set_IsPhysicalCamera_m818868B1BD841C9DBD8B9DCBBAE69A14D099D5E9_AdjustorThunk (void);
extern void LensSettings_SnapshotCameraReadOnlyProperties_mBFE20278097AE1A1D51AC775347F47AB84E32F4D_AdjustorThunk (void);
extern void LensSettings_SnapshotCameraReadOnlyProperties_mEF4FCF04C9AE103AAFE9CDB4531154896BFD660E_AdjustorThunk (void);
extern void LensSettings__ctor_mA347110802F0FE6F33CD4D92461CADF6B1B93F59_AdjustorThunk (void);
extern void LensSettings_Validate_m2C9ABCED7FE02F0C7B575CFADB77493DEEA03B01_AdjustorThunk (void);
extern void NoiseParams_GetValueAt_mDDE2123C634874F162FAFAD1270E443FD9C13F7B_AdjustorThunk (void);
extern void TransformNoiseParams_GetValueAt_m541D9F21055D8F6F727E24C5A3D2C26B7044D4F0_AdjustorThunk (void);
extern void TimeRange_get_IsEmpty_mB51E1F97CF61DD08C502385B2AFC72C6A7F68BCB_AdjustorThunk (void);
extern void TimeRange_Contains_mF974E5E36DD20BD63B5B09564B09800AB30CF6B6_AdjustorThunk (void);
extern void TimeRange_Include_mC1E0A1214F4C3A6FC7F15728A3EB9C398B96F1B2_AdjustorThunk (void);
extern void ActionSettings__ctor_m1713D8512FDCD108FF5F41EE681F79311452DB49_AdjustorThunk (void);
extern void ActionSettings_Invoke_mF55BAA376882270B52604D6E67EC1EF1F15F8A01_AdjustorThunk (void);
extern void ImpulseReaction_ReSeed_m0E5974B5A368FE95D4137FB5A87BF71699535DB5_AdjustorThunk (void);
extern void ImpulseReaction_GetReaction_mF1A9A410B1F05368B416656512188311E44E8CC3_AdjustorThunk (void);
extern void EnvelopeDefinition_get_Duration_m3CF863DC6B71344BE096AD9CBFC6C86AD2FF634C_AdjustorThunk (void);
extern void EnvelopeDefinition_GetValueAt_mAF39D22894C1B21FDA3A5D7C50AF4AEB393BEE6D_AdjustorThunk (void);
extern void EnvelopeDefinition_ChangeStopTime_mFE038CADEFEBC54B1262B69139C2C8C209CE1196_AdjustorThunk (void);
extern void EnvelopeDefinition_Clear_m70B8BFD4F76297739F4D6EC3044D46832196D4EF_AdjustorThunk (void);
extern void EnvelopeDefinition_Validate_m2DE55071F0E90DAF09281B7735FD96A93D278C37_AdjustorThunk (void);
extern void DoublePoint__ctor_m87B0A4A8419F7E7608DF989FFBD0E92295A0A72F_AdjustorThunk (void);
extern void DoublePoint__ctor_m1DD41CFCE7D02EDD548BC15B67AF3A5FF8919DAB_AdjustorThunk (void);
extern void DoublePoint__ctor_mC072F91863E140D4884B37BD4072B89E6ACAD877_AdjustorThunk (void);
extern void Int128__ctor_m99DFFF77A5A8617353DAB1869A052B84E772B858_AdjustorThunk (void);
extern void Int128__ctor_m8C6D7B59F2A217F10B2117D900C9890B7BA677B5_AdjustorThunk (void);
extern void Int128__ctor_mC6B960C84486E99631A6B965A0D0357EFC13756D_AdjustorThunk (void);
extern void Int128_IsNegative_mC7705DD6A31CD27D6F86081FB8E63A61FC51DBEE_AdjustorThunk (void);
extern void Int128_Equals_m3A1780C1A972388F69135020EA85391611CF4777_AdjustorThunk (void);
extern void Int128_GetHashCode_m03339EA88F9C7443DD7F84339389F35A70703484_AdjustorThunk (void);
extern void IntPoint__ctor_mD85ED4713EDE2C713D37C9CD776B791F21C048E1_AdjustorThunk (void);
extern void IntPoint__ctor_mA161A7BD0009BE271681C45C59B291FDB224616E_AdjustorThunk (void);
extern void IntPoint__ctor_m64D954CC64AF3DEB13E5E64F544E46789983BDEB_AdjustorThunk (void);
extern void IntPoint_Equals_m6E23677EC9306F8D66DD717865D49A3E9A9BAB5E_AdjustorThunk (void);
extern void IntPoint_GetHashCode_m19A1D74A3CD370690B8C8751C6762CDB47736788_AdjustorThunk (void);
extern void IntRect__ctor_mE1409649D5D928E097BE8C8B4B771DC881052464_AdjustorThunk (void);
extern void IntRect__ctor_m40FEB4C1F3B4B8123E190855A65E09E81D766961_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[99] = 
{
	{ 0x060000BE, ShapeCache_Invalidate_m5D7E303E5E807F2388F06AD1054E5CCD8D36D6C3_AdjustorThunk },
	{ 0x060000BF, ShapeCache_ValidateCache_mD71A58F6BBBAC79F5BB6560704C33935C77A76DD_AdjustorThunk },
	{ 0x060000C0, ShapeCache_IsValid_m2AA4B5B68785B4D264FB2D8DECD0163D6FAAC0D9_AdjustorThunk },
	{ 0x060000C1, ShapeCache_CalculateDeltaTransformationMatrix_m74CA864B0DDA47E56B35B3CA1380C35FEA603784_AdjustorThunk },
	{ 0x060000FC, Orbit__ctor_mEB696AB489A6AE5AB5E7D9A366CC741BD8CA23A1_AdjustorThunk },
	{ 0x06000139, Waypoint_get_AsVector4_mA3935DAA3EBC69BEF2DDD1AA4DA077A0B520FA97_AdjustorThunk },
	{ 0x06000158, ParentHash__ctor_m6CD157CE916B29AC191604A0283607464CCC0DDD_AdjustorThunk },
	{ 0x060001D7, FovCache_UpdateCache_m3462592E7672B43BEB32686E0F62B7C17F0E2999_AdjustorThunk },
	{ 0x060001D8, FovCache_ScreenToFOV_m84AEDE8D18A7CE6A911AB93E622316E126980056_AdjustorThunk },
	{ 0x06000213, Heading__ctor_m8BA2E53862E9957B1942EF8A55E5C8284ACDAAAB_AdjustorThunk },
	{ 0x06000235, AutoDolly__ctor_m8DEA29EE4AE5C67F12B07FB0C51EEC0810FDDF20_AdjustorThunk },
	{ 0x06000248, AxisState__ctor_m09348C6ABBA887484BF7D3961D4FB582C0E5A4F6_AdjustorThunk },
	{ 0x06000249, AxisState_Validate_m1245D61F6D9A031C27F75F4B49E78A52AA91BDE5_AdjustorThunk },
	{ 0x0600024A, AxisState_Reset_m329065EBC9963460CD7733144EC5F47D107967C9_AdjustorThunk },
	{ 0x0600024B, AxisState_SetInputAxisProvider_m9FBC0D9C885EDF31C4FFDA8A70029C5FC9089C85_AdjustorThunk },
	{ 0x0600024C, AxisState_get_HasInputProvider_mD82DACE6E188BCFE1B0B5FCB1328BF8FA738B091_AdjustorThunk },
	{ 0x0600024D, AxisState_Update_mE86F039B78105160E5C13153B456E3A988AF28B4_AdjustorThunk },
	{ 0x0600024E, AxisState_ClampValue_m2985D75E8FF57E3F88BF31B24CC719511507837F_AdjustorThunk },
	{ 0x0600024F, AxisState_MaxSpeedUpdate_m59BC1A91869A0D4A07E53DA4ED4172D5FBBF1DBD_AdjustorThunk },
	{ 0x06000250, AxisState_GetMaxSpeed_m323DC3125D2C40B79B0C041CBE7F5F126329E489_AdjustorThunk },
	{ 0x06000251, AxisState_get_ValueRangeLocked_m25A67A9600BCC5AFD35CA1A2C57AE0CFCB76E6B1_AdjustorThunk },
	{ 0x06000252, AxisState_set_ValueRangeLocked_m367AD65F7E97A0DFF0DE1CA0C74AEEBCCC36D000_AdjustorThunk },
	{ 0x06000253, AxisState_get_HasRecentering_m24F7A4CEF751588924C04AAB32BD1B59389BA4DC_AdjustorThunk },
	{ 0x06000254, AxisState_set_HasRecentering_m978B18A62A74813CC75078114997E708B6877D85_AdjustorThunk },
	{ 0x06000256, Recentering__ctor_mD885C396DC27C43D79A1FAA42F5ADD7D05CF2476_AdjustorThunk },
	{ 0x06000257, Recentering_Validate_m3F5EE15AE52BB8FF2B69E3963851CEE2600340D3_AdjustorThunk },
	{ 0x06000258, Recentering_CopyStateFrom_m1DB1F919E2F17C4913D1F2605E71630004138D89_AdjustorThunk },
	{ 0x06000259, Recentering_CancelRecentering_mB79FB4BE6A929EA524224E11C885AFBA1C212D90_AdjustorThunk },
	{ 0x0600025A, Recentering_RecenterNow_m0A012C8E8ABA1B3D00765C8C0FDC3A96C3DB102C_AdjustorThunk },
	{ 0x0600025B, Recentering_DoRecentering_m7B1730622484A958AF9FD87F2056A388D96EA01A_AdjustorThunk },
	{ 0x0600025C, Recentering_LegacyUpgrade_m17A3ED97851377053B2385331ED85BE3DA3D4D7D_AdjustorThunk },
	{ 0x0600025D, CameraState_get_HasLookAt_m2581CDE02E0998E65DF1AA58B170AAB84CBFD0AC_AdjustorThunk },
	{ 0x0600025E, CameraState_get_CorrectedPosition_m2F96F0F6D3AE57BCEDE566FCE49D1488CA057089_AdjustorThunk },
	{ 0x0600025F, CameraState_get_CorrectedOrientation_m04987B71E708B14A28973FFF81645C8834FD04E8_AdjustorThunk },
	{ 0x06000260, CameraState_get_FinalPosition_m4D482D1F3E008068C2151FC24FD85CB6F603AE12_AdjustorThunk },
	{ 0x06000261, CameraState_get_FinalOrientation_m65D23E9A3C9264408AB177483C74FD609EFAB4B3_AdjustorThunk },
	{ 0x06000263, CameraState_get_NumCustomBlendables_mA7FC428A3F135FA88769EC45E2C5521F2D1169DB_AdjustorThunk },
	{ 0x06000264, CameraState_set_NumCustomBlendables_m599C74DAA99E17F8B5EF87CFD0A6238A81D05AD3_AdjustorThunk },
	{ 0x06000265, CameraState_GetCustomBlendable_mE19B33F6CEC1B42ACAEB34A0601E48A80577498E_AdjustorThunk },
	{ 0x06000266, CameraState_FindCustomBlendable_m141410A5E7FF4B985E2D3979D72BF80F398DE57C_AdjustorThunk },
	{ 0x06000267, CameraState_AddCustomBlendable_m1DA24CB5A397752C33B6A1773CFF38F02505AD3C_AdjustorThunk },
	{ 0x0600026C, CameraState_InterpolatePosition_m0754A646434C49674356B584F9BDBB67B0D4F707_AdjustorThunk },
	{ 0x0600026E, CustomBlendable__ctor_mF38BF574AF05E415A01A2A46E506DE6B5086B303_AdjustorThunk },
	{ 0x06000277, CinemachineBlendDefinition_get_BlendTime_m05485E3F00A40AD789C1A1C457153C8EEF13EF45_AdjustorThunk },
	{ 0x06000278, CinemachineBlendDefinition__ctor_m24EFAC96EEDA53F43590F285C0B637771E6C947D_AdjustorThunk },
	{ 0x06000279, CinemachineBlendDefinition_CreateStandardCurves_mC0C71CFA64286A2ED1DC190CFC2C9FAE37E9E2CF_AdjustorThunk },
	{ 0x0600027A, CinemachineBlendDefinition_get_BlendCurve_mC33A778E56621A57C341B1882DE76D85CEBF82C0_AdjustorThunk },
	{ 0x060002F7, AxisBase_Validate_mD6017BA404C55814A0E55DD7D036FA666EE038CB_AdjustorThunk },
	{ 0x060002F8, CinemachineInputAxisDriver_Validate_mC2EFECCBF1C729D83650ECCB9EC02BA70A3692FC_AdjustorThunk },
	{ 0x060002F9, CinemachineInputAxisDriver_Update_m8AFFF82834DDE9F93045956D8A9EEEA933766FD4_AdjustorThunk },
	{ 0x060002FA, CinemachineInputAxisDriver_Update_m24BE353BA761E2D8A9EE55CF6274D17C31EB3F76_AdjustorThunk },
	{ 0x060002FB, CinemachineInputAxisDriver_ClampValue_mA2A92688571EA4584213869F0C7CA9A7699B2747_AdjustorThunk },
	{ 0x06000382, AspectStretcher_get_Aspect_m506D4C16F8E6AE36198ACCAE2C4AFEED14552272_AdjustorThunk },
	{ 0x06000383, AspectStretcher__ctor_m84A17187C183823205C2C732202DCBBAA2350852_AdjustorThunk },
	{ 0x06000384, AspectStretcher_Stretch_m1265459BD5A34090D4174D1D69509C2C53D36A92_AdjustorThunk },
	{ 0x06000385, AspectStretcher_Unstretch_mA27250710163BEFDB2E5F0E074F41B878A7AB08E_AdjustorThunk },
	{ 0x06000386, PolygonSolution_StateChanged_mB1E13BE21168ACB0EA459D4757A52799724CD7A0_AdjustorThunk },
	{ 0x06000387, PolygonSolution_get_IsNull_m55D141117498A201C12B86647EB9B17E7BDDA801_AdjustorThunk },
	{ 0x06000399, LensSettings_get_Orthographic_m198D9052494017EEE832066A64F81ADD2B75C17D_AdjustorThunk },
	{ 0x0600039A, LensSettings_set_Orthographic_mDD2CDEBC91693B4A25E92DB0DEDE8698DD115EB6_AdjustorThunk },
	{ 0x0600039B, LensSettings_get_SensorSize_m1D1F2A7226C400F0062842864C67608E2DCBBD4B_AdjustorThunk },
	{ 0x0600039C, LensSettings_set_SensorSize_mD43BCB83C6FBE95F48DAD8993E8DA53927F5CA04_AdjustorThunk },
	{ 0x0600039D, LensSettings_get_Aspect_m47C88E8BFBCFA1394AF0259DF528CCC4786A2555_AdjustorThunk },
	{ 0x0600039E, LensSettings_get_IsPhysicalCamera_m6AD402DF51FEFD48DC4813C53C10D034B16F9386_AdjustorThunk },
	{ 0x0600039F, LensSettings_set_IsPhysicalCamera_m818868B1BD841C9DBD8B9DCBBAE69A14D099D5E9_AdjustorThunk },
	{ 0x060003A1, LensSettings_SnapshotCameraReadOnlyProperties_mBFE20278097AE1A1D51AC775347F47AB84E32F4D_AdjustorThunk },
	{ 0x060003A2, LensSettings_SnapshotCameraReadOnlyProperties_mEF4FCF04C9AE103AAFE9CDB4531154896BFD660E_AdjustorThunk },
	{ 0x060003A3, LensSettings__ctor_mA347110802F0FE6F33CD4D92461CADF6B1B93F59_AdjustorThunk },
	{ 0x060003A5, LensSettings_Validate_m2C9ABCED7FE02F0C7B575CFADB77493DEEA03B01_AdjustorThunk },
	{ 0x060003AB, NoiseParams_GetValueAt_mDDE2123C634874F162FAFAD1270E443FD9C13F7B_AdjustorThunk },
	{ 0x060003AC, TransformNoiseParams_GetValueAt_m541D9F21055D8F6F727E24C5A3D2C26B7044D4F0_AdjustorThunk },
	{ 0x060003D0, TimeRange_get_IsEmpty_mB51E1F97CF61DD08C502385B2AFC72C6A7F68BCB_AdjustorThunk },
	{ 0x060003D1, TimeRange_Contains_mF974E5E36DD20BD63B5B09564B09800AB30CF6B6_AdjustorThunk },
	{ 0x060003D3, TimeRange_Include_mC1E0A1214F4C3A6FC7F15728A3EB9C398B96F1B2_AdjustorThunk },
	{ 0x060003F2, ActionSettings__ctor_m1713D8512FDCD108FF5F41EE681F79311452DB49_AdjustorThunk },
	{ 0x060003F3, ActionSettings_Invoke_mF55BAA376882270B52604D6E67EC1EF1F15F8A01_AdjustorThunk },
	{ 0x0600041C, ImpulseReaction_ReSeed_m0E5974B5A368FE95D4137FB5A87BF71699535DB5_AdjustorThunk },
	{ 0x0600041D, ImpulseReaction_GetReaction_mF1A9A410B1F05368B416656512188311E44E8CC3_AdjustorThunk },
	{ 0x0600042A, EnvelopeDefinition_get_Duration_m3CF863DC6B71344BE096AD9CBFC6C86AD2FF634C_AdjustorThunk },
	{ 0x0600042B, EnvelopeDefinition_GetValueAt_mAF39D22894C1B21FDA3A5D7C50AF4AEB393BEE6D_AdjustorThunk },
	{ 0x0600042C, EnvelopeDefinition_ChangeStopTime_mFE038CADEFEBC54B1262B69139C2C8C209CE1196_AdjustorThunk },
	{ 0x0600042D, EnvelopeDefinition_Clear_m70B8BFD4F76297739F4D6EC3044D46832196D4EF_AdjustorThunk },
	{ 0x0600042E, EnvelopeDefinition_Validate_m2DE55071F0E90DAF09281B7735FD96A93D278C37_AdjustorThunk },
	{ 0x06000444, DoublePoint__ctor_m87B0A4A8419F7E7608DF989FFBD0E92295A0A72F_AdjustorThunk },
	{ 0x06000445, DoublePoint__ctor_m1DD41CFCE7D02EDD548BC15B67AF3A5FF8919DAB_AdjustorThunk },
	{ 0x06000446, DoublePoint__ctor_mC072F91863E140D4884B37BD4072B89E6ACAD877_AdjustorThunk },
	{ 0x06000457, Int128__ctor_m99DFFF77A5A8617353DAB1869A052B84E772B858_AdjustorThunk },
	{ 0x06000458, Int128__ctor_m8C6D7B59F2A217F10B2117D900C9890B7BA677B5_AdjustorThunk },
	{ 0x06000459, Int128__ctor_mC6B960C84486E99631A6B965A0D0357EFC13756D_AdjustorThunk },
	{ 0x0600045A, Int128_IsNegative_mC7705DD6A31CD27D6F86081FB8E63A61FC51DBEE_AdjustorThunk },
	{ 0x0600045D, Int128_Equals_m3A1780C1A972388F69135020EA85391611CF4777_AdjustorThunk },
	{ 0x0600045E, Int128_GetHashCode_m03339EA88F9C7443DD7F84339389F35A70703484_AdjustorThunk },
	{ 0x06000466, IntPoint__ctor_mD85ED4713EDE2C713D37C9CD776B791F21C048E1_AdjustorThunk },
	{ 0x06000467, IntPoint__ctor_mA161A7BD0009BE271681C45C59B291FDB224616E_AdjustorThunk },
	{ 0x06000468, IntPoint__ctor_m64D954CC64AF3DEB13E5E64F544E46789983BDEB_AdjustorThunk },
	{ 0x0600046B, IntPoint_Equals_m6E23677EC9306F8D66DD717865D49A3E9A9BAB5E_AdjustorThunk },
	{ 0x0600046C, IntPoint_GetHashCode_m19A1D74A3CD370690B8C8751C6762CDB47736788_AdjustorThunk },
	{ 0x0600046D, IntRect__ctor_mE1409649D5D928E097BE8C8B4B771DC881052464_AdjustorThunk },
	{ 0x0600046E, IntRect__ctor_m40FEB4C1F3B4B8123E190855A65E09E81D766961_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1389] = 
{
	997,
	6329,
	6329,
	6329,
	1474,
	997,
	6329,
	6329,
	4579,
	6329,
	5045,
	2909,
	1534,
	4580,
	6329,
	2879,
	6204,
	2226,
	4479,
	2258,
	2883,
	6329,
	6108,
	6329,
	1303,
	6329,
	6320,
	5141,
	6329,
	6329,
	1153,
	5040,
	2322,
	2322,
	997,
	6329,
	6204,
	6329,
	6204,
	5040,
	1708,
	6109,
	6204,
	5040,
	6204,
	5040,
	2898,
	2971,
	1532,
	2972,
	6329,
	6329,
	6329,
	6329,
	6204,
	6108,
	6329,
	6329,
	6329,
	5087,
	6329,
	0,
	0,
	0,
	6204,
	6204,
	5040,
	9523,
	9375,
	9492,
	6320,
	6329,
	6329,
	2926,
	5082,
	6329,
	6329,
	6329,
	6204,
	6329,
	6329,
	4575,
	2659,
	6204,
	9160,
	3597,
	6108,
	6204,
	5040,
	4179,
	268,
	5016,
	5087,
	5087,
	2349,
	1708,
	6109,
	4946,
	6204,
	1894,
	4935,
	6329,
	9569,
	6329,
	6329,
	6108,
	6329,
	5016,
	6329,
	6108,
	6204,
	6329,
	6204,
	6204,
	6204,
	5040,
	6109,
	1708,
	6204,
	5040,
	6204,
	5040,
	2898,
	2971,
	2972,
	6329,
	6329,
	6329,
	6329,
	6108,
	6204,
	6204,
	6329,
	6329,
	6329,
	4494,
	4479,
	1894,
	1532,
	6329,
	9569,
	6329,
	2083,
	3597,
	3597,
	4579,
	6329,
	6329,
	6204,
	6259,
	2898,
	997,
	2314,
	891,
	83,
	549,
	884,
	7936,
	2324,
	3505,
	8949,
	6329,
	9569,
	5141,
	2287,
	5087,
	5087,
	6329,
	3597,
	4579,
	6329,
	4945,
	6108,
	6259,
	997,
	6329,
	6108,
	4683,
	4679,
	6329,
	6329,
	6329,
	3644,
	997,
	2271,
	6329,
	6329,
	6329,
	6329,
	6329,
	262,
	509,
	6329,
	6329,
	6329,
	6329,
	5087,
	6329,
	6109,
	6204,
	5040,
	6204,
	5040,
	2972,
	6329,
	6329,
	6259,
	997,
	6329,
	6329,
	6329,
	4472,
	6108,
	9523,
	6329,
	6329,
	6329,
	6329,
	6329,
	6108,
	4945,
	6109,
	6204,
	5040,
	6204,
	5040,
	1708,
	2898,
	2971,
	2972,
	1532,
	6108,
	2288,
	4583,
	6329,
	6329,
	4479,
	6108,
	4466,
	1329,
	6329,
	6259,
	1892,
	4682,
	6329,
	6329,
	6329,
	2283,
	2283,
	4574,
	1545,
	2934,
	2879,
	1258,
	409,
	4479,
	2879,
	5040,
	1258,
	5040,
	4578,
	2659,
	4579,
	2889,
	6204,
	5040,
	6109,
	6204,
	5040,
	6204,
	5040,
	2898,
	2971,
	6329,
	6329,
	6329,
	1708,
	6204,
	6329,
	6329,
	1532,
	2972,
	6329,
	6259,
	6259,
	6108,
	6329,
	6329,
	6178,
	1333,
	4682,
	4682,
	4528,
	1317,
	9219,
	6329,
	6329,
	6329,
	6259,
	6259,
	6108,
	6178,
	6329,
	6329,
	6329,
	6329,
	1333,
	4682,
	4682,
	4528,
	9219,
	6329,
	6322,
	9392,
	6204,
	6204,
	5040,
	1708,
	6109,
	6204,
	5040,
	6204,
	5040,
	2898,
	2971,
	1532,
	2972,
	6329,
	6329,
	6329,
	6329,
	6204,
	6108,
	6204,
	8446,
	1973,
	6329,
	6329,
	6329,
	6204,
	1973,
	1894,
	6329,
	2604,
	997,
	6329,
	4945,
	6204,
	5040,
	2221,
	5040,
	6329,
	2889,
	9375,
	9569,
	6329,
	6329,
	0,
	0,
	0,
	0,
	0,
	0,
	6329,
	6329,
	6204,
	6107,
	4944,
	6106,
	4943,
	6108,
	1526,
	5040,
	4201,
	3249,
	3252,
	6108,
	3570,
	7658,
	6329,
	6329,
	6320,
	6107,
	6106,
	6222,
	6329,
	6329,
	6329,
	960,
	6329,
	6109,
	6204,
	5040,
	6204,
	5040,
	6259,
	2972,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	4479,
	6329,
	6204,
	6204,
	4472,
	0,
	0,
	0,
	6329,
	9375,
	1892,
	2898,
	2971,
	5141,
	1532,
	6108,
	6329,
	6329,
	2879,
	1258,
	409,
	4479,
	2879,
	5040,
	1258,
	5040,
	9569,
	6329,
	1994,
	3597,
	6329,
	6329,
	6329,
	6108,
	6178,
	6259,
	2352,
	2898,
	2352,
	1373,
	8578,
	501,
	420,
	6329,
	6108,
	6178,
	2352,
	6329,
	6329,
	6329,
	6108,
	6178,
	6320,
	5141,
	1357,
	2898,
	2971,
	6259,
	2352,
	2352,
	6229,
	5061,
	6229,
	5061,
	126,
	513,
	6329,
	959,
	880,
	6229,
	5061,
	6229,
	5061,
	6329,
	6108,
	6178,
	6108,
	6320,
	5141,
	2898,
	2971,
	6259,
	535,
	1309,
	2323,
	6107,
	4944,
	6197,
	5033,
	2352,
	4582,
	2315,
	7659,
	6329,
	6329,
	6107,
	4944,
	6197,
	5033,
	6259,
	2352,
	4582,
	7660,
	6329,
	6108,
	6178,
	6259,
	2352,
	6329,
	6108,
	6178,
	2352,
	6329,
	6329,
	1335,
	417,
	6329,
	6329,
	2898,
	2971,
	535,
	2288,
	2352,
	4683,
	6108,
	2286,
	6329,
	1446,
	2879,
	1329,
	410,
	4579,
	9569,
	6329,
	1329,
	6108,
	6178,
	6329,
	6329,
	6329,
	2352,
	2352,
	6318,
	9266,
	2971,
	535,
	6108,
	5056,
	6329,
	6108,
	6178,
	6259,
	2352,
	6329,
	6108,
	6178,
	6259,
	2352,
	2262,
	6320,
	6329,
	922,
	6329,
	6108,
	4945,
	6320,
	6108,
	6178,
	6259,
	2352,
	2898,
	2971,
	2352,
	498,
	422,
	6320,
	6320,
	4683,
	4529,
	6329,
	50,
	6329,
	6329,
	2626,
	6108,
	3644,
	4580,
	1756,
	6259,
	6108,
	4945,
	6108,
	4945,
	0,
	1395,
	6329,
	4935,
	6329,
	6329,
	1381,
	1634,
	6108,
	6320,
	6222,
	6320,
	6222,
	9490,
	6178,
	5016,
	5320,
	4201,
	5164,
	7721,
	7021,
	6738,
	6728,
	421,
	9569,
	2889,
	6259,
	6108,
	6108,
	6204,
	3597,
	480,
	2972,
	6109,
	6259,
	2659,
	6329,
	6204,
	2363,
	4946,
	6204,
	5040,
	6204,
	6178,
	5016,
	6204,
	5040,
	6204,
	5040,
	6109,
	4946,
	6204,
	6108,
	6204,
	1708,
	2972,
	2972,
	1532,
	2898,
	5040,
	6204,
	5040,
	6204,
	6204,
	6178,
	5016,
	6204,
	5040,
	6204,
	5040,
	6109,
	4946,
	6204,
	6108,
	6204,
	1708,
	3859,
	2972,
	2972,
	1532,
	2898,
	1181,
	6329,
	6204,
	6204,
	6204,
	6204,
	6204,
	6320,
	6222,
	6204,
	6204,
	6320,
	6222,
	6109,
	0,
	2352,
	0,
	6108,
	0,
	535,
	2898,
	2971,
	6259,
	6108,
	6329,
	9523,
	9553,
	9553,
	6178,
	4472,
	5040,
	5040,
	6178,
	4472,
	5040,
	5040,
	5040,
	5040,
	5040,
	1458,
	1532,
	9569,
	9160,
	4201,
	3597,
	3597,
	2883,
	5040,
	4479,
	2898,
	6329,
	9569,
	2879,
	4579,
	1258,
	4579,
	2879,
	553,
	191,
	3860,
	6329,
	9569,
	6329,
	1994,
	6204,
	6329,
	6329,
	6329,
	6329,
	4945,
	1474,
	997,
	0,
	2898,
	2971,
	1153,
	6259,
	6108,
	0,
	0,
	6329,
	6329,
	6329,
	1755,
	1755,
	2272,
	0,
	0,
	0,
	4580,
	4682,
	4682,
	4528,
	0,
	0,
	0,
	886,
	4578,
	4578,
	2284,
	2319,
	2319,
	2263,
	0,
	6329,
	6108,
	6259,
	4580,
	2284,
	2284,
	5016,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	4945,
	6178,
	5016,
	5016,
	6178,
	5016,
	6259,
	1334,
	1357,
	1356,
	1334,
	1357,
	1356,
	5040,
	5040,
	6204,
	5040,
	997,
	1474,
	1153,
	6204,
	6204,
	6178,
	5016,
	2349,
	6204,
	6108,
	0,
	6204,
	1708,
	0,
	0,
	0,
	0,
	6108,
	4945,
	2972,
	0,
	1532,
	6329,
	6329,
	6329,
	6108,
	6329,
	6204,
	6329,
	6329,
	6329,
	6329,
	6329,
	4479,
	4479,
	6329,
	6329,
	2898,
	2971,
	3597,
	863,
	1891,
	6329,
	6108,
	4945,
	6108,
	4945,
	6329,
	6204,
	6204,
	6204,
	6204,
	6329,
	6329,
	5016,
	6329,
	4945,
	6329,
	9569,
	6329,
	3597,
	899,
	4483,
	6178,
	5016,
	899,
	5087,
	9223,
	9394,
	4935,
	244,
	6108,
	4670,
	7180,
	8154,
	3744,
	7941,
	1814,
	8405,
	6329,
	3597,
	6259,
	2934,
	4675,
	4675,
	3483,
	6108,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6108,
	4945,
	6318,
	5139,
	6259,
	6108,
	4945,
	9106,
	5040,
	4935,
	497,
	7824,
	6329,
	9569,
	7972,
	6259,
	1545,
	6329,
	2287,
	2320,
	9375,
	8957,
	6817,
	6613,
	9523,
	9569,
	7854,
	9569,
	0,
	0,
	0,
	0,
	6329,
	9515,
	9371,
	9489,
	9489,
	9489,
	9578,
	9489,
	9569,
	9569,
	9349,
	9217,
	6329,
	6178,
	1551,
	5300,
	1580,
	5522,
	8160,
	9581,
	1547,
	6329,
	6329,
	6108,
	3644,
	9578,
	5087,
	9569,
	9371,
	9066,
	9371,
	6329,
	9569,
	6178,
	5016,
	2620,
	1444,
	4578,
	2210,
	6329,
	6329,
	8504,
	6329,
	3597,
	3597,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	5040,
	6329,
	6329,
	5016,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	5040,
	5040,
	2275,
	2898,
	5040,
	5040,
	2275,
	2898,
	6329,
	6259,
	4579,
	1545,
	2280,
	6329,
	6329,
	6329,
	9569,
	9156,
	6204,
	2973,
	2252,
	2252,
	6329,
	2898,
	6259,
	1545,
	2898,
	6259,
	1545,
	6329,
	997,
	6329,
	6329,
	546,
	6329,
	6329,
	6329,
	9523,
	9569,
	8612,
	264,
	6259,
	6204,
	5040,
	6329,
	9572,
	6259,
	4580,
	2931,
	6329,
	6329,
	6108,
	2931,
	4580,
	548,
	6329,
	6329,
	6329,
	6329,
	2973,
	5141,
	5087,
	6329,
	2973,
	5141,
	5087,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	2373,
	5175,
	5177,
	6329,
	6204,
	6178,
	6329,
	6108,
	6178,
	6204,
	5040,
	6204,
	6204,
	6204,
	6204,
	6108,
	6108,
	4945,
	6329,
	5017,
	2847,
	5176,
	6108,
	8363,
	8363,
	3597,
	6178,
	8363,
	8363,
	8801,
	8801,
	9393,
	9018,
	8800,
	2844,
	2373,
	5177,
	8364,
	8364,
	3597,
	6178,
	956,
	5178,
	6329,
	6329,
	1994,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	6329,
	8951,
	6108,
	4945,
	2345,
	8957,
	1813,
	550,
	1171,
	7701,
	7172,
	6828,
	6329,
	6329,
	6329,
	2980,
	1031,
	2877,
	4479,
	2221,
	1140,
	1140,
	1172,
	4479,
	5040,
	5040,
	1686,
	5040,
	6329,
	9395,
	5017,
	3483,
	6108,
	6204,
	5016,
	4935,
	2883,
	5040,
	5016,
	5017,
	6108,
	4945,
	6108,
	4945,
	1109,
	1109,
	522,
	522,
	5040,
	6108,
	6329,
	1518,
	2900,
	5017,
	2883,
	1715,
	3597,
	3597,
	3597,
	5040,
	5040,
	3483,
	6329,
	2883,
	1518,
	1261,
	2229,
	4479,
	2345,
	523,
	2883,
	1906,
	1715,
	4479,
	2226,
	1715,
	4472,
	2883,
	5040,
	8761,
	8761,
	1518,
	5040,
	6329,
	961,
	5040,
	2224,
	3597,
	1711,
	1711,
	4479,
	4479,
	3571,
	5017,
	3597,
	8456,
	6108,
	6329,
	9080,
	8479,
	1506,
	5017,
	5040,
	9375,
	8957,
	4201,
	5040,
	5040,
	5040,
	5040,
	2221,
	170,
	175,
	1144,
	8470,
	8470,
	8296,
	2883,
	2883,
	2883,
	9160,
	6329,
	5040,
	6329,
	9011,
	3904,
	3904,
	8529,
	8529,
	8405,
	7740,
	7173,
	7720,
	9160,
	8526,
	8526,
	7397,
	7870,
	8542,
	7870,
	8534,
	9160,
	8106,
	9160,
	9160,
	6131,
	4972,
	6131,
	4972,
	2373,
	6329,
	9080,
	1490,
	1490,
	6329,
	8799,
	4972,
	2348,
	2348,
	1433,
	2604,
	1441,
	2604,
	5040,
	997,
	6329,
	6329,
	9375,
	7917,
	9523,
	9375,
	6329,
	2879,
	6329,
	2226,
	5040,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	2932,
	4680,
	2932,
	4525,
	2932,
	4672,
	6108,
	5141,
	6329,
	1567,
	4682,
	4682,
	6329,
	8612,
	7937,
	7937,
	7975,
	7973,
	5016,
	6178,
	6329,
	9266,
	5141,
	6329,
	6329,
	6320,
	7027,
	7027,
	6652,
	7021,
	7021,
	8004,
	8004,
	8970,
	8971,
	7940,
	7939,
	8673,
	9345,
	6830,
	8613,
	9345,
	9352,
	8970,
	8971,
	8971,
	8136,
	8614,
	7940,
	7914,
	7489,
	7466,
	9218,
	7964,
	7912,
	8591,
};
static const Il2CppTokenRangePair s_rgctxIndices[6] = 
{
	{ 0x020000E3, { 10, 8 } },
	{ 0x06000199, { 0, 1 } },
	{ 0x0600019A, { 1, 2 } },
	{ 0x0600019B, { 3, 1 } },
	{ 0x060002F4, { 4, 2 } },
	{ 0x060002F5, { 6, 4 } },
};
extern const uint32_t g_rgctx_T_t80D63D3CC381640E7ED160608C847023393F613E;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_tE262FA7B9B0B8DC1124EA9EE63AFC7F6FB77691E_mD9D0AAAA47166E9BCCD05B16E0C0A58DA37C0E99;
extern const uint32_t g_rgctx_T_tE262FA7B9B0B8DC1124EA9EE63AFC7F6FB77691E;
extern const uint32_t g_rgctx_T_tFD17E229B2AAE61ABD98D6D28D935B4703761F3E;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_tD49875D8FA4360D1459824E7E8724BFB4945B370_m9DA46CBE6729BD3A9F1E82ABA9F323C4C97DE7C6;
extern const uint32_t g_rgctx_T_tD49875D8FA4360D1459824E7E8724BFB4945B370;
extern const uint32_t g_rgctx_List_1_tFC6CCFCEAB64A7DD52E88FC7489CE14D01B3ABC1;
extern const uint32_t g_rgctx_List_1__ctor_mCFE7FCC4B9FF46EE21CE8628FD816CE500A30B06;
extern const uint32_t g_rgctx_T_t6BD5FDA65FB6F57FABD53B8E98A9474EF8DD2806;
extern const uint32_t g_rgctx_List_1_Add_m59727E38892F5075A0FEA099FEFA7867F093A778;
extern const uint32_t g_rgctx_GaussianWindow1d_1_set_Sigma_m25287737BF28E8A0167CE6E2F2F4214C8DFD16C2;
extern const uint32_t g_rgctx_GaussianWindow1d_1_GenerateKernel_m9EE51FEBBF2E62A4BAEABFF5FD34CFD23C88778B;
extern const uint32_t g_rgctx_GaussianWindow1d_1_get_KernelSize_m19F93A30A8B500772E4FE81C7B7E87A97B6DCC89;
extern const uint32_t g_rgctx_TU5BU5D_tD03DD18AA88A107FD8C40519AFE9F98941C97E87;
extern const uint32_t g_rgctx_GaussianWindow1d_1_AddValue_mE757ED12365F71571F1E19E4C516456B83DD90E3;
extern const uint32_t g_rgctx_GaussianWindow1d_1_Value_m00DBBA3806D114B128BDA13E238A230E89337DA2;
extern const uint32_t g_rgctx_GaussianWindow1d_1_tC71BFCB6F8F0B12154FEA7FA2BE146369AD9BEE8;
extern const uint32_t g_rgctx_GaussianWindow1d_1_Compute_m2CC099DC503468EBCF9134EF04E77A0403F34A33;
static const Il2CppRGCTXDefinition s_rgctxValues[18] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t80D63D3CC381640E7ED160608C847023393F613E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_tE262FA7B9B0B8DC1124EA9EE63AFC7F6FB77691E_mD9D0AAAA47166E9BCCD05B16E0C0A58DA37C0E99 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE262FA7B9B0B8DC1124EA9EE63AFC7F6FB77691E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFD17E229B2AAE61ABD98D6D28D935B4703761F3E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_tD49875D8FA4360D1459824E7E8724BFB4945B370_m9DA46CBE6729BD3A9F1E82ABA9F323C4C97DE7C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD49875D8FA4360D1459824E7E8724BFB4945B370 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tFC6CCFCEAB64A7DD52E88FC7489CE14D01B3ABC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mCFE7FCC4B9FF46EE21CE8628FD816CE500A30B06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6BD5FDA65FB6F57FABD53B8E98A9474EF8DD2806 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m59727E38892F5075A0FEA099FEFA7867F093A778 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_set_Sigma_m25287737BF28E8A0167CE6E2F2F4214C8DFD16C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_GenerateKernel_m9EE51FEBBF2E62A4BAEABFF5FD34CFD23C88778B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_get_KernelSize_m19F93A30A8B500772E4FE81C7B7E87A97B6DCC89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD03DD18AA88A107FD8C40519AFE9F98941C97E87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_AddValue_mE757ED12365F71571F1E19E4C516456B83DD90E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_Value_m00DBBA3806D114B128BDA13E238A230E89337DA2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GaussianWindow1d_1_tC71BFCB6F8F0B12154FEA7FA2BE146369AD9BEE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GaussianWindow1d_1_Compute_m2CC099DC503468EBCF9134EF04E77A0403F34A33 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Cinemachine_CodeGenModule;
const Il2CppCodeGenModule g_Cinemachine_CodeGenModule = 
{
	"Cinemachine.dll",
	1389,
	s_methodPointers,
	99,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	6,
	s_rgctxIndices,
	18,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
