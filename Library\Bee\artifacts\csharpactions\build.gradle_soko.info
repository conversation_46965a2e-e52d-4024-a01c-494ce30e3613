{"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles+Arguments": {"ProjectPath": "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle", "Architectures": "ARMv7, ARM64", "BuildSystem": "<PERSON><PERSON><PERSON>", "GradleProjectCreateInfo": {"HostPlatform": "Windows", "ApplicationType": "APK", "BuildType": "Release", "AndroidSDKPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK", "AndroidNDKPath": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "7.5.1", "UnityLibraryTemplatePath": "Assets/Plugins/Android\\mainTemplate.gradle", "UnityLibraryTemplatePathUsed": true, "LauncherTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\launcherTemplate.gradle", "LauncherTemplatePathUsed": false, "BaseProjectTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\baseProjectTemplate.gradle", "BaseProjectTemplatePathUsed": false, "GradlePropertiesTemplatePath": "Assets/Plugins/Android\\gradleTemplate.properties", "GradlePropertiesTemplatePathUsed": true, "GradleLibraryTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\libTemplate.gradle", "GradleLibraryTemplatePathUsed": false, "UnityProguardTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\UnityProGuardTemplate.txt", "ProguardUserPath": "Assets/Plugins/Android\\proguard-user.txt", "SettingsTemplatePath": "Assets/Plugins/Android\\settingsTemplate.gradle", "SettingsTemplatePathUsed": true, "BuildTools": "34.0.0", "TargetSDKVersion": 36, "MinSDKVersion": 23, "PackageName": "com.smg.tractor.trolly.games.farming.game", "Architectures": "ARMv7, ARM64", "BuildApkPerCpuArchitecture": false, "VersionCode": 24, "VersionName": "2.4", "Minify": 1, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": [".json"]}, "UseCustomKeystore": true, "KeystorePath": "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/keystore/smg (1).keystore", "KeystoreName": "smg (1).keystore", "KeystorePassword": "simulatorgames", "KeystoreAliasName": "smg", "KeystoreAliasPassword": "simulatorgames", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": ["Assets/Plugins/Android/GoogleMobileAdsPlugin.androidlib", "Assets/Plugins/Android/FirebaseApp.androidlib"], "AARFiles": ["Assets/Plugins/Android/googlemobileads-unity.aar"], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerActivity.java"], "JavaSourcePaths": [], "KotlinSourcePaths": [], "PlayerPackage": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo", "Dependencies": [], "AAPT2Path": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\build-tools\\34.0.0\\aapt2.exe"}}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe", "targets": ["D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/build.gradle", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build.gradle", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/build.gradle", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle.properties", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/local.properties", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/settings.gradle", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/GoogleMobileAdsPlugin.androidlib/build.gradle", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/FirebaseApp.androidlib/build.gradle", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/proguard-unity.txt", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle/wrapper/gradle-wrapper.properties"], "inputs": ["Assets/Plugins/Android/mainTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcherTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/baseProjectTemplate.gradle", "Assets/Plugins/Android/gradleTemplate.properties", "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/libTemplate.gradle", "Assets/Plugins/Android/proguard-user.txt", "Assets/Plugins/Android/settingsTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt"], "targetDirectories": []}}