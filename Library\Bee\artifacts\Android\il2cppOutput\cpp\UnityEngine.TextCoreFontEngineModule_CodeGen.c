﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Int32 UnityEngine.TextCore.GlyphRect::get_x()
extern void GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898 (void);
// 0x00000002 System.Int32 UnityEngine.TextCore.GlyphRect::get_y()
extern void GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA (void);
// 0x00000003 System.Int32 UnityEngine.TextCore.GlyphRect::get_width()
extern void GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12 (void);
// 0x00000004 System.Int32 UnityEngine.TextCore.GlyphRect::get_height()
extern void GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A (void);
// 0x00000005 UnityEngine.TextCore.GlyphRect UnityEngine.TextCore.GlyphRect::get_zero()
extern void GlyphRect_get_zero_m359121752EE1A46C51118D84F03204F3285FF3FA (void);
// 0x00000006 System.Void UnityEngine.TextCore.GlyphRect::.ctor(System.Int32,System.Int32,System.Int32,System.Int32)
extern void GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95 (void);
// 0x00000007 System.Int32 UnityEngine.TextCore.GlyphRect::GetHashCode()
extern void GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F (void);
// 0x00000008 System.Boolean UnityEngine.TextCore.GlyphRect::Equals(System.Object)
extern void GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B (void);
// 0x00000009 System.Boolean UnityEngine.TextCore.GlyphRect::Equals(UnityEngine.TextCore.GlyphRect)
extern void GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87 (void);
// 0x0000000A System.Void UnityEngine.TextCore.GlyphRect::.cctor()
extern void GlyphRect__cctor_m74BDAD5150F67B623F7D02238DD25D30C133BBDA (void);
// 0x0000000B System.Single UnityEngine.TextCore.GlyphMetrics::get_width()
extern void GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13 (void);
// 0x0000000C System.Single UnityEngine.TextCore.GlyphMetrics::get_height()
extern void GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C (void);
// 0x0000000D System.Single UnityEngine.TextCore.GlyphMetrics::get_horizontalBearingX()
extern void GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F (void);
// 0x0000000E System.Single UnityEngine.TextCore.GlyphMetrics::get_horizontalBearingY()
extern void GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223 (void);
// 0x0000000F System.Single UnityEngine.TextCore.GlyphMetrics::get_horizontalAdvance()
extern void GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1 (void);
// 0x00000010 System.Void UnityEngine.TextCore.GlyphMetrics::.ctor(System.Single,System.Single,System.Single,System.Single,System.Single)
extern void GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D (void);
// 0x00000011 System.Int32 UnityEngine.TextCore.GlyphMetrics::GetHashCode()
extern void GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6 (void);
// 0x00000012 System.Boolean UnityEngine.TextCore.GlyphMetrics::Equals(System.Object)
extern void GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970 (void);
// 0x00000013 System.Boolean UnityEngine.TextCore.GlyphMetrics::Equals(UnityEngine.TextCore.GlyphMetrics)
extern void GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB (void);
// 0x00000014 System.UInt32 UnityEngine.TextCore.Glyph::get_index()
extern void Glyph_get_index_mCFBBCF85E7F3434B7A595EEE3411EFFB78E5675B (void);
// 0x00000015 System.Void UnityEngine.TextCore.Glyph::set_index(System.UInt32)
extern void Glyph_set_index_mD033C966D79B910424B985F9D81C01D4E056B72C (void);
// 0x00000016 UnityEngine.TextCore.GlyphMetrics UnityEngine.TextCore.Glyph::get_metrics()
extern void Glyph_get_metrics_mB6E9D3D1899E35BA257638F6F58B7D260170B6FA (void);
// 0x00000017 System.Void UnityEngine.TextCore.Glyph::set_metrics(UnityEngine.TextCore.GlyphMetrics)
extern void Glyph_set_metrics_m3350984977FC50061481B1EC563DE59147428BC2 (void);
// 0x00000018 UnityEngine.TextCore.GlyphRect UnityEngine.TextCore.Glyph::get_glyphRect()
extern void Glyph_get_glyphRect_m94E7C5FE682695CDC096248EF027079F33768EE5 (void);
// 0x00000019 System.Void UnityEngine.TextCore.Glyph::set_glyphRect(UnityEngine.TextCore.GlyphRect)
extern void Glyph_set_glyphRect_mC21EB362D6EC56E0D110B0A08505CAD2DF26A6A8 (void);
// 0x0000001A System.Single UnityEngine.TextCore.Glyph::get_scale()
extern void Glyph_get_scale_m3ED738CBB032247526DB38161E180759B2D06F29 (void);
// 0x0000001B System.Void UnityEngine.TextCore.Glyph::set_scale(System.Single)
extern void Glyph_set_scale_m44247C5948E32562931FA8C44799A3E1E4F0562A (void);
// 0x0000001C System.Int32 UnityEngine.TextCore.Glyph::get_atlasIndex()
extern void Glyph_get_atlasIndex_m575332307F2C182655EE9AD352E92F1B5F4D26DF (void);
// 0x0000001D System.Void UnityEngine.TextCore.Glyph::set_atlasIndex(System.Int32)
extern void Glyph_set_atlasIndex_m622CB24F3110B65CADB0C9F0223133B0DA926ABE (void);
// 0x0000001E System.Void UnityEngine.TextCore.Glyph::.ctor()
extern void Glyph__ctor_m9FB83C6B166AC59E03B585F76C09C5FC1720281F (void);
// 0x0000001F System.Void UnityEngine.TextCore.Glyph::.ctor(UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct)
extern void Glyph__ctor_m2E3C296A46BE48B869634BBE3F97B30F3442CC94 (void);
// 0x00000020 System.Void UnityEngine.TextCore.Glyph::.ctor(System.UInt32,UnityEngine.TextCore.GlyphMetrics,UnityEngine.TextCore.GlyphRect,System.Single,System.Int32)
extern void Glyph__ctor_m71D55A8FBEA48ECCD78D65CAC9D008905E56FBF1 (void);
// 0x00000021 System.Int32 UnityEngine.TextCore.FaceInfo::get_faceIndex()
extern void FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641 (void);
// 0x00000022 System.String UnityEngine.TextCore.FaceInfo::get_familyName()
extern void FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241 (void);
// 0x00000023 System.Void UnityEngine.TextCore.FaceInfo::set_familyName(System.String)
extern void FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D (void);
// 0x00000024 System.String UnityEngine.TextCore.FaceInfo::get_styleName()
extern void FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6 (void);
// 0x00000025 System.Void UnityEngine.TextCore.FaceInfo::set_styleName(System.String)
extern void FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6 (void);
// 0x00000026 System.Int32 UnityEngine.TextCore.FaceInfo::get_pointSize()
extern void FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB (void);
// 0x00000027 System.Void UnityEngine.TextCore.FaceInfo::set_pointSize(System.Int32)
extern void FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492 (void);
// 0x00000028 System.Single UnityEngine.TextCore.FaceInfo::get_scale()
extern void FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD (void);
// 0x00000029 System.Void UnityEngine.TextCore.FaceInfo::set_scale(System.Single)
extern void FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4 (void);
// 0x0000002A System.Single UnityEngine.TextCore.FaceInfo::get_lineHeight()
extern void FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D (void);
// 0x0000002B System.Void UnityEngine.TextCore.FaceInfo::set_lineHeight(System.Single)
extern void FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE (void);
// 0x0000002C System.Single UnityEngine.TextCore.FaceInfo::get_ascentLine()
extern void FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD (void);
// 0x0000002D System.Void UnityEngine.TextCore.FaceInfo::set_ascentLine(System.Single)
extern void FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78 (void);
// 0x0000002E System.Single UnityEngine.TextCore.FaceInfo::get_capLine()
extern void FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88 (void);
// 0x0000002F System.Void UnityEngine.TextCore.FaceInfo::set_capLine(System.Single)
extern void FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF (void);
// 0x00000030 System.Single UnityEngine.TextCore.FaceInfo::get_meanLine()
extern void FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96 (void);
// 0x00000031 System.Void UnityEngine.TextCore.FaceInfo::set_meanLine(System.Single)
extern void FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99 (void);
// 0x00000032 System.Single UnityEngine.TextCore.FaceInfo::get_baseline()
extern void FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A (void);
// 0x00000033 System.Void UnityEngine.TextCore.FaceInfo::set_baseline(System.Single)
extern void FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741 (void);
// 0x00000034 System.Single UnityEngine.TextCore.FaceInfo::get_descentLine()
extern void FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3 (void);
// 0x00000035 System.Void UnityEngine.TextCore.FaceInfo::set_descentLine(System.Single)
extern void FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C (void);
// 0x00000036 System.Single UnityEngine.TextCore.FaceInfo::get_superscriptOffset()
extern void FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A (void);
// 0x00000037 System.Void UnityEngine.TextCore.FaceInfo::set_superscriptOffset(System.Single)
extern void FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5 (void);
// 0x00000038 System.Single UnityEngine.TextCore.FaceInfo::get_superscriptSize()
extern void FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0 (void);
// 0x00000039 System.Void UnityEngine.TextCore.FaceInfo::set_superscriptSize(System.Single)
extern void FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515 (void);
// 0x0000003A System.Single UnityEngine.TextCore.FaceInfo::get_subscriptOffset()
extern void FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A (void);
// 0x0000003B System.Void UnityEngine.TextCore.FaceInfo::set_subscriptOffset(System.Single)
extern void FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A (void);
// 0x0000003C System.Single UnityEngine.TextCore.FaceInfo::get_subscriptSize()
extern void FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31 (void);
// 0x0000003D System.Void UnityEngine.TextCore.FaceInfo::set_subscriptSize(System.Single)
extern void FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D (void);
// 0x0000003E System.Single UnityEngine.TextCore.FaceInfo::get_underlineOffset()
extern void FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251 (void);
// 0x0000003F System.Void UnityEngine.TextCore.FaceInfo::set_underlineOffset(System.Single)
extern void FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA (void);
// 0x00000040 System.Single UnityEngine.TextCore.FaceInfo::get_underlineThickness()
extern void FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2 (void);
// 0x00000041 System.Void UnityEngine.TextCore.FaceInfo::set_underlineThickness(System.Single)
extern void FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A (void);
// 0x00000042 System.Single UnityEngine.TextCore.FaceInfo::get_strikethroughOffset()
extern void FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5 (void);
// 0x00000043 System.Void UnityEngine.TextCore.FaceInfo::set_strikethroughOffset(System.Single)
extern void FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0 (void);
// 0x00000044 System.Void UnityEngine.TextCore.FaceInfo::set_strikethroughThickness(System.Single)
extern void FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52 (void);
// 0x00000045 System.Single UnityEngine.TextCore.FaceInfo::get_tabWidth()
extern void FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161 (void);
// 0x00000046 System.Void UnityEngine.TextCore.FaceInfo::set_tabWidth(System.Single)
extern void FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D (void);
// 0x00000047 System.Single UnityEngine.TextCore.LowLevel.GlyphValueRecord::get_xPlacement()
extern void GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8 (void);
// 0x00000048 System.Single UnityEngine.TextCore.LowLevel.GlyphValueRecord::get_yPlacement()
extern void GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA (void);
// 0x00000049 System.Single UnityEngine.TextCore.LowLevel.GlyphValueRecord::get_xAdvance()
extern void GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E (void);
// 0x0000004A System.Single UnityEngine.TextCore.LowLevel.GlyphValueRecord::get_yAdvance()
extern void GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6 (void);
// 0x0000004B UnityEngine.TextCore.LowLevel.GlyphValueRecord UnityEngine.TextCore.LowLevel.GlyphValueRecord::op_Addition(UnityEngine.TextCore.LowLevel.GlyphValueRecord,UnityEngine.TextCore.LowLevel.GlyphValueRecord)
extern void GlyphValueRecord_op_Addition_mF26165B4CE61A5409AEFF24B0D1727804E13602B (void);
// 0x0000004C System.Int32 UnityEngine.TextCore.LowLevel.GlyphValueRecord::GetHashCode()
extern void GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60 (void);
// 0x0000004D System.Boolean UnityEngine.TextCore.LowLevel.GlyphValueRecord::Equals(System.Object)
extern void GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6 (void);
// 0x0000004E System.Boolean UnityEngine.TextCore.LowLevel.GlyphValueRecord::Equals(UnityEngine.TextCore.LowLevel.GlyphValueRecord)
extern void GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972 (void);
// 0x0000004F System.UInt32 UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord::get_glyphIndex()
extern void GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9 (void);
// 0x00000050 UnityEngine.TextCore.LowLevel.GlyphValueRecord UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord::get_glyphValueRecord()
extern void GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7 (void);
// 0x00000051 System.Int32 UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord::GetHashCode()
extern void GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C (void);
// 0x00000052 System.Boolean UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord::Equals(System.Object)
extern void GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D (void);
// 0x00000053 System.Boolean UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord::Equals(UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord)
extern void GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535 (void);
// 0x00000054 UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord::get_firstAdjustmentRecord()
extern void GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C (void);
// 0x00000055 UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord::get_secondAdjustmentRecord()
extern void GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91 (void);
// 0x00000056 System.Int32 UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord::GetHashCode()
extern void GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5 (void);
// 0x00000057 System.Boolean UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord::Equals(System.Object)
extern void GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD (void);
// 0x00000058 System.Boolean UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord::Equals(UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord)
extern void GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689 (void);
// 0x00000059 UnityEngine.TextCore.LowLevel.FontEngineError UnityEngine.TextCore.LowLevel.FontEngine::InitializeFontEngine()
extern void FontEngine_InitializeFontEngine_mCA2F9C3294A61C0294B4B9849082C0344169F322 (void);
// 0x0000005A System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::InitializeFontEngine_Internal()
extern void FontEngine_InitializeFontEngine_Internal_m64ABDC3A5AF9D87C995FF6B98979933E7074AB06 (void);
// 0x0000005B UnityEngine.TextCore.LowLevel.FontEngineError UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace(System.String,System.Int32,System.Int32)
extern void FontEngine_LoadFontFace_m6DCF863A84AF3E2EEB6AC69C7C19D937909698C1 (void);
// 0x0000005C System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_And_FaceIndex_Internal(System.String,System.Int32,System.Int32)
extern void FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_mD89D8C9D6A2B8E7D29BAE669C15781DBCC63B8E4 (void);
// 0x0000005D UnityEngine.TextCore.LowLevel.FontEngineError UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace(UnityEngine.Font,System.Int32)
extern void FontEngine_LoadFontFace_m63A9171030B36C960896CEF55E7ECF55AF990548 (void);
// 0x0000005E System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_FromFont_Internal(UnityEngine.Font,System.Int32)
extern void FontEngine_LoadFontFace_With_Size_FromFont_Internal_m13A7F3EB357E2D2C67B14D10B96F3C490BF6AC11 (void);
// 0x0000005F UnityEngine.TextCore.LowLevel.FontEngineError UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace(UnityEngine.Font,System.Int32,System.Int32)
extern void FontEngine_LoadFontFace_m15ECA69542615468DB27EE5DCA11EE855BDAA356 (void);
// 0x00000060 System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal(UnityEngine.Font,System.Int32,System.Int32)
extern void FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_m7C4E8F4F88CDDE6B838D890FDC1E0722EBD40497 (void);
// 0x00000061 UnityEngine.TextCore.LowLevel.FontEngineError UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace(System.String,System.String,System.Int32)
extern void FontEngine_LoadFontFace_m793FE6557D378E25CDF12DDA5EFAA1F0F9A51C26 (void);
// 0x00000062 System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal(System.String,System.String,System.Int32)
extern void FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_m0856180C0F5FFA09E964986A78153D021F810F30 (void);
// 0x00000063 System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetSystemFontReference(System.String,System.String,UnityEngine.TextCore.LowLevel.FontReference&)
extern void FontEngine_TryGetSystemFontReference_mA32D1513035E9B58417092500DDC3A7C939367A1 (void);
// 0x00000064 System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetSystemFontReference_Internal(System.String,System.String,UnityEngine.TextCore.LowLevel.FontReference&)
extern void FontEngine_TryGetSystemFontReference_Internal_m61AAB78124D19B1BC88C22D520287893E7280E1F (void);
// 0x00000065 UnityEngine.TextCore.FaceInfo UnityEngine.TextCore.LowLevel.FontEngine::GetFaceInfo()
extern void FontEngine_GetFaceInfo_mF371B75CDEEDA91FF910BED8DEE8FEB2A493BE37 (void);
// 0x00000066 System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetFaceInfo_Internal(UnityEngine.TextCore.FaceInfo&)
extern void FontEngine_GetFaceInfo_Internal_m34E21653DF4724C3FCE289DA20AD5AB1B2F24B90 (void);
// 0x00000067 System.UInt32 UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphIndex(System.UInt32)
extern void FontEngine_GetGlyphIndex_mEAE36421D92783413286344213D6EFD52E90CC00 (void);
// 0x00000068 System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithUnicodeValue(System.UInt32,UnityEngine.TextCore.LowLevel.GlyphLoadFlags,UnityEngine.TextCore.Glyph&)
extern void FontEngine_TryGetGlyphWithUnicodeValue_m58889809E3D65A0F1C5AEFF4DF6D319EBD139159 (void);
// 0x00000069 System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithUnicodeValue_Internal(System.UInt32,UnityEngine.TextCore.LowLevel.GlyphLoadFlags,UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct&)
extern void FontEngine_TryGetGlyphWithUnicodeValue_Internal_mD799CD2B5CEA61ED5ABA16EDAA428385FD906BD8 (void);
// 0x0000006A System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithIndexValue(System.UInt32,UnityEngine.TextCore.LowLevel.GlyphLoadFlags,UnityEngine.TextCore.Glyph&)
extern void FontEngine_TryGetGlyphWithIndexValue_mD922A7EB95949E95D96C222D2CA1ED56BA2E81C3 (void);
// 0x0000006B System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithIndexValue_Internal(System.UInt32,UnityEngine.TextCore.LowLevel.GlyphLoadFlags,UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct&)
extern void FontEngine_TryGetGlyphWithIndexValue_Internal_m2C5359B3C62139D20F52C1D232938CFAC9C3459D (void);
// 0x0000006C System.Void UnityEngine.TextCore.LowLevel.FontEngine::SetTextureUploadMode(System.Boolean)
extern void FontEngine_SetTextureUploadMode_m04D13DFE627B79D5EB574EC74556E31DF42A83F3 (void);
// 0x0000006D System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphToTexture(System.UInt32,System.Int32,UnityEngine.TextCore.LowLevel.GlyphPackingMode,System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect>,System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect>,UnityEngine.TextCore.LowLevel.GlyphRenderMode,UnityEngine.Texture2D,UnityEngine.TextCore.Glyph&)
extern void FontEngine_TryAddGlyphToTexture_m45A94FA06ADDCE2FA6B139B29E942496B760A090 (void);
// 0x0000006E System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphToTexture_Internal(System.UInt32,System.Int32,UnityEngine.TextCore.LowLevel.GlyphPackingMode,UnityEngine.TextCore.GlyphRect[],System.Int32&,UnityEngine.TextCore.GlyphRect[],System.Int32&,UnityEngine.TextCore.LowLevel.GlyphRenderMode,UnityEngine.Texture2D,UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct&)
extern void FontEngine_TryAddGlyphToTexture_Internal_mD6ED15BDDDC4F874C98D514D8DCE699EADB8C708 (void);
// 0x0000006F System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphsToTexture(System.Collections.Generic.List`1<System.UInt32>,System.Int32,UnityEngine.TextCore.LowLevel.GlyphPackingMode,System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect>,System.Collections.Generic.List`1<UnityEngine.TextCore.GlyphRect>,UnityEngine.TextCore.LowLevel.GlyphRenderMode,UnityEngine.Texture2D,UnityEngine.TextCore.Glyph[]&)
extern void FontEngine_TryAddGlyphsToTexture_m18740AD9F7264F54C397916268C0AB0738879801 (void);
// 0x00000070 System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphsToTexture_Internal(System.UInt32[],System.Int32,UnityEngine.TextCore.LowLevel.GlyphPackingMode,UnityEngine.TextCore.GlyphRect[],System.Int32&,UnityEngine.TextCore.GlyphRect[],System.Int32&,UnityEngine.TextCore.LowLevel.GlyphRenderMode,UnityEngine.Texture2D,UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct[],System.Int32&)
extern void FontEngine_TryAddGlyphsToTexture_Internal_m43D4D242873C647DF5A20F7579FD90E373999EA8 (void);
// 0x00000071 UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord[] UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphPairAdjustmentTable(System.UInt32[])
extern void FontEngine_GetGlyphPairAdjustmentTable_m67DAC7C0029384FC814621F85F8B35D0D3327BC5 (void);
// 0x00000072 UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord[] UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphPairAdjustmentRecords(System.Collections.Generic.List`1<System.UInt32>,System.Int32&)
extern void FontEngine_GetGlyphPairAdjustmentRecords_m23D346BEC5BA63185A01DF33576E98650947ABA8 (void);
// 0x00000073 System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::PopulatePairAdjustmentRecordMarshallingArray_from_KernTable(System.UInt32[],System.Int32&)
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_mE3B5D21C5D72CEADB2BEA5AA8022E466356386E7 (void);
// 0x00000074 System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetPairAdjustmentRecordsFromMarshallingArray(UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord[])
extern void FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_m81121A95D2196747D3597C8BEFF53842577599F6 (void);
// 0x00000075 System.Void UnityEngine.TextCore.LowLevel.FontEngine::GenericListToMarshallingArray(System.Collections.Generic.List`1<T>&,T[]&)
// 0x00000076 System.Void UnityEngine.TextCore.LowLevel.FontEngine::SetMarshallingArraySize(T[]&,System.Int32)
// 0x00000077 System.Void UnityEngine.TextCore.LowLevel.FontEngine::ResetAtlasTexture(UnityEngine.Texture2D)
extern void FontEngine_ResetAtlasTexture_m15BBE67DFDD8A1E740BC0C4B29612A8C866860DC (void);
// 0x00000078 System.Void UnityEngine.TextCore.LowLevel.FontEngine::.cctor()
extern void FontEngine__cctor_mD8EC115E258FD225726CBC71B249C1F411447D79 (void);
// 0x00000079 System.Int32 UnityEngine.TextCore.LowLevel.FontEngineUtilities::MaxValue(System.Int32,System.Int32,System.Int32)
extern void FontEngineUtilities_MaxValue_m7E0FBF90FE07F65C9895ACD56FD032A53E417F83 (void);
static Il2CppMethodPointer s_methodPointers[121] = 
{
	GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898,
	GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA,
	GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12,
	GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A,
	GlyphRect_get_zero_m359121752EE1A46C51118D84F03204F3285FF3FA,
	GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95,
	GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F,
	GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B,
	GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87,
	GlyphRect__cctor_m74BDAD5150F67B623F7D02238DD25D30C133BBDA,
	GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13,
	GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C,
	GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F,
	GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223,
	GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1,
	GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D,
	GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6,
	GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970,
	GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB,
	Glyph_get_index_mCFBBCF85E7F3434B7A595EEE3411EFFB78E5675B,
	Glyph_set_index_mD033C966D79B910424B985F9D81C01D4E056B72C,
	Glyph_get_metrics_mB6E9D3D1899E35BA257638F6F58B7D260170B6FA,
	Glyph_set_metrics_m3350984977FC50061481B1EC563DE59147428BC2,
	Glyph_get_glyphRect_m94E7C5FE682695CDC096248EF027079F33768EE5,
	Glyph_set_glyphRect_mC21EB362D6EC56E0D110B0A08505CAD2DF26A6A8,
	Glyph_get_scale_m3ED738CBB032247526DB38161E180759B2D06F29,
	Glyph_set_scale_m44247C5948E32562931FA8C44799A3E1E4F0562A,
	Glyph_get_atlasIndex_m575332307F2C182655EE9AD352E92F1B5F4D26DF,
	Glyph_set_atlasIndex_m622CB24F3110B65CADB0C9F0223133B0DA926ABE,
	Glyph__ctor_m9FB83C6B166AC59E03B585F76C09C5FC1720281F,
	Glyph__ctor_m2E3C296A46BE48B869634BBE3F97B30F3442CC94,
	Glyph__ctor_m71D55A8FBEA48ECCD78D65CAC9D008905E56FBF1,
	FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641,
	FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241,
	FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D,
	FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6,
	FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6,
	FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB,
	FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492,
	FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD,
	FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4,
	FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D,
	FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE,
	FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD,
	FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78,
	FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88,
	FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF,
	FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96,
	FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99,
	FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A,
	FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741,
	FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3,
	FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C,
	FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A,
	FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5,
	FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0,
	FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515,
	FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A,
	FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A,
	FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31,
	FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D,
	FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251,
	FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA,
	FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2,
	FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A,
	FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5,
	FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0,
	FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52,
	FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161,
	FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D,
	GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8,
	GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA,
	GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E,
	GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6,
	GlyphValueRecord_op_Addition_mF26165B4CE61A5409AEFF24B0D1727804E13602B,
	GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60,
	GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6,
	GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972,
	GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9,
	GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7,
	GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C,
	GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D,
	GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535,
	GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C,
	GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91,
	GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5,
	GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD,
	GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689,
	FontEngine_InitializeFontEngine_mCA2F9C3294A61C0294B4B9849082C0344169F322,
	FontEngine_InitializeFontEngine_Internal_m64ABDC3A5AF9D87C995FF6B98979933E7074AB06,
	FontEngine_LoadFontFace_m6DCF863A84AF3E2EEB6AC69C7C19D937909698C1,
	FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_mD89D8C9D6A2B8E7D29BAE669C15781DBCC63B8E4,
	FontEngine_LoadFontFace_m63A9171030B36C960896CEF55E7ECF55AF990548,
	FontEngine_LoadFontFace_With_Size_FromFont_Internal_m13A7F3EB357E2D2C67B14D10B96F3C490BF6AC11,
	FontEngine_LoadFontFace_m15ECA69542615468DB27EE5DCA11EE855BDAA356,
	FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_m7C4E8F4F88CDDE6B838D890FDC1E0722EBD40497,
	FontEngine_LoadFontFace_m793FE6557D378E25CDF12DDA5EFAA1F0F9A51C26,
	FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_m0856180C0F5FFA09E964986A78153D021F810F30,
	FontEngine_TryGetSystemFontReference_mA32D1513035E9B58417092500DDC3A7C939367A1,
	FontEngine_TryGetSystemFontReference_Internal_m61AAB78124D19B1BC88C22D520287893E7280E1F,
	FontEngine_GetFaceInfo_mF371B75CDEEDA91FF910BED8DEE8FEB2A493BE37,
	FontEngine_GetFaceInfo_Internal_m34E21653DF4724C3FCE289DA20AD5AB1B2F24B90,
	FontEngine_GetGlyphIndex_mEAE36421D92783413286344213D6EFD52E90CC00,
	FontEngine_TryGetGlyphWithUnicodeValue_m58889809E3D65A0F1C5AEFF4DF6D319EBD139159,
	FontEngine_TryGetGlyphWithUnicodeValue_Internal_mD799CD2B5CEA61ED5ABA16EDAA428385FD906BD8,
	FontEngine_TryGetGlyphWithIndexValue_mD922A7EB95949E95D96C222D2CA1ED56BA2E81C3,
	FontEngine_TryGetGlyphWithIndexValue_Internal_m2C5359B3C62139D20F52C1D232938CFAC9C3459D,
	FontEngine_SetTextureUploadMode_m04D13DFE627B79D5EB574EC74556E31DF42A83F3,
	FontEngine_TryAddGlyphToTexture_m45A94FA06ADDCE2FA6B139B29E942496B760A090,
	FontEngine_TryAddGlyphToTexture_Internal_mD6ED15BDDDC4F874C98D514D8DCE699EADB8C708,
	FontEngine_TryAddGlyphsToTexture_m18740AD9F7264F54C397916268C0AB0738879801,
	FontEngine_TryAddGlyphsToTexture_Internal_m43D4D242873C647DF5A20F7579FD90E373999EA8,
	FontEngine_GetGlyphPairAdjustmentTable_m67DAC7C0029384FC814621F85F8B35D0D3327BC5,
	FontEngine_GetGlyphPairAdjustmentRecords_m23D346BEC5BA63185A01DF33576E98650947ABA8,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_mE3B5D21C5D72CEADB2BEA5AA8022E466356386E7,
	FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_m81121A95D2196747D3597C8BEFF53842577599F6,
	NULL,
	NULL,
	FontEngine_ResetAtlasTexture_m15BBE67DFDD8A1E740BC0C4B29612A8C866860DC,
	FontEngine__cctor_mD8EC115E258FD225726CBC71B249C1F411447D79,
	FontEngineUtilities_MaxValue_m7E0FBF90FE07F65C9895ACD56FD032A53E417F83,
};
extern void GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898_AdjustorThunk (void);
extern void GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA_AdjustorThunk (void);
extern void GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12_AdjustorThunk (void);
extern void GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A_AdjustorThunk (void);
extern void GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95_AdjustorThunk (void);
extern void GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F_AdjustorThunk (void);
extern void GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B_AdjustorThunk (void);
extern void GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87_AdjustorThunk (void);
extern void GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13_AdjustorThunk (void);
extern void GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1_AdjustorThunk (void);
extern void GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D_AdjustorThunk (void);
extern void GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6_AdjustorThunk (void);
extern void GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970_AdjustorThunk (void);
extern void GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB_AdjustorThunk (void);
extern void FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641_AdjustorThunk (void);
extern void FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241_AdjustorThunk (void);
extern void FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D_AdjustorThunk (void);
extern void FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6_AdjustorThunk (void);
extern void FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6_AdjustorThunk (void);
extern void FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB_AdjustorThunk (void);
extern void FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492_AdjustorThunk (void);
extern void FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD_AdjustorThunk (void);
extern void FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4_AdjustorThunk (void);
extern void FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D_AdjustorThunk (void);
extern void FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE_AdjustorThunk (void);
extern void FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD_AdjustorThunk (void);
extern void FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78_AdjustorThunk (void);
extern void FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88_AdjustorThunk (void);
extern void FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF_AdjustorThunk (void);
extern void FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96_AdjustorThunk (void);
extern void FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99_AdjustorThunk (void);
extern void FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A_AdjustorThunk (void);
extern void FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741_AdjustorThunk (void);
extern void FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3_AdjustorThunk (void);
extern void FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C_AdjustorThunk (void);
extern void FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A_AdjustorThunk (void);
extern void FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5_AdjustorThunk (void);
extern void FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0_AdjustorThunk (void);
extern void FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515_AdjustorThunk (void);
extern void FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A_AdjustorThunk (void);
extern void FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A_AdjustorThunk (void);
extern void FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31_AdjustorThunk (void);
extern void FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D_AdjustorThunk (void);
extern void FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251_AdjustorThunk (void);
extern void FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA_AdjustorThunk (void);
extern void FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2_AdjustorThunk (void);
extern void FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A_AdjustorThunk (void);
extern void FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5_AdjustorThunk (void);
extern void FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0_AdjustorThunk (void);
extern void FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52_AdjustorThunk (void);
extern void FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161_AdjustorThunk (void);
extern void FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D_AdjustorThunk (void);
extern void GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8_AdjustorThunk (void);
extern void GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA_AdjustorThunk (void);
extern void GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E_AdjustorThunk (void);
extern void GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6_AdjustorThunk (void);
extern void GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60_AdjustorThunk (void);
extern void GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6_AdjustorThunk (void);
extern void GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[72] = 
{
	{ 0x06000001, GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898_AdjustorThunk },
	{ 0x06000002, GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA_AdjustorThunk },
	{ 0x06000003, GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12_AdjustorThunk },
	{ 0x06000004, GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A_AdjustorThunk },
	{ 0x06000006, GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95_AdjustorThunk },
	{ 0x06000007, GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F_AdjustorThunk },
	{ 0x06000008, GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B_AdjustorThunk },
	{ 0x06000009, GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87_AdjustorThunk },
	{ 0x0600000B, GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13_AdjustorThunk },
	{ 0x0600000C, GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C_AdjustorThunk },
	{ 0x0600000D, GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F_AdjustorThunk },
	{ 0x0600000E, GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223_AdjustorThunk },
	{ 0x0600000F, GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1_AdjustorThunk },
	{ 0x06000010, GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D_AdjustorThunk },
	{ 0x06000011, GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6_AdjustorThunk },
	{ 0x06000012, GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970_AdjustorThunk },
	{ 0x06000013, GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB_AdjustorThunk },
	{ 0x06000021, FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641_AdjustorThunk },
	{ 0x06000022, FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241_AdjustorThunk },
	{ 0x06000023, FaceInfo_set_familyName_m49CB07A51AC9008B4F399A3064EC4FF6EA8E839D_AdjustorThunk },
	{ 0x06000024, FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6_AdjustorThunk },
	{ 0x06000025, FaceInfo_set_styleName_m3BEBF7E576032A668BCC83D88D2F6902F561B5E6_AdjustorThunk },
	{ 0x06000026, FaceInfo_get_pointSize_m7EF7429A4725AB715931A220F6BB498C3D6BF7CB_AdjustorThunk },
	{ 0x06000027, FaceInfo_set_pointSize_m17D0B03C4A762F657A4ABD15327B08D6B55EF492_AdjustorThunk },
	{ 0x06000028, FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD_AdjustorThunk },
	{ 0x06000029, FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4_AdjustorThunk },
	{ 0x0600002A, FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D_AdjustorThunk },
	{ 0x0600002B, FaceInfo_set_lineHeight_m5952A394C6055DD86700448D9E33EBBE852E05AE_AdjustorThunk },
	{ 0x0600002C, FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD_AdjustorThunk },
	{ 0x0600002D, FaceInfo_set_ascentLine_mDFB32635374875A695D3BB686D2A16C648A20D78_AdjustorThunk },
	{ 0x0600002E, FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88_AdjustorThunk },
	{ 0x0600002F, FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF_AdjustorThunk },
	{ 0x06000030, FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96_AdjustorThunk },
	{ 0x06000031, FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99_AdjustorThunk },
	{ 0x06000032, FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A_AdjustorThunk },
	{ 0x06000033, FaceInfo_set_baseline_m528F6ADAF4F45A31E9D2BA362FB3C02B03DDD741_AdjustorThunk },
	{ 0x06000034, FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3_AdjustorThunk },
	{ 0x06000035, FaceInfo_set_descentLine_m62423E864258229A85FD3684D8385A44C01AF97C_AdjustorThunk },
	{ 0x06000036, FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A_AdjustorThunk },
	{ 0x06000037, FaceInfo_set_superscriptOffset_mC81144590FA80858D489AFC46AAA383E11D3D2B5_AdjustorThunk },
	{ 0x06000038, FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0_AdjustorThunk },
	{ 0x06000039, FaceInfo_set_superscriptSize_m89F17C1502C07A3846402A5A43369BFB66723515_AdjustorThunk },
	{ 0x0600003A, FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A_AdjustorThunk },
	{ 0x0600003B, FaceInfo_set_subscriptOffset_m796EF61DE0677BFB134F6B8C4B6C1869BCF8782A_AdjustorThunk },
	{ 0x0600003C, FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31_AdjustorThunk },
	{ 0x0600003D, FaceInfo_set_subscriptSize_m5759439F9D100591A6FFF1D7377495CEBDCD6B5D_AdjustorThunk },
	{ 0x0600003E, FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251_AdjustorThunk },
	{ 0x0600003F, FaceInfo_set_underlineOffset_m1C0E755772FE8C47D565E6CC7DC4E953B71794AA_AdjustorThunk },
	{ 0x06000040, FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2_AdjustorThunk },
	{ 0x06000041, FaceInfo_set_underlineThickness_mDD002D08CE97E0C7DA6FA6FFBDD15295D24B303A_AdjustorThunk },
	{ 0x06000042, FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5_AdjustorThunk },
	{ 0x06000043, FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0_AdjustorThunk },
	{ 0x06000044, FaceInfo_set_strikethroughThickness_m8CA3C9897FF3D5D0F653539CCBF5E16E01CACF52_AdjustorThunk },
	{ 0x06000045, FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161_AdjustorThunk },
	{ 0x06000046, FaceInfo_set_tabWidth_m44234ED657FAB54320C48C34D48532F8232F6E1D_AdjustorThunk },
	{ 0x06000047, GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8_AdjustorThunk },
	{ 0x06000048, GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA_AdjustorThunk },
	{ 0x06000049, GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E_AdjustorThunk },
	{ 0x0600004A, GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6_AdjustorThunk },
	{ 0x0600004C, GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60_AdjustorThunk },
	{ 0x0600004D, GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6_AdjustorThunk },
	{ 0x0600004E, GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972_AdjustorThunk },
	{ 0x0600004F, GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9_AdjustorThunk },
	{ 0x06000050, GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7_AdjustorThunk },
	{ 0x06000051, GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C_AdjustorThunk },
	{ 0x06000052, GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D_AdjustorThunk },
	{ 0x06000053, GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535_AdjustorThunk },
	{ 0x06000054, GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C_AdjustorThunk },
	{ 0x06000055, GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91_AdjustorThunk },
	{ 0x06000056, GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5_AdjustorThunk },
	{ 0x06000057, GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD_AdjustorThunk },
	{ 0x06000058, GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689_AdjustorThunk },
};
static const int32_t s_InvokerIndices[121] = 
{
	6178,
	6178,
	6178,
	6178,
	9507,
	942,
	6178,
	3597,
	3548,
	9569,
	6259,
	6259,
	6259,
	6259,
	6259,
	497,
	6178,
	3597,
	3546,
	6315,
	5136,
	6150,
	4991,
	6152,
	4993,
	6259,
	5087,
	6178,
	5016,
	6329,
	4990,
	500,
	6178,
	6204,
	5040,
	6204,
	5040,
	6178,
	5016,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	6259,
	5087,
	5087,
	6259,
	5087,
	6259,
	6259,
	6259,
	6259,
	8411,
	6178,
	3597,
	3549,
	6315,
	6153,
	6178,
	3597,
	3544,
	6148,
	6148,
	6178,
	3597,
	3547,
	9515,
	9515,
	7781,
	7781,
	8455,
	8455,
	7781,
	7781,
	7787,
	7787,
	7700,
	7700,
	9501,
	9051,
	9325,
	7716,
	7716,
	7716,
	7716,
	9363,
	6565,
	6523,
	6563,
	6516,
	9160,
	8523,
	8452,
	9066,
	0,
	0,
	9375,
	9569,
	7770,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000075, { 0, 5 } },
	{ 0x06000076, { 5, 2 } },
};
extern const uint32_t g_rgctx_List_1_tAF861B916DD21383F06D73A5F0F2132D57F5A2E5;
extern const uint32_t g_rgctx_List_1_get_Count_mAFBAA494C44EA10C5165ABCAF3C7D9948C9776BB;
extern const uint32_t g_rgctx_TU5BU5D_t04DF92FC5A6ED2E137925B2D4AAF396747A65B9D;
extern const uint32_t g_rgctx_Array_Resize_TisT_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017_mC449C792316CB2FED9221E20AB9071D2651433EE;
extern const uint32_t g_rgctx_List_1_get_Item_mFBCECC084098088A5E18C51A686368A5839C7EF3;
extern const uint32_t g_rgctx_TU5BU5D_t8A34B43F2CD5F32F323C003A2FDB6F9C9A6C977A;
extern const uint32_t g_rgctx_Array_Resize_TisT_t201BA83724B99DBE4155ED3CCD796A8773447525_mE500A00B5DE2E8E6605EF95ECC80FCBF77FD2DDC;
static const Il2CppRGCTXDefinition s_rgctxValues[7] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tAF861B916DD21383F06D73A5F0F2132D57F5A2E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mAFBAA494C44EA10C5165ABCAF3C7D9948C9776BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t04DF92FC5A6ED2E137925B2D4AAF396747A65B9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017_mC449C792316CB2FED9221E20AB9071D2651433EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mFBCECC084098088A5E18C51A686368A5839C7EF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t8A34B43F2CD5F32F323C003A2FDB6F9C9A6C977A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t201BA83724B99DBE4155ED3CCD796A8773447525_mE500A00B5DE2E8E6605EF95ECC80FCBF77FD2DDC },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule = 
{
	"UnityEngine.TextCoreFontEngineModule.dll",
	121,
	s_methodPointers,
	72,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	7,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
