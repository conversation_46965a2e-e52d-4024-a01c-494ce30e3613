﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Common.AdErrorClientEventArgs::.ctor()
extern void AdErrorClientEventArgs__ctor_mE8C76834CB63E41EDC8F533A16BF1ADE185649BA (void);
// 0x00000002 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.AdErrorClientEventArgs::get_AdErrorClient()
extern void AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF (void);
// 0x00000003 System.Void GoogleMobileAds.Common.AdErrorClientEventArgs::set_AdErrorClient(GoogleMobileAds.Common.IAdErrorClient)
extern void AdErrorClientEventArgs_set_AdErrorClient_mF1018FAC40B7908AADE3D91D83CD6AC3310CDC6E (void);
// 0x00000004 System.Void GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::.ctor()
extern void AdInspectorErrorClientEventArgs__ctor_mD589A3C66FE286DEEB9EAF61585447A0AA9C90D2 (void);
// 0x00000005 System.Void GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::set_AdErrorClient(GoogleMobileAds.Common.IAdInspectorErrorClient)
extern void AdInspectorErrorClientEventArgs_set_AdErrorClient_m30E0F50965C8A0C01F9CB9C81FE5076079ADB259 (void);
// 0x00000006 System.Void GoogleMobileAds.Common.AppStateEventClient::.ctor()
extern void AppStateEventClient__ctor_m7033F1368759856CC91974B018E4004FC6CB36D6 (void);
// 0x00000007 GoogleMobileAds.Common.AppStateEventClient GoogleMobileAds.Common.AppStateEventClient::get_Instance()
extern void AppStateEventClient_get_Instance_mD6A5472FC082A0EDFB6A26F7290A90E88FEB0E9B (void);
// 0x00000008 System.Void GoogleMobileAds.Common.AppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_AppStateChanged_m5F95F6F5F802D911F18C9A0F6F9CEF5C6D41CD7C (void);
// 0x00000009 System.Void GoogleMobileAds.Common.AppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_AppStateChanged_mCCA2A08AE6710D6FA7AA2418B8CFDC071B189499 (void);
// 0x0000000A System.Void GoogleMobileAds.Common.AppStateEventClient::OnApplicationPause(System.Boolean)
extern void AppStateEventClient_OnApplicationPause_m85DD95B9D3B4E6722106A81E328690F7CAEBA8F8 (void);
// 0x0000000B System.Void GoogleMobileAds.Common.AppStateEventClient::<AppStateChanged>m__0(GoogleMobileAds.Common.AppState)
extern void AppStateEventClient_U3CAppStateChangedU3Em__0_m07E4B12AED15A4F1195CB9A239F6ED3432AE1F67 (void);
// 0x0000000C System.String GoogleMobileAds.Common.IAdErrorClient::GetMessage()
// 0x0000000D System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000000E System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000000F System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000010 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000011 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000012 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000013 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000014 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000015 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000016 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000017 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000018 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000019 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000001A System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000001B System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdClicked(System.Action)
// 0x0000001C System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdClicked(System.Action)
// 0x0000001D System.Void GoogleMobileAds.Common.IAppOpenAdClient::CreateAppOpenAd()
// 0x0000001E System.Void GoogleMobileAds.Common.IAppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x0000001F System.Void GoogleMobileAds.Common.IAppOpenAdClient::Show()
// 0x00000020 System.Void GoogleMobileAds.Common.IAppOpenAdClient::DestroyAppOpenAd()
// 0x00000021 System.Void GoogleMobileAds.Common.IAppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
// 0x00000022 System.Void GoogleMobileAds.Common.IAppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
// 0x00000023 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000024 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000025 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000026 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000027 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdOpening(System.EventHandler`1<System.EventArgs>)
// 0x00000028 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdOpening(System.EventHandler`1<System.EventArgs>)
// 0x00000029 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClosed(System.EventHandler`1<System.EventArgs>)
// 0x0000002A System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdClosed(System.EventHandler`1<System.EventArgs>)
// 0x0000002B System.Void GoogleMobileAds.Common.IBannerClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x0000002C System.Void GoogleMobileAds.Common.IBannerClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x0000002D System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClicked(System.Action)
// 0x0000002E System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdClicked(System.Action)
// 0x0000002F System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdImpressionRecorded(System.Action)
// 0x00000030 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdImpressionRecorded(System.Action)
// 0x00000031 System.Void GoogleMobileAds.Common.IBannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
// 0x00000032 System.Void GoogleMobileAds.Common.IBannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
// 0x00000033 System.Void GoogleMobileAds.Common.IBannerClient::ShowBannerView()
// 0x00000034 System.Void GoogleMobileAds.Common.IBannerClient::HideBannerView()
// 0x00000035 System.Void GoogleMobileAds.Common.IBannerClient::DestroyBannerView()
// 0x00000036 System.Single GoogleMobileAds.Common.IBannerClient::GetHeightInPixels()
// 0x00000037 System.Single GoogleMobileAds.Common.IBannerClient::GetWidthInPixels()
// 0x00000038 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IBannerClient::GetResponseInfoClient()
// 0x00000039 GoogleMobileAds.Common.IAppStateEventClient GoogleMobileAds.IClientFactory::BuildAppStateEventClient()
// 0x0000003A GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.IClientFactory::BuildAppOpenAdClient()
// 0x0000003B GoogleMobileAds.Common.IBannerClient GoogleMobileAds.IClientFactory::BuildBannerClient()
// 0x0000003C GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.IClientFactory::BuildInterstitialClient()
// 0x0000003D GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.IClientFactory::BuildRewardedAdClient()
// 0x0000003E GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.IClientFactory::BuildRewardedInterstitialAdClient()
// 0x0000003F GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.IClientFactory::MobileAdsInstance()
// 0x00000040 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000041 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000042 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000043 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000044 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000045 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000046 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000047 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000048 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000049 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000004A System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000004B System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000004C System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000004D System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000004E System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdClicked(System.Action)
// 0x0000004F System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdClicked(System.Action)
// 0x00000050 System.Void GoogleMobileAds.Common.IInterstitialClient::CreateInterstitialAd()
// 0x00000051 System.Void GoogleMobileAds.Common.IInterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x00000052 System.Void GoogleMobileAds.Common.IInterstitialClient::Show()
// 0x00000053 System.Void GoogleMobileAds.Common.IInterstitialClient::DestroyInterstitial()
// 0x00000054 System.Void GoogleMobileAds.Common.IMobileAdsClient::Initialize(System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>)
// 0x00000055 System.Void GoogleMobileAds.Common.IMobileAdsClient::SetiOSAppPauseOnBackground(System.Boolean)
// 0x00000056 System.Void GoogleMobileAds.Common.IMobileAdsClient::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
// 0x00000057 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000058 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000059 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000005A System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000005B System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x0000005C System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x0000005D System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x0000005E System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x0000005F System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000060 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000061 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000062 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000063 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000064 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000065 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000066 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000067 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdClicked(System.Action)
// 0x00000068 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdClicked(System.Action)
// 0x00000069 System.Void GoogleMobileAds.Common.IRewardedAdClient::CreateRewardedAd()
// 0x0000006A System.Void GoogleMobileAds.Common.IRewardedAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x0000006B System.Void GoogleMobileAds.Common.IRewardedAdClient::Show()
// 0x0000006C System.Void GoogleMobileAds.Common.IRewardedAdClient::DestroyRewardedAd()
// 0x0000006D System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000006E System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000006F System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000070 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000071 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000072 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000073 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x00000074 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x00000075 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000076 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000077 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000078 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000079 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000007A System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000007B System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000007C System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000007D System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdClicked(System.Action)
// 0x0000007E System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdClicked(System.Action)
// 0x0000007F System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::CreateRewardedInterstitialAd()
// 0x00000080 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x00000081 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::Show()
// 0x00000082 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::DestroyRewardedInterstitialAd()
// 0x00000083 System.Void GoogleMobileAds.Common.LoadAdErrorClientEventArgs::.ctor()
extern void LoadAdErrorClientEventArgs__ctor_m2D09A984965A5EDB0506CDAC47BE1675E80AE0AB (void);
// 0x00000084 GoogleMobileAds.Common.ILoadAdErrorClient GoogleMobileAds.Common.LoadAdErrorClientEventArgs::get_LoadAdErrorClient()
extern void LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69 (void);
// 0x00000085 System.Void GoogleMobileAds.Common.LoadAdErrorClientEventArgs::set_LoadAdErrorClient(GoogleMobileAds.Common.ILoadAdErrorClient)
extern void LoadAdErrorClientEventArgs_set_LoadAdErrorClient_m6E865CB1B347CEF0B75F55CE43EE3FDBF8353353 (void);
// 0x00000086 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::.ctor()
extern void MobileAdsEventExecutor__ctor_m41D53858D8E26BD68A2C99A50E0A3ABAA1AD3EE4 (void);
// 0x00000087 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Initialize()
extern void MobileAdsEventExecutor_Initialize_m76A4DE8CDB2F00244DF305ACE913CDA65750ADEE (void);
// 0x00000088 System.Boolean GoogleMobileAds.Common.MobileAdsEventExecutor::IsActive()
extern void MobileAdsEventExecutor_IsActive_m739FAFCE76C28483EAD8FDF0D8004DE3FA8D7002 (void);
// 0x00000089 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Awake()
extern void MobileAdsEventExecutor_Awake_mBED27401298ECAA85C578989510914356138EDAB (void);
// 0x0000008A System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::ExecuteInUpdate(System.Action)
extern void MobileAdsEventExecutor_ExecuteInUpdate_m4D7D863382B3DFA6F791CBCFD275B5F7EE4BA91E (void);
// 0x0000008B System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::InvokeInUpdate(UnityEngine.Events.UnityEvent)
extern void MobileAdsEventExecutor_InvokeInUpdate_m707D79AE5D859144C46C142B97CA2C20BA53A171 (void);
// 0x0000008C System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Update()
extern void MobileAdsEventExecutor_Update_mA2FA865A80DE36FC26CAD15783037324C74D5113 (void);
// 0x0000008D System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::OnDisable()
extern void MobileAdsEventExecutor_OnDisable_m23177F7EA326224E1CCB135CC0CF96FE54CAC975 (void);
// 0x0000008E System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::.cctor()
extern void MobileAdsEventExecutor__cctor_m7AD876F64AB941DD9AABAEE8B2A16CA87F09B57E (void);
// 0x0000008F System.Void GoogleMobileAds.Common.MobileAdsEventExecutor/<InvokeInUpdate>c__AnonStorey0::.ctor()
extern void U3CInvokeInUpdateU3Ec__AnonStorey0__ctor_mD6FCF6FC84A141A42B2D40632E39BA8F7028216F (void);
// 0x00000090 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor/<InvokeInUpdate>c__AnonStorey0::<>m__0()
extern void U3CInvokeInUpdateU3Ec__AnonStorey0_U3CU3Em__0_m3F739919615588E573F5D6B77761038D0B6D8807 (void);
static Il2CppMethodPointer s_methodPointers[144] = 
{
	AdErrorClientEventArgs__ctor_mE8C76834CB63E41EDC8F533A16BF1ADE185649BA,
	AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF,
	AdErrorClientEventArgs_set_AdErrorClient_mF1018FAC40B7908AADE3D91D83CD6AC3310CDC6E,
	AdInspectorErrorClientEventArgs__ctor_mD589A3C66FE286DEEB9EAF61585447A0AA9C90D2,
	AdInspectorErrorClientEventArgs_set_AdErrorClient_m30E0F50965C8A0C01F9CB9C81FE5076079ADB259,
	AppStateEventClient__ctor_m7033F1368759856CC91974B018E4004FC6CB36D6,
	AppStateEventClient_get_Instance_mD6A5472FC082A0EDFB6A26F7290A90E88FEB0E9B,
	AppStateEventClient_add_AppStateChanged_m5F95F6F5F802D911F18C9A0F6F9CEF5C6D41CD7C,
	AppStateEventClient_remove_AppStateChanged_mCCA2A08AE6710D6FA7AA2418B8CFDC071B189499,
	AppStateEventClient_OnApplicationPause_m85DD95B9D3B4E6722106A81E328690F7CAEBA8F8,
	AppStateEventClient_U3CAppStateChangedU3Em__0_m07E4B12AED15A4F1195CB9A239F6ED3432AE1F67,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LoadAdErrorClientEventArgs__ctor_m2D09A984965A5EDB0506CDAC47BE1675E80AE0AB,
	LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69,
	LoadAdErrorClientEventArgs_set_LoadAdErrorClient_m6E865CB1B347CEF0B75F55CE43EE3FDBF8353353,
	MobileAdsEventExecutor__ctor_m41D53858D8E26BD68A2C99A50E0A3ABAA1AD3EE4,
	MobileAdsEventExecutor_Initialize_m76A4DE8CDB2F00244DF305ACE913CDA65750ADEE,
	MobileAdsEventExecutor_IsActive_m739FAFCE76C28483EAD8FDF0D8004DE3FA8D7002,
	MobileAdsEventExecutor_Awake_mBED27401298ECAA85C578989510914356138EDAB,
	MobileAdsEventExecutor_ExecuteInUpdate_m4D7D863382B3DFA6F791CBCFD275B5F7EE4BA91E,
	MobileAdsEventExecutor_InvokeInUpdate_m707D79AE5D859144C46C142B97CA2C20BA53A171,
	MobileAdsEventExecutor_Update_mA2FA865A80DE36FC26CAD15783037324C74D5113,
	MobileAdsEventExecutor_OnDisable_m23177F7EA326224E1CCB135CC0CF96FE54CAC975,
	MobileAdsEventExecutor__cctor_m7AD876F64AB941DD9AABAEE8B2A16CA87F09B57E,
	U3CInvokeInUpdateU3Ec__AnonStorey0__ctor_mD6FCF6FC84A141A42B2D40632E39BA8F7028216F,
	U3CInvokeInUpdateU3Ec__AnonStorey0_U3CU3Em__0_m3F739919615588E573F5D6B77761038D0B6D8807,
};
static const int32_t s_InvokerIndices[144] = 
{
	6329,
	6204,
	5040,
	6329,
	5040,
	6329,
	9523,
	5040,
	5040,
	4945,
	9371,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6329,
	6204,
	5040,
	6329,
	9569,
	9489,
	6329,
	9375,
	9375,
	6329,
	6329,
	9569,
	6329,
	6329,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Common_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Common_CodeGenModule = 
{
	"GoogleMobileAds.Common.dll",
	144,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
