﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.Physics::OnSceneContactModify(UnityEngine.PhysicsScene,System.IntPtr,System.Int32,System.Boolean)
extern void Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D (void);
// 0x00000002 UnityEngine.PhysicsScene UnityEngine.Physics::get_defaultPhysicsScene()
extern void Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A (void);
// 0x00000003 System.Void UnityEngine.Physics::IgnoreCollision(UnityEngine.Collider,UnityEngine.Collider,System.Boolean)
extern void Physics_IgnoreCollision_mA8E5C54299FC47921E41BF864C7C2214621595D6 (void);
// 0x00000004 System.Void UnityEngine.Physics::IgnoreCollision(UnityEngine.Collider,UnityEngine.Collider)
extern void Physics_IgnoreCollision_mFBAAD9B91D488802086C1A1C96447CE4C869211D (void);
// 0x00000005 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D (void);
// 0x00000006 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377 (void);
// 0x00000007 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE (void);
// 0x00000008 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028 (void);
// 0x00000009 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688 (void);
// 0x0000000A System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (void);
// 0x0000000B System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
extern void Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33 (void);
// 0x0000000C System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903 (void);
// 0x0000000D System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8 (void);
// 0x0000000E System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,System.Single,System.Int32)
extern void Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50 (void);
// 0x0000000F System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,System.Single)
extern void Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488 (void);
// 0x00000010 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray)
extern void Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3 (void);
// 0x00000011 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB (void);
// 0x00000012 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025 (void);
// 0x00000013 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single)
extern void Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (void);
// 0x00000014 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&)
extern void Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5 (void);
// 0x00000015 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925 (void);
// 0x00000016 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Int32)
extern void Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6 (void);
// 0x00000017 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932 (void);
// 0x00000018 System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CapsuleCast_mAB6E13B795FAFAFE366DE88B8C7A31C54EAE9EF7 (void);
// 0x00000019 System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
extern void Physics_CapsuleCast_m0A540F025E5170C56348DBB377795CFA2EE9AFEE (void);
// 0x0000001A UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_RaycastAll(UnityEngine.PhysicsScene,UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074 (void);
// 0x0000001B UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34 (void);
// 0x0000001C UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51 (void);
// 0x0000001D UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E (void);
// 0x0000001E UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789 (void);
// 0x0000001F UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74 (void);
// 0x00000020 UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray,System.Single,System.Int32)
extern void Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87 (void);
// 0x00000021 UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray,System.Single)
extern void Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468 (void);
// 0x00000022 UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray)
extern void Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2 (void);
// 0x00000023 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE (void);
// 0x00000024 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC (void);
// 0x00000025 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single)
extern void Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C (void);
// 0x00000026 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[])
extern void Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E (void);
// 0x00000027 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836 (void);
// 0x00000028 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0 (void);
// 0x00000029 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single)
extern void Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9 (void);
// 0x0000002A System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[])
extern void Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460 (void);
// 0x0000002B UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B (void);
// 0x0000002C UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere(UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318 (void);
// 0x0000002D UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere(UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_OverlapSphere_m2D0C9BC78473512F1F89AE731FBAE1B734EDF3EE (void);
// 0x0000002E UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere(UnityEngine.Vector3,System.Single)
extern void Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69 (void);
// 0x0000002F System.Boolean UnityEngine.Physics::Query_ComputePenetration(UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Vector3&,System.Single&)
extern void Physics_Query_ComputePenetration_mFC4D885B9B0A1A511997F8F25D64117D075E3B88 (void);
// 0x00000030 System.Boolean UnityEngine.Physics::ComputePenetration(UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Vector3&,System.Single&)
extern void Physics_ComputePenetration_mA9AA5B3B6982BAC84467322616E8423CA4E91AFF (void);
// 0x00000031 System.Int32 UnityEngine.Physics::OverlapSphereNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83 (void);
// 0x00000032 System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C (void);
// 0x00000033 System.Void UnityEngine.Physics::get_defaultPhysicsScene_Injected(UnityEngine.PhysicsScene&)
extern void Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6 (void);
// 0x00000034 UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_RaycastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Internal_RaycastAll_Injected_mAFAA47E2224DEA0ABF1A2188A969E7A663E50C92 (void);
// 0x00000035 UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B (void);
// 0x00000036 System.Boolean UnityEngine.Physics::Query_ComputePenetration_Injected(UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,System.Single&)
extern void Physics_Query_ComputePenetration_Injected_mDDA9AC3B6EC05A824EE608809BD11266E4D529B2 (void);
// 0x00000037 System.Void UnityEngine.PhysicMaterial::.ctor()
extern void PhysicMaterial__ctor_mD8ECF21D92EBF6A8C5517E7EB4D2D089B4F78D3E (void);
// 0x00000038 System.Void UnityEngine.PhysicMaterial::Internal_CreateDynamicsMaterial(UnityEngine.PhysicMaterial,System.String)
extern void PhysicMaterial_Internal_CreateDynamicsMaterial_m7A2577629C56F741F3B03B7E859611A20241F3C1 (void);
// 0x00000039 UnityEngine.Collider UnityEngine.RaycastHit::get_collider()
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (void);
// 0x0000003A UnityEngine.Vector3 UnityEngine.RaycastHit::get_point()
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (void);
// 0x0000003B System.Void UnityEngine.RaycastHit::set_point(UnityEngine.Vector3)
extern void RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11 (void);
// 0x0000003C UnityEngine.Vector3 UnityEngine.RaycastHit::get_normal()
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (void);
// 0x0000003D System.Void UnityEngine.RaycastHit::set_normal(UnityEngine.Vector3)
extern void RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B (void);
// 0x0000003E System.Single UnityEngine.RaycastHit::get_distance()
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (void);
// 0x0000003F System.Void UnityEngine.RaycastHit::set_distance(System.Single)
extern void RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC (void);
// 0x00000040 UnityEngine.Transform UnityEngine.RaycastHit::get_transform()
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155 (void);
// 0x00000041 UnityEngine.Rigidbody UnityEngine.RaycastHit::get_rigidbody()
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005 (void);
// 0x00000042 UnityEngine.Vector3 UnityEngine.Rigidbody::get_velocity()
extern void Rigidbody_get_velocity_mAE331303E7214402C93E2183D0AA1198F425F843 (void);
// 0x00000043 System.Void UnityEngine.Rigidbody::set_velocity(UnityEngine.Vector3)
extern void Rigidbody_set_velocity_mE4031DF1C2C1CCE889F2AC9D8871D83795BB0D62 (void);
// 0x00000044 UnityEngine.Vector3 UnityEngine.Rigidbody::get_angularVelocity()
extern void Rigidbody_get_angularVelocity_m4EACCFCF15CA441CCD53B24322C2E7B8EEBDF6A8 (void);
// 0x00000045 System.Void UnityEngine.Rigidbody::set_angularVelocity(UnityEngine.Vector3)
extern void Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0 (void);
// 0x00000046 System.Single UnityEngine.Rigidbody::get_drag()
extern void Rigidbody_get_drag_m03B87FE60D5ABB7B937993112982C75080929D54 (void);
// 0x00000047 System.Void UnityEngine.Rigidbody::set_drag(System.Single)
extern void Rigidbody_set_drag_m9E9F375A26A8F3D1AABCEB015E41696F39088EE0 (void);
// 0x00000048 System.Void UnityEngine.Rigidbody::set_angularDrag(System.Single)
extern void Rigidbody_set_angularDrag_m4193B04EEFCA831DB99E29E98F778957557F130C (void);
// 0x00000049 System.Single UnityEngine.Rigidbody::get_mass()
extern void Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054 (void);
// 0x0000004A System.Void UnityEngine.Rigidbody::set_mass(System.Single)
extern void Rigidbody_set_mass_mC7F886DEDB57C742A16F8B6B779F69AFE164CA4B (void);
// 0x0000004B System.Void UnityEngine.Rigidbody::set_useGravity(System.Boolean)
extern void Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389 (void);
// 0x0000004C System.Boolean UnityEngine.Rigidbody::get_isKinematic()
extern void Rigidbody_get_isKinematic_mC20906CA5A89983DE06EAC6E3AFC5BC012F90CA1 (void);
// 0x0000004D System.Void UnityEngine.Rigidbody::set_isKinematic(System.Boolean)
extern void Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED (void);
// 0x0000004E System.Void UnityEngine.Rigidbody::set_freezeRotation(System.Boolean)
extern void Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC (void);
// 0x0000004F System.Void UnityEngine.Rigidbody::set_collisionDetectionMode(UnityEngine.CollisionDetectionMode)
extern void Rigidbody_set_collisionDetectionMode_m70A22E9878027BF6D3D7E851A43A8E32B8E02343 (void);
// 0x00000050 System.Void UnityEngine.Rigidbody::set_centerOfMass(UnityEngine.Vector3)
extern void Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188 (void);
// 0x00000051 System.Void UnityEngine.Rigidbody::set_detectCollisions(System.Boolean)
extern void Rigidbody_set_detectCollisions_m42A50DFACA7709DA1F87BCB9DC0BDA00720C80CF (void);
// 0x00000052 UnityEngine.Vector3 UnityEngine.Rigidbody::get_position()
extern void Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (void);
// 0x00000053 UnityEngine.Quaternion UnityEngine.Rigidbody::get_rotation()
extern void Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1 (void);
// 0x00000054 System.Void UnityEngine.Rigidbody::set_rotation(UnityEngine.Quaternion)
extern void Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D (void);
// 0x00000055 System.Void UnityEngine.Rigidbody::set_interpolation(UnityEngine.RigidbodyInterpolation)
extern void Rigidbody_set_interpolation_mC7D39114A7AC6ED0AB2B40FECA4E2ED3C1D7603C (void);
// 0x00000056 System.Void UnityEngine.Rigidbody::set_maxAngularVelocity(System.Single)
extern void Rigidbody_set_maxAngularVelocity_m26E48B1DC6B9F8DBB81EE0681ABEB3AB255FC3F6 (void);
// 0x00000057 System.Void UnityEngine.Rigidbody::MovePosition(UnityEngine.Vector3)
extern void Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9 (void);
// 0x00000058 System.Void UnityEngine.Rigidbody::MoveRotation(UnityEngine.Quaternion)
extern void Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D (void);
// 0x00000059 System.Void UnityEngine.Rigidbody::ResetInertiaTensor()
extern void Rigidbody_ResetInertiaTensor_m34020552CA2D42DEA3E01562641A9B292848BD01 (void);
// 0x0000005A System.Void UnityEngine.Rigidbody::AddForce(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC (void);
// 0x0000005B System.Void UnityEngine.Rigidbody::AddForce(UnityEngine.Vector3)
extern void Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198 (void);
// 0x0000005C System.Void UnityEngine.Rigidbody::AddRelativeForce(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeForce_mAF5EA6C0A2417A4C72AF31538D66EB9612CB6543 (void);
// 0x0000005D System.Void UnityEngine.Rigidbody::AddRelativeTorque(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeTorque_m98DD3E53803D7E5BA726CC98FBFA58C2350F2233 (void);
// 0x0000005E System.Void UnityEngine.Rigidbody::AddRelativeTorque(UnityEngine.Vector3)
extern void Rigidbody_AddRelativeTorque_m8A4883737B7F8BDC0B25144986F74C4B9F789311 (void);
// 0x0000005F System.Void UnityEngine.Rigidbody::AddForceAtPosition(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C (void);
// 0x00000060 System.Void UnityEngine.Rigidbody::AddForceAtPosition(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965 (void);
// 0x00000061 System.Void UnityEngine.Rigidbody::.ctor()
extern void Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07 (void);
// 0x00000062 System.Void UnityEngine.Rigidbody::get_velocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_velocity_Injected_mFD6FCA2857D9953AA953DB9AAF26A88CA881171C (void);
// 0x00000063 System.Void UnityEngine.Rigidbody::set_velocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_velocity_Injected_m41B399E90D6AA49BABD3C178B3183AD3BBB4EAC4 (void);
// 0x00000064 System.Void UnityEngine.Rigidbody::get_angularVelocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_angularVelocity_Injected_m1F0D38AD14491E05E18C0E2C043F777FADC588BC (void);
// 0x00000065 System.Void UnityEngine.Rigidbody::set_angularVelocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_angularVelocity_Injected_mD7EA47CB618918BD45985951E5A5388853975E68 (void);
// 0x00000066 System.Void UnityEngine.Rigidbody::set_centerOfMass_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_centerOfMass_Injected_mF33B1D20FF435377B48235CDD322478131AE4B1B (void);
// 0x00000067 System.Void UnityEngine.Rigidbody::get_position_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_position_Injected_m12A715C52CD3C7F66125950D7AB6ECFCF4336626 (void);
// 0x00000068 System.Void UnityEngine.Rigidbody::get_rotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_get_rotation_Injected_m38431B37B78B4DF59B59F9CB7E609820430051F8 (void);
// 0x00000069 System.Void UnityEngine.Rigidbody::set_rotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_set_rotation_Injected_mDE5B37E97D8FE16D9AD8FC6B9213A106B28589D7 (void);
// 0x0000006A System.Void UnityEngine.Rigidbody::MovePosition_Injected(UnityEngine.Vector3&)
extern void Rigidbody_MovePosition_Injected_mF2CDF14960A920DCDDEAFA49A7E066A2FF021E37 (void);
// 0x0000006B System.Void UnityEngine.Rigidbody::MoveRotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_MoveRotation_Injected_m75B6A86B8BE8D68714CA5356DDCC11D24B96B505 (void);
// 0x0000006C System.Void UnityEngine.Rigidbody::AddForce_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddForce_Injected_m094E54DEA6CEAEA340F053D077CDF0753900F48E (void);
// 0x0000006D System.Void UnityEngine.Rigidbody::AddRelativeForce_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeForce_Injected_m9BD7D9D36B62C306CA7D30CEB6BDFCDEDDFF9DF3 (void);
// 0x0000006E System.Void UnityEngine.Rigidbody::AddRelativeTorque_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeTorque_Injected_m4FCDB6F9DA07C7C64925943F79670E967B1B8FA6 (void);
// 0x0000006F System.Void UnityEngine.Rigidbody::AddForceAtPosition_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddForceAtPosition_Injected_m24F286471C9928629A50A5C1A0DF22698172438C (void);
// 0x00000070 System.Void UnityEngine.MeshCollider::set_sharedMesh(UnityEngine.Mesh)
extern void MeshCollider_set_sharedMesh_m05F87B9AC04139285EBBCC159F77B51C6940E79C (void);
// 0x00000071 System.Void UnityEngine.MeshCollider::set_convex(System.Boolean)
extern void MeshCollider_set_convex_m20482D687240D0921BA76B77983403E55A2E3CE1 (void);
// 0x00000072 System.Void UnityEngine.BoxCollider::set_center(UnityEngine.Vector3)
extern void BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0 (void);
// 0x00000073 UnityEngine.Vector3 UnityEngine.BoxCollider::get_size()
extern void BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E (void);
// 0x00000074 System.Void UnityEngine.BoxCollider::set_size(UnityEngine.Vector3)
extern void BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A (void);
// 0x00000075 System.Void UnityEngine.BoxCollider::set_center_Injected(UnityEngine.Vector3&)
extern void BoxCollider_set_center_Injected_mF2FF6FB33F950F7329395C6EC97E843ACDA52D8E (void);
// 0x00000076 System.Void UnityEngine.BoxCollider::get_size_Injected(UnityEngine.Vector3&)
extern void BoxCollider_get_size_Injected_m4F20D7B3D8FB4360C9E2986FB6A8CC66ABC89511 (void);
// 0x00000077 System.Void UnityEngine.BoxCollider::set_size_Injected(UnityEngine.Vector3&)
extern void BoxCollider_set_size_Injected_mFA7F15E94435D42A70D7C8543CEC7AF4F6D55343 (void);
// 0x00000078 UnityEngine.ContactPoint[] UnityEngine.Collision::GetContacts_Internal()
extern void Collision_GetContacts_Internal_m925C8F6272E55A551B54B1F96B1994F05FDB9DF5 (void);
// 0x00000079 UnityEngine.Vector3 UnityEngine.Collision::get_relativeVelocity()
extern void Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0 (void);
// 0x0000007A UnityEngine.Rigidbody UnityEngine.Collision::get_rigidbody()
extern void Collision_get_rigidbody_mD7A14B9C8AA98352340D2AB0097FC3A424FBB81B (void);
// 0x0000007B UnityEngine.Collider UnityEngine.Collision::get_collider()
extern void Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D (void);
// 0x0000007C UnityEngine.GameObject UnityEngine.Collision::get_gameObject()
extern void Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E (void);
// 0x0000007D System.Int32 UnityEngine.Collision::get_contactCount()
extern void Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150 (void);
// 0x0000007E UnityEngine.ContactPoint[] UnityEngine.Collision::get_contacts()
extern void Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473 (void);
// 0x0000007F UnityEngine.ContactPoint UnityEngine.Collision::GetContact(System.Int32)
extern void Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD (void);
// 0x00000080 System.Int32 UnityEngine.Collision::GetContacts(UnityEngine.ContactPoint[])
extern void Collision_GetContacts_m3E2B52E011083420A9A1F5E551798F9483622292 (void);
// 0x00000081 UnityEngine.Vector3 UnityEngine.Collision::get_impulse()
extern void Collision_get_impulse_mBA2EDD39B7F495FF335FB867B244253602C7EF5D (void);
// 0x00000082 System.Boolean UnityEngine.Collider::get_enabled()
extern void Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B (void);
// 0x00000083 System.Void UnityEngine.Collider::set_enabled(System.Boolean)
extern void Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70 (void);
// 0x00000084 UnityEngine.Rigidbody UnityEngine.Collider::get_attachedRigidbody()
extern void Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD (void);
// 0x00000085 System.Boolean UnityEngine.Collider::get_isTrigger()
extern void Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87 (void);
// 0x00000086 System.Void UnityEngine.Collider::set_isTrigger(System.Boolean)
extern void Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78 (void);
// 0x00000087 UnityEngine.Vector3 UnityEngine.Collider::ClosestPoint(UnityEngine.Vector3)
extern void Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB (void);
// 0x00000088 UnityEngine.Bounds UnityEngine.Collider::get_bounds()
extern void Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4 (void);
// 0x00000089 UnityEngine.PhysicMaterial UnityEngine.Collider::get_sharedMaterial()
extern void Collider_get_sharedMaterial_m238C1D9D4B2B1F02876C610E049C7A5ECCDC07AC (void);
// 0x0000008A UnityEngine.RaycastHit UnityEngine.Collider::Raycast(UnityEngine.Ray,System.Single,System.Boolean&)
extern void Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C (void);
// 0x0000008B System.Boolean UnityEngine.Collider::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single)
extern void Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938 (void);
// 0x0000008C System.Void UnityEngine.Collider::Internal_ClosestPointOnBounds(UnityEngine.Vector3,UnityEngine.Vector3&,System.Single&)
extern void Collider_Internal_ClosestPointOnBounds_m87BD13A92D4239E7BA08C0417197DFC8D4E5DB7E (void);
// 0x0000008D UnityEngine.Vector3 UnityEngine.Collider::ClosestPointOnBounds(UnityEngine.Vector3)
extern void Collider_ClosestPointOnBounds_mBF2F0C0E76C5F11AED801931D780823A94630952 (void);
// 0x0000008E System.Void UnityEngine.Collider::.ctor()
extern void Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA (void);
// 0x0000008F System.Void UnityEngine.Collider::ClosestPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Collider_ClosestPoint_Injected_m4E218A16FABAA4615270B9CD82DC66E130AAFE77 (void);
// 0x00000090 System.Void UnityEngine.Collider::get_bounds_Injected(UnityEngine.Bounds&)
extern void Collider_get_bounds_Injected_m1BDB8DBC0BC2BFC51D4A185C494EDB0997B93A43 (void);
// 0x00000091 System.Void UnityEngine.Collider::Raycast_Injected(UnityEngine.Ray&,System.Single,System.Boolean&,UnityEngine.RaycastHit&)
extern void Collider_Raycast_Injected_mAFAD355B658765116985B737217587C68BF257A3 (void);
// 0x00000092 System.Void UnityEngine.Collider::Internal_ClosestPointOnBounds_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single&)
extern void Collider_Internal_ClosestPointOnBounds_Injected_mC77F9140817CA77A46BB8672B6DD388CC3BD2B7E (void);
// 0x00000093 System.Void UnityEngine.CapsuleCollider::set_center(UnityEngine.Vector3)
extern void CapsuleCollider_set_center_m242D92DAEF25887C6A87A0777E4E624C0A431A2E (void);
// 0x00000094 System.Void UnityEngine.CapsuleCollider::set_radius(System.Single)
extern void CapsuleCollider_set_radius_mB301C0086FE0D251683512184B0F6DDE264BA985 (void);
// 0x00000095 System.Void UnityEngine.CapsuleCollider::set_height(System.Single)
extern void CapsuleCollider_set_height_m5DAE3DC5AD851E30C5A29AC7A22F36BE1E205BBB (void);
// 0x00000096 System.Void UnityEngine.CapsuleCollider::set_center_Injected(UnityEngine.Vector3&)
extern void CapsuleCollider_set_center_Injected_m2C7FB04A185C5A1C8B3AFCFBB2923C8E6966C132 (void);
// 0x00000097 System.Void UnityEngine.SphereCollider::set_radius(System.Single)
extern void SphereCollider_set_radius_m6119FE18C6739B077AB17334B1B53984911017FF (void);
// 0x00000098 UnityEngine.Rigidbody UnityEngine.Joint::get_connectedBody()
extern void Joint_get_connectedBody_mE39E0AC9869325CD018B9ADB383B6BE01D497B59 (void);
// 0x00000099 System.Void UnityEngine.Joint::set_connectedBody(UnityEngine.Rigidbody)
extern void Joint_set_connectedBody_mE9E631476E9D4264E8DC0D6307146F5EB64D3ED4 (void);
// 0x0000009A UnityEngine.Vector3 UnityEngine.Joint::get_axis()
extern void Joint_get_axis_mEDF8FE710E08CED9EA69A0369A075B77FF0BE79C (void);
// 0x0000009B System.Void UnityEngine.Joint::set_axis(UnityEngine.Vector3)
extern void Joint_set_axis_m3C64D93F04DA043D6B02160F9034907BACC06800 (void);
// 0x0000009C UnityEngine.Vector3 UnityEngine.Joint::get_anchor()
extern void Joint_get_anchor_m1CDF56CF0BD9773E9923D777EA24B2102DEDB79D (void);
// 0x0000009D System.Void UnityEngine.Joint::set_anchor(UnityEngine.Vector3)
extern void Joint_set_anchor_m89447EF25E0FC6DB9D22562BAF3BDA3E6D04029C (void);
// 0x0000009E System.Void UnityEngine.Joint::set_connectedAnchor(UnityEngine.Vector3)
extern void Joint_set_connectedAnchor_m2A40C3C4FB583E9DBC020222A21F577C066D5D90 (void);
// 0x0000009F System.Void UnityEngine.Joint::set_autoConfigureConnectedAnchor(System.Boolean)
extern void Joint_set_autoConfigureConnectedAnchor_mF61D716174DE67CD94FF042881E9052357679E02 (void);
// 0x000000A0 System.Void UnityEngine.Joint::set_connectedMassScale(System.Single)
extern void Joint_set_connectedMassScale_m6F7D8FEFAD6BFDC7177D9D06DBCCDCC288C4475D (void);
// 0x000000A1 System.Void UnityEngine.Joint::get_axis_Injected(UnityEngine.Vector3&)
extern void Joint_get_axis_Injected_m85FD31C188CC3C7BC266AC7E5E1DD5B14E656C34 (void);
// 0x000000A2 System.Void UnityEngine.Joint::set_axis_Injected(UnityEngine.Vector3&)
extern void Joint_set_axis_Injected_m013FB85E34CB410702FC87EAB54B856CF180B505 (void);
// 0x000000A3 System.Void UnityEngine.Joint::get_anchor_Injected(UnityEngine.Vector3&)
extern void Joint_get_anchor_Injected_mE20087A50431397C2C20E031DD98733425C2B445 (void);
// 0x000000A4 System.Void UnityEngine.Joint::set_anchor_Injected(UnityEngine.Vector3&)
extern void Joint_set_anchor_Injected_m640F53862ECAB8D8163B378E54FB6E2194BB2AF7 (void);
// 0x000000A5 System.Void UnityEngine.Joint::set_connectedAnchor_Injected(UnityEngine.Vector3&)
extern void Joint_set_connectedAnchor_Injected_mE4275DA473C8A61B37A1639E2352A09DCE2A4198 (void);
// 0x000000A6 UnityEngine.ConfigurableJointMotion UnityEngine.ConfigurableJoint::get_xMotion()
extern void ConfigurableJoint_get_xMotion_m5518BED4E7F558174DD6B8F313CE7D125E1A1334 (void);
// 0x000000A7 System.Void UnityEngine.ConfigurableJoint::set_xMotion(UnityEngine.ConfigurableJointMotion)
extern void ConfigurableJoint_set_xMotion_mBDA7D8874899D2C20E1B1BA45944AA357CDFBDCC (void);
// 0x000000A8 UnityEngine.ConfigurableJointMotion UnityEngine.ConfigurableJoint::get_yMotion()
extern void ConfigurableJoint_get_yMotion_m4F0FA7246F1BAA1AC0BD7E86504CBE48D47CA005 (void);
// 0x000000A9 System.Void UnityEngine.ConfigurableJoint::set_yMotion(UnityEngine.ConfigurableJointMotion)
extern void ConfigurableJoint_set_yMotion_m597259075C915C848E87B3A9CBBDA0762B5A2563 (void);
// 0x000000AA UnityEngine.ConfigurableJointMotion UnityEngine.ConfigurableJoint::get_zMotion()
extern void ConfigurableJoint_get_zMotion_mC0AEE3A95069E7C0F451B71E356DCA387CDF4AEF (void);
// 0x000000AB System.Void UnityEngine.ConfigurableJoint::set_zMotion(UnityEngine.ConfigurableJointMotion)
extern void ConfigurableJoint_set_zMotion_m3479D7843AC2F91AA958F7B18AFCE3730842AFA8 (void);
// 0x000000AC UnityEngine.ConfigurableJointMotion UnityEngine.ConfigurableJoint::get_angularXMotion()
extern void ConfigurableJoint_get_angularXMotion_m5CBF8FC37A7CF94AF97583E5C1551BF7859B9258 (void);
// 0x000000AD System.Void UnityEngine.ConfigurableJoint::set_angularXMotion(UnityEngine.ConfigurableJointMotion)
extern void ConfigurableJoint_set_angularXMotion_m1691CF3456A38996918D077FD6FC2CBEEFB0C9D5 (void);
// 0x000000AE UnityEngine.ConfigurableJointMotion UnityEngine.ConfigurableJoint::get_angularYMotion()
extern void ConfigurableJoint_get_angularYMotion_m1A08889BB8666184FED3CF1275444D5BA70ACE5F (void);
// 0x000000AF System.Void UnityEngine.ConfigurableJoint::set_angularYMotion(UnityEngine.ConfigurableJointMotion)
extern void ConfigurableJoint_set_angularYMotion_m21858D3799D8EED8AB21C46DF84927B10F1414D7 (void);
// 0x000000B0 UnityEngine.ConfigurableJointMotion UnityEngine.ConfigurableJoint::get_angularZMotion()
extern void ConfigurableJoint_get_angularZMotion_m515347C78E06D82BE0AD254824E6F134E46CC58C (void);
// 0x000000B1 System.Void UnityEngine.ConfigurableJoint::set_angularZMotion(UnityEngine.ConfigurableJointMotion)
extern void ConfigurableJoint_set_angularZMotion_m485474C654E903BBAE579F631BBD6C737B47394B (void);
// 0x000000B2 UnityEngine.SoftJointLimit UnityEngine.ConfigurableJoint::get_linearLimit()
extern void ConfigurableJoint_get_linearLimit_m35456F7AF48ACA69E79D1EFE14578730BAA6A98A (void);
// 0x000000B3 System.Void UnityEngine.ConfigurableJoint::set_linearLimit(UnityEngine.SoftJointLimit)
extern void ConfigurableJoint_set_linearLimit_m57EE251D8642A4ADED96D77555B5948AF7F4AA9E (void);
// 0x000000B4 UnityEngine.SoftJointLimit UnityEngine.ConfigurableJoint::get_lowAngularXLimit()
extern void ConfigurableJoint_get_lowAngularXLimit_mE5EA802AA80E71542FDE6DD911364FC24297F4BD (void);
// 0x000000B5 System.Void UnityEngine.ConfigurableJoint::set_lowAngularXLimit(UnityEngine.SoftJointLimit)
extern void ConfigurableJoint_set_lowAngularXLimit_m6424314936986525CEBCE5C16EBA69B5129BBD5A (void);
// 0x000000B6 UnityEngine.SoftJointLimit UnityEngine.ConfigurableJoint::get_highAngularXLimit()
extern void ConfigurableJoint_get_highAngularXLimit_m978FF09CAF3E87AFA149752594ADD09FB9EA1ACE (void);
// 0x000000B7 System.Void UnityEngine.ConfigurableJoint::set_highAngularXLimit(UnityEngine.SoftJointLimit)
extern void ConfigurableJoint_set_highAngularXLimit_m96373EE2554934636E127E345F91306844177294 (void);
// 0x000000B8 UnityEngine.SoftJointLimit UnityEngine.ConfigurableJoint::get_angularYLimit()
extern void ConfigurableJoint_get_angularYLimit_mCC629F60D5650EF0F8F49FFB5DEE4052F687CA47 (void);
// 0x000000B9 System.Void UnityEngine.ConfigurableJoint::set_angularYLimit(UnityEngine.SoftJointLimit)
extern void ConfigurableJoint_set_angularYLimit_mF819FB8C5F17C9737EC0BA5A3EAAC5245AE57A08 (void);
// 0x000000BA UnityEngine.SoftJointLimit UnityEngine.ConfigurableJoint::get_angularZLimit()
extern void ConfigurableJoint_get_angularZLimit_m3F1975F6CAFD784F4F0881CB00D6E266CCE2658B (void);
// 0x000000BB System.Void UnityEngine.ConfigurableJoint::set_angularZLimit(UnityEngine.SoftJointLimit)
extern void ConfigurableJoint_set_angularZLimit_mCB9FEE0CAF97A1A278BDCD127C86DDD26CDBBC70 (void);
// 0x000000BC System.Void UnityEngine.ConfigurableJoint::set_configuredInWorldSpace(System.Boolean)
extern void ConfigurableJoint_set_configuredInWorldSpace_mD1FB99B42E24A0CABF43B4470E6F0C92BCCC2450 (void);
// 0x000000BD System.Void UnityEngine.ConfigurableJoint::get_linearLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_get_linearLimit_Injected_mAB06629F3F0FF14825C71BC77B8D1856836E6329 (void);
// 0x000000BE System.Void UnityEngine.ConfigurableJoint::set_linearLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_set_linearLimit_Injected_mB7C9F6674B0E76154DFCFAB457E68264FC8ACA88 (void);
// 0x000000BF System.Void UnityEngine.ConfigurableJoint::get_lowAngularXLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_get_lowAngularXLimit_Injected_m65F3B2184E5BE0B93B9702DE371B50B727050C60 (void);
// 0x000000C0 System.Void UnityEngine.ConfigurableJoint::set_lowAngularXLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_set_lowAngularXLimit_Injected_m424F97E98AB918627E19CFA7FED1C4236DBFDD61 (void);
// 0x000000C1 System.Void UnityEngine.ConfigurableJoint::get_highAngularXLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_get_highAngularXLimit_Injected_m6672C8D546D7DCBD8E67AB9CF91AEB3FB76E72AC (void);
// 0x000000C2 System.Void UnityEngine.ConfigurableJoint::set_highAngularXLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_set_highAngularXLimit_Injected_m7C31099A209513CAE46018D03F195D33CFCFD501 (void);
// 0x000000C3 System.Void UnityEngine.ConfigurableJoint::get_angularYLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_get_angularYLimit_Injected_m7B5BF8EE2D2AFC977D6E192307EE9A8DC14A6C47 (void);
// 0x000000C4 System.Void UnityEngine.ConfigurableJoint::set_angularYLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_set_angularYLimit_Injected_m4DA460D042653C9FB41E5E2A3AC4B0F18F6D76FE (void);
// 0x000000C5 System.Void UnityEngine.ConfigurableJoint::get_angularZLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_get_angularZLimit_Injected_m3E5B226EC970A3A74652C9AE9B9F1BF07B8A6AE0 (void);
// 0x000000C6 System.Void UnityEngine.ConfigurableJoint::set_angularZLimit_Injected(UnityEngine.SoftJointLimit&)
extern void ConfigurableJoint_set_angularZLimit_Injected_m781FCA9CA40DA1F5B984BDCBA4DBE0F4309198FC (void);
// 0x000000C7 UnityEngine.Vector3 UnityEngine.ContactPoint::get_point()
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208 (void);
// 0x000000C8 UnityEngine.Vector3 UnityEngine.ContactPoint::get_normal()
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6 (void);
// 0x000000C9 System.String UnityEngine.PhysicsScene::ToString()
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7 (void);
// 0x000000CA System.Int32 UnityEngine.PhysicsScene::GetHashCode()
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814 (void);
// 0x000000CB System.Boolean UnityEngine.PhysicsScene::Equals(System.Object)
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3 (void);
// 0x000000CC System.Boolean UnityEngine.PhysicsScene::Equals(UnityEngine.PhysicsScene)
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1 (void);
// 0x000000CD System.Boolean UnityEngine.PhysicsScene::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730 (void);
// 0x000000CE System.Boolean UnityEngine.PhysicsScene::Internal_RaycastTest(UnityEngine.PhysicsScene,UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0 (void);
// 0x000000CF System.Boolean UnityEngine.PhysicsScene::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE (void);
// 0x000000D0 System.Boolean UnityEngine.PhysicsScene::Internal_Raycast(UnityEngine.PhysicsScene,UnityEngine.Ray,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74 (void);
// 0x000000D1 System.Int32 UnityEngine.PhysicsScene::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3 (void);
// 0x000000D2 System.Int32 UnityEngine.PhysicsScene::Internal_RaycastNonAlloc(UnityEngine.PhysicsScene,UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB (void);
// 0x000000D3 System.Boolean UnityEngine.PhysicsScene::Query_CapsuleCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_CapsuleCast_m6871258F7BAA2370C7BA7334E2EE6752EFBD723F (void);
// 0x000000D4 System.Boolean UnityEngine.PhysicsScene::Internal_CapsuleCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_CapsuleCast_mFB5002955B349D73D842F47BB3DBCDAE453FF2F0 (void);
// 0x000000D5 System.Boolean UnityEngine.PhysicsScene::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35 (void);
// 0x000000D6 System.Int32 UnityEngine.PhysicsScene::Internal_SphereCastNonAlloc(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F (void);
// 0x000000D7 System.Int32 UnityEngine.PhysicsScene::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F (void);
// 0x000000D8 System.Int32 UnityEngine.PhysicsScene::OverlapSphereNonAlloc_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1 (void);
// 0x000000D9 System.Int32 UnityEngine.PhysicsScene::OverlapSphere(UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01 (void);
// 0x000000DA System.Boolean UnityEngine.PhysicsScene::Internal_RaycastTest_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62 (void);
// 0x000000DB System.Boolean UnityEngine.PhysicsScene::Internal_Raycast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D (void);
// 0x000000DC System.Int32 UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_mD6BA34F06BE743B2CBF46AA82EE6DDC9CCEC0F27 (void);
// 0x000000DD System.Boolean UnityEngine.PhysicsScene::Query_CapsuleCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_CapsuleCast_Injected_m3A9CC7AB617D70C2284C49E982163F37EB0B27B0 (void);
// 0x000000DE System.Int32 UnityEngine.PhysicsScene::Internal_SphereCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_SphereCastNonAlloc_Injected_m8B19C4FB753820C4D4952D6BEB59B7044F7C7394 (void);
// 0x000000DF System.Int32 UnityEngine.PhysicsScene::OverlapSphereNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737 (void);
// 0x000000E0 System.Void UnityEngine.WheelFrictionCurve::set_extremumSlip(System.Single)
extern void WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E (void);
// 0x000000E1 System.Single UnityEngine.WheelFrictionCurve::get_extremumValue()
extern void WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34 (void);
// 0x000000E2 System.Void UnityEngine.WheelFrictionCurve::set_extremumValue(System.Single)
extern void WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E (void);
// 0x000000E3 System.Void UnityEngine.WheelFrictionCurve::set_asymptoteSlip(System.Single)
extern void WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C (void);
// 0x000000E4 System.Single UnityEngine.WheelFrictionCurve::get_asymptoteValue()
extern void WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F (void);
// 0x000000E5 System.Void UnityEngine.WheelFrictionCurve::set_asymptoteValue(System.Single)
extern void WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43 (void);
// 0x000000E6 System.Single UnityEngine.WheelFrictionCurve::get_stiffness()
extern void WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6 (void);
// 0x000000E7 System.Void UnityEngine.WheelFrictionCurve::set_stiffness(System.Single)
extern void WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5 (void);
static Il2CppMethodPointer s_methodPointers[231] = 
{
	Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D,
	Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A,
	Physics_IgnoreCollision_mA8E5C54299FC47921E41BF864C7C2214621595D6,
	Physics_IgnoreCollision_mFBAAD9B91D488802086C1A1C96447CE4C869211D,
	Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D,
	Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377,
	Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE,
	Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028,
	Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688,
	Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12,
	Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33,
	Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903,
	Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8,
	Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50,
	Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488,
	Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3,
	Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB,
	Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025,
	Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685,
	Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5,
	Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925,
	Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6,
	Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932,
	Physics_CapsuleCast_mAB6E13B795FAFAFE366DE88B8C7A31C54EAE9EF7,
	Physics_CapsuleCast_m0A540F025E5170C56348DBB377795CFA2EE9AFEE,
	Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074,
	Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34,
	Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51,
	Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E,
	Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789,
	Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74,
	Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87,
	Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468,
	Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2,
	Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE,
	Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC,
	Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C,
	Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E,
	Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836,
	Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0,
	Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9,
	Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460,
	Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B,
	Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318,
	Physics_OverlapSphere_m2D0C9BC78473512F1F89AE731FBAE1B734EDF3EE,
	Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69,
	Physics_Query_ComputePenetration_mFC4D885B9B0A1A511997F8F25D64117D075E3B88,
	Physics_ComputePenetration_mA9AA5B3B6982BAC84467322616E8423CA4E91AFF,
	Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83,
	Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C,
	Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6,
	Physics_Internal_RaycastAll_Injected_mAFAA47E2224DEA0ABF1A2188A969E7A663E50C92,
	Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B,
	Physics_Query_ComputePenetration_Injected_mDDA9AC3B6EC05A824EE608809BD11266E4D529B2,
	PhysicMaterial__ctor_mD8ECF21D92EBF6A8C5517E7EB4D2D089B4F78D3E,
	PhysicMaterial_Internal_CreateDynamicsMaterial_m7A2577629C56F741F3B03B7E859611A20241F3C1,
	RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D,
	RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39,
	RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11,
	RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5,
	RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B,
	RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78,
	RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC,
	RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155,
	RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005,
	Rigidbody_get_velocity_mAE331303E7214402C93E2183D0AA1198F425F843,
	Rigidbody_set_velocity_mE4031DF1C2C1CCE889F2AC9D8871D83795BB0D62,
	Rigidbody_get_angularVelocity_m4EACCFCF15CA441CCD53B24322C2E7B8EEBDF6A8,
	Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0,
	Rigidbody_get_drag_m03B87FE60D5ABB7B937993112982C75080929D54,
	Rigidbody_set_drag_m9E9F375A26A8F3D1AABCEB015E41696F39088EE0,
	Rigidbody_set_angularDrag_m4193B04EEFCA831DB99E29E98F778957557F130C,
	Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054,
	Rigidbody_set_mass_mC7F886DEDB57C742A16F8B6B779F69AFE164CA4B,
	Rigidbody_set_useGravity_m1B1B22E093F9DC92D7BEEBBE6B02642B3B6C4389,
	Rigidbody_get_isKinematic_mC20906CA5A89983DE06EAC6E3AFC5BC012F90CA1,
	Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED,
	Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC,
	Rigidbody_set_collisionDetectionMode_m70A22E9878027BF6D3D7E851A43A8E32B8E02343,
	Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188,
	Rigidbody_set_detectCollisions_m42A50DFACA7709DA1F87BCB9DC0BDA00720C80CF,
	Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691,
	Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1,
	Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D,
	Rigidbody_set_interpolation_mC7D39114A7AC6ED0AB2B40FECA4E2ED3C1D7603C,
	Rigidbody_set_maxAngularVelocity_m26E48B1DC6B9F8DBB81EE0681ABEB3AB255FC3F6,
	Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9,
	Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D,
	Rigidbody_ResetInertiaTensor_m34020552CA2D42DEA3E01562641A9B292848BD01,
	Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC,
	Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198,
	Rigidbody_AddRelativeForce_mAF5EA6C0A2417A4C72AF31538D66EB9612CB6543,
	Rigidbody_AddRelativeTorque_m98DD3E53803D7E5BA726CC98FBFA58C2350F2233,
	Rigidbody_AddRelativeTorque_m8A4883737B7F8BDC0B25144986F74C4B9F789311,
	Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C,
	Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965,
	Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07,
	Rigidbody_get_velocity_Injected_mFD6FCA2857D9953AA953DB9AAF26A88CA881171C,
	Rigidbody_set_velocity_Injected_m41B399E90D6AA49BABD3C178B3183AD3BBB4EAC4,
	Rigidbody_get_angularVelocity_Injected_m1F0D38AD14491E05E18C0E2C043F777FADC588BC,
	Rigidbody_set_angularVelocity_Injected_mD7EA47CB618918BD45985951E5A5388853975E68,
	Rigidbody_set_centerOfMass_Injected_mF33B1D20FF435377B48235CDD322478131AE4B1B,
	Rigidbody_get_position_Injected_m12A715C52CD3C7F66125950D7AB6ECFCF4336626,
	Rigidbody_get_rotation_Injected_m38431B37B78B4DF59B59F9CB7E609820430051F8,
	Rigidbody_set_rotation_Injected_mDE5B37E97D8FE16D9AD8FC6B9213A106B28589D7,
	Rigidbody_MovePosition_Injected_mF2CDF14960A920DCDDEAFA49A7E066A2FF021E37,
	Rigidbody_MoveRotation_Injected_m75B6A86B8BE8D68714CA5356DDCC11D24B96B505,
	Rigidbody_AddForce_Injected_m094E54DEA6CEAEA340F053D077CDF0753900F48E,
	Rigidbody_AddRelativeForce_Injected_m9BD7D9D36B62C306CA7D30CEB6BDFCDEDDFF9DF3,
	Rigidbody_AddRelativeTorque_Injected_m4FCDB6F9DA07C7C64925943F79670E967B1B8FA6,
	Rigidbody_AddForceAtPosition_Injected_m24F286471C9928629A50A5C1A0DF22698172438C,
	MeshCollider_set_sharedMesh_m05F87B9AC04139285EBBCC159F77B51C6940E79C,
	MeshCollider_set_convex_m20482D687240D0921BA76B77983403E55A2E3CE1,
	BoxCollider_set_center_m0AB0482699735FEE8306A7FCAAE66A76C479F0F0,
	BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E,
	BoxCollider_set_size_m8374267FDE5DD628973E0E5E1331E781552B855A,
	BoxCollider_set_center_Injected_mF2FF6FB33F950F7329395C6EC97E843ACDA52D8E,
	BoxCollider_get_size_Injected_m4F20D7B3D8FB4360C9E2986FB6A8CC66ABC89511,
	BoxCollider_set_size_Injected_mFA7F15E94435D42A70D7C8543CEC7AF4F6D55343,
	Collision_GetContacts_Internal_m925C8F6272E55A551B54B1F96B1994F05FDB9DF5,
	Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0,
	Collision_get_rigidbody_mD7A14B9C8AA98352340D2AB0097FC3A424FBB81B,
	Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D,
	Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E,
	Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150,
	Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473,
	Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD,
	Collision_GetContacts_m3E2B52E011083420A9A1F5E551798F9483622292,
	Collision_get_impulse_mBA2EDD39B7F495FF335FB867B244253602C7EF5D,
	Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B,
	Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70,
	Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD,
	Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87,
	Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78,
	Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB,
	Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4,
	Collider_get_sharedMaterial_m238C1D9D4B2B1F02876C610E049C7A5ECCDC07AC,
	Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C,
	Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938,
	Collider_Internal_ClosestPointOnBounds_m87BD13A92D4239E7BA08C0417197DFC8D4E5DB7E,
	Collider_ClosestPointOnBounds_mBF2F0C0E76C5F11AED801931D780823A94630952,
	Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA,
	Collider_ClosestPoint_Injected_m4E218A16FABAA4615270B9CD82DC66E130AAFE77,
	Collider_get_bounds_Injected_m1BDB8DBC0BC2BFC51D4A185C494EDB0997B93A43,
	Collider_Raycast_Injected_mAFAD355B658765116985B737217587C68BF257A3,
	Collider_Internal_ClosestPointOnBounds_Injected_mC77F9140817CA77A46BB8672B6DD388CC3BD2B7E,
	CapsuleCollider_set_center_m242D92DAEF25887C6A87A0777E4E624C0A431A2E,
	CapsuleCollider_set_radius_mB301C0086FE0D251683512184B0F6DDE264BA985,
	CapsuleCollider_set_height_m5DAE3DC5AD851E30C5A29AC7A22F36BE1E205BBB,
	CapsuleCollider_set_center_Injected_m2C7FB04A185C5A1C8B3AFCFBB2923C8E6966C132,
	SphereCollider_set_radius_m6119FE18C6739B077AB17334B1B53984911017FF,
	Joint_get_connectedBody_mE39E0AC9869325CD018B9ADB383B6BE01D497B59,
	Joint_set_connectedBody_mE9E631476E9D4264E8DC0D6307146F5EB64D3ED4,
	Joint_get_axis_mEDF8FE710E08CED9EA69A0369A075B77FF0BE79C,
	Joint_set_axis_m3C64D93F04DA043D6B02160F9034907BACC06800,
	Joint_get_anchor_m1CDF56CF0BD9773E9923D777EA24B2102DEDB79D,
	Joint_set_anchor_m89447EF25E0FC6DB9D22562BAF3BDA3E6D04029C,
	Joint_set_connectedAnchor_m2A40C3C4FB583E9DBC020222A21F577C066D5D90,
	Joint_set_autoConfigureConnectedAnchor_mF61D716174DE67CD94FF042881E9052357679E02,
	Joint_set_connectedMassScale_m6F7D8FEFAD6BFDC7177D9D06DBCCDCC288C4475D,
	Joint_get_axis_Injected_m85FD31C188CC3C7BC266AC7E5E1DD5B14E656C34,
	Joint_set_axis_Injected_m013FB85E34CB410702FC87EAB54B856CF180B505,
	Joint_get_anchor_Injected_mE20087A50431397C2C20E031DD98733425C2B445,
	Joint_set_anchor_Injected_m640F53862ECAB8D8163B378E54FB6E2194BB2AF7,
	Joint_set_connectedAnchor_Injected_mE4275DA473C8A61B37A1639E2352A09DCE2A4198,
	ConfigurableJoint_get_xMotion_m5518BED4E7F558174DD6B8F313CE7D125E1A1334,
	ConfigurableJoint_set_xMotion_mBDA7D8874899D2C20E1B1BA45944AA357CDFBDCC,
	ConfigurableJoint_get_yMotion_m4F0FA7246F1BAA1AC0BD7E86504CBE48D47CA005,
	ConfigurableJoint_set_yMotion_m597259075C915C848E87B3A9CBBDA0762B5A2563,
	ConfigurableJoint_get_zMotion_mC0AEE3A95069E7C0F451B71E356DCA387CDF4AEF,
	ConfigurableJoint_set_zMotion_m3479D7843AC2F91AA958F7B18AFCE3730842AFA8,
	ConfigurableJoint_get_angularXMotion_m5CBF8FC37A7CF94AF97583E5C1551BF7859B9258,
	ConfigurableJoint_set_angularXMotion_m1691CF3456A38996918D077FD6FC2CBEEFB0C9D5,
	ConfigurableJoint_get_angularYMotion_m1A08889BB8666184FED3CF1275444D5BA70ACE5F,
	ConfigurableJoint_set_angularYMotion_m21858D3799D8EED8AB21C46DF84927B10F1414D7,
	ConfigurableJoint_get_angularZMotion_m515347C78E06D82BE0AD254824E6F134E46CC58C,
	ConfigurableJoint_set_angularZMotion_m485474C654E903BBAE579F631BBD6C737B47394B,
	ConfigurableJoint_get_linearLimit_m35456F7AF48ACA69E79D1EFE14578730BAA6A98A,
	ConfigurableJoint_set_linearLimit_m57EE251D8642A4ADED96D77555B5948AF7F4AA9E,
	ConfigurableJoint_get_lowAngularXLimit_mE5EA802AA80E71542FDE6DD911364FC24297F4BD,
	ConfigurableJoint_set_lowAngularXLimit_m6424314936986525CEBCE5C16EBA69B5129BBD5A,
	ConfigurableJoint_get_highAngularXLimit_m978FF09CAF3E87AFA149752594ADD09FB9EA1ACE,
	ConfigurableJoint_set_highAngularXLimit_m96373EE2554934636E127E345F91306844177294,
	ConfigurableJoint_get_angularYLimit_mCC629F60D5650EF0F8F49FFB5DEE4052F687CA47,
	ConfigurableJoint_set_angularYLimit_mF819FB8C5F17C9737EC0BA5A3EAAC5245AE57A08,
	ConfigurableJoint_get_angularZLimit_m3F1975F6CAFD784F4F0881CB00D6E266CCE2658B,
	ConfigurableJoint_set_angularZLimit_mCB9FEE0CAF97A1A278BDCD127C86DDD26CDBBC70,
	ConfigurableJoint_set_configuredInWorldSpace_mD1FB99B42E24A0CABF43B4470E6F0C92BCCC2450,
	ConfigurableJoint_get_linearLimit_Injected_mAB06629F3F0FF14825C71BC77B8D1856836E6329,
	ConfigurableJoint_set_linearLimit_Injected_mB7C9F6674B0E76154DFCFAB457E68264FC8ACA88,
	ConfigurableJoint_get_lowAngularXLimit_Injected_m65F3B2184E5BE0B93B9702DE371B50B727050C60,
	ConfigurableJoint_set_lowAngularXLimit_Injected_m424F97E98AB918627E19CFA7FED1C4236DBFDD61,
	ConfigurableJoint_get_highAngularXLimit_Injected_m6672C8D546D7DCBD8E67AB9CF91AEB3FB76E72AC,
	ConfigurableJoint_set_highAngularXLimit_Injected_m7C31099A209513CAE46018D03F195D33CFCFD501,
	ConfigurableJoint_get_angularYLimit_Injected_m7B5BF8EE2D2AFC977D6E192307EE9A8DC14A6C47,
	ConfigurableJoint_set_angularYLimit_Injected_m4DA460D042653C9FB41E5E2A3AC4B0F18F6D76FE,
	ConfigurableJoint_get_angularZLimit_Injected_m3E5B226EC970A3A74652C9AE9B9F1BF07B8A6AE0,
	ConfigurableJoint_set_angularZLimit_Injected_m781FCA9CA40DA1F5B984BDCBA4DBE0F4309198FC,
	ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208,
	ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6,
	PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7,
	PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814,
	PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3,
	PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1,
	PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730,
	PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0,
	PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE,
	PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74,
	PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3,
	PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB,
	PhysicsScene_Query_CapsuleCast_m6871258F7BAA2370C7BA7334E2EE6752EFBD723F,
	PhysicsScene_Internal_CapsuleCast_mFB5002955B349D73D842F47BB3DBCDAE453FF2F0,
	PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35,
	PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F,
	PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F,
	PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1,
	PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01,
	PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62,
	PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_mD6BA34F06BE743B2CBF46AA82EE6DDC9CCEC0F27,
	PhysicsScene_Query_CapsuleCast_Injected_m3A9CC7AB617D70C2284C49E982163F37EB0B27B0,
	PhysicsScene_Internal_SphereCastNonAlloc_Injected_m8B19C4FB753820C4D4952D6BEB59B7044F7C7394,
	PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737,
	WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E,
	WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34,
	WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E,
	WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C,
	WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F,
	WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43,
	WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6,
	WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5,
};
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk (void);
extern void RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11_AdjustorThunk (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk (void);
extern void RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B_AdjustorThunk (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk (void);
extern void RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC_AdjustorThunk (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk (void);
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk (void);
extern void PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk (void);
extern void WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E_AdjustorThunk (void);
extern void WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34_AdjustorThunk (void);
extern void WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E_AdjustorThunk (void);
extern void WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C_AdjustorThunk (void);
extern void WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F_AdjustorThunk (void);
extern void WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43_AdjustorThunk (void);
extern void WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6_AdjustorThunk (void);
extern void WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[29] = 
{
	{ 0x06000039, RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk },
	{ 0x0600003A, RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk },
	{ 0x0600003B, RaycastHit_set_point_m3B63BEB25A82BFCF9FBB300022D0362BC2CF9E11_AdjustorThunk },
	{ 0x0600003C, RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk },
	{ 0x0600003D, RaycastHit_set_normal_m97DDF1CBE8ADF1F72AA30BC83870615ABB38C88B_AdjustorThunk },
	{ 0x0600003E, RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk },
	{ 0x0600003F, RaycastHit_set_distance_mD5C9C6A5F7EDFFAC302DA4981F3483AA9981A9DC_AdjustorThunk },
	{ 0x06000040, RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk },
	{ 0x06000041, RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk },
	{ 0x060000C7, ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk },
	{ 0x060000C8, ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk },
	{ 0x060000C9, PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk },
	{ 0x060000CA, PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk },
	{ 0x060000CB, PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk },
	{ 0x060000CC, PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk },
	{ 0x060000CD, PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk },
	{ 0x060000CF, PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk },
	{ 0x060000D1, PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk },
	{ 0x060000D5, PhysicsScene_CapsuleCast_m31A5F75B99A0D9CC616E1F18ADCF6E51937CAD35_AdjustorThunk },
	{ 0x060000D7, PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk },
	{ 0x060000D9, PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk },
	{ 0x060000E0, WheelFrictionCurve_set_extremumSlip_m9001B1CEF04F1B96DCADDC195AEE27B154E86C1E_AdjustorThunk },
	{ 0x060000E1, WheelFrictionCurve_get_extremumValue_mD8481A6FBF758B06DA59A911B3FB2E95299C2F34_AdjustorThunk },
	{ 0x060000E2, WheelFrictionCurve_set_extremumValue_m20B792FE59A3FC99CE593F30B718298D6D4AAB6E_AdjustorThunk },
	{ 0x060000E3, WheelFrictionCurve_set_asymptoteSlip_mC97F60DD6CD02E5C5124ABEDD9C2E9B07307438C_AdjustorThunk },
	{ 0x060000E4, WheelFrictionCurve_get_asymptoteValue_mA7F621299782717BAD2F3AEFFB88D153DB1F457F_AdjustorThunk },
	{ 0x060000E5, WheelFrictionCurve_set_asymptoteValue_m9D22E9A885235D2309214936A813EEC64DBD0B43_AdjustorThunk },
	{ 0x060000E6, WheelFrictionCurve_get_stiffness_mD8032249050920ECEB2C5F4EE723C0901A1FF8F6_AdjustorThunk },
	{ 0x060000E7, WheelFrictionCurve_set_stiffness_mBB10E953047898E37EC5BC7304C5ADA3225ECFA5_AdjustorThunk },
};
static const int32_t s_InvokerIndices[231] = 
{
	7597,
	9524,
	8115,
	8761,
	6827,
	7171,
	7719,
	8359,
	6685,
	6826,
	7170,
	7718,
	7160,
	7709,
	8313,
	8961,
	6818,
	7159,
	7708,
	8312,
	6825,
	7169,
	7718,
	6566,
	6686,
	7010,
	7013,
	7462,
	7904,
	8563,
	7453,
	7894,
	8544,
	9161,
	6955,
	7339,
	7797,
	8460,
	6702,
	6958,
	7341,
	7801,
	7011,
	7461,
	7903,
	8562,
	6564,
	6564,
	6957,
	6617,
	9362,
	6961,
	6961,
	6560,
	6329,
	8761,
	6204,
	6320,
	5141,
	6320,
	5141,
	6259,
	5087,
	6204,
	6204,
	6320,
	5141,
	6320,
	5141,
	6259,
	5087,
	5087,
	6259,
	5087,
	4945,
	6108,
	4945,
	4945,
	5016,
	5141,
	4945,
	6320,
	6222,
	5056,
	5016,
	5087,
	5141,
	5056,
	6329,
	2969,
	5141,
	2969,
	2969,
	5141,
	1570,
	2973,
	6329,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	2349,
	2349,
	2349,
	1374,
	5040,
	4945,
	5141,
	6320,
	5141,
	4935,
	4935,
	4935,
	6204,
	6320,
	6204,
	6204,
	6204,
	6178,
	6204,
	3883,
	4201,
	6320,
	6108,
	4945,
	6204,
	6108,
	4945,
	4683,
	6107,
	6204,
	1307,
	1159,
	1563,
	4683,
	6329,
	2345,
	4935,
	911,
	1373,
	5141,
	5087,
	5087,
	4935,
	5087,
	6204,
	5040,
	6320,
	5141,
	6320,
	5141,
	5141,
	4945,
	5087,
	4935,
	4935,
	4935,
	4935,
	4935,
	6178,
	5016,
	6178,
	5016,
	6178,
	5016,
	6178,
	5016,
	6178,
	5016,
	6178,
	5016,
	6261,
	5089,
	6261,
	5089,
	6261,
	5089,
	6261,
	5089,
	6261,
	5089,
	4945,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	4935,
	6320,
	6320,
	6204,
	6178,
	3597,
	3600,
	265,
	6816,
	178,
	6682,
	183,
	6698,
	6539,
	6538,
	76,
	6571,
	117,
	6699,
	390,
	6792,
	6659,
	6689,
	6535,
	6567,
	6690,
	5087,
	6259,
	5087,
	5087,
	6259,
	5087,
	6259,
	5087,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	231,
	s_methodPointers,
	29,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
