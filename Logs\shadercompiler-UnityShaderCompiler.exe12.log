Base path: 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data', plugins path 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=1153 file=Assets/shader/Standard pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=_ALPHAPREMULTIPLY_ON DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _EMISSION _METALLICGLOSSMAP _DETAIL_MULX2 _PARALLAXMAP FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=63 ok=1 outsize=11197

Cmd: compileSnippet
  insize=1109 file=Assets/shader/Standard pass=FORWARD_DELTA cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDADD uKW=_ALPHAPREMULTIPLY_ON DIRECTIONAL SHADOWS_SCREEN dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _METALLICGLOSSMAP _DETAIL_MULX2 _PARALLAXMAP FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=98 ok=1 outsize=8133

Cmd: compileSnippet
  insize=1109 file=Assets/shader/Standard pass=FORWARD_DELTA cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDADD uKW=_ALPHAPREMULTIPLY_ON POINT_COOKIE SHADOWS_CUBE dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _METALLICGLOSSMAP _DETAIL_MULX2 _PARALLAXMAP FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=98 ok=1 outsize=9112

Cmd: compileSnippet
  insize=1057 file=Assets/shader/Standard pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_FORWARDBASE uKW=_ALPHAPREMULTIPLY_ON DIRECTIONAL VERTEXLIGHT_ON dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _EMISSION _METALLICGLOSSMAP _DETAIL_MULX2 FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=1 mask=6 start=221 ok=1 outsize=6846

Cmd: compileSnippet
  insize=1057 file=Assets/shader/Standard pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=_ALPHAPREMULTIPLY_ON DIRECTIONAL dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _EMISSION _METALLICGLOSSMAP _DETAIL_MULX2 FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=1 mask=6 start=221 ok=1 outsize=7336

Cmd: compileSnippet
  insize=987 file=Assets/shader/Standard pass=FORWARD_DELTA cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_FORWARDADD uKW=_ALPHAPREMULTIPLY_ON POINT SHADOWS_CUBE dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _METALLICGLOSSMAP _DETAIL_MULX2 FOG_LINEAR FOG_EXP FOG_EXP2 DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=1 mask=6 start=254 ok=1 outsize=7131

Cmd: compileSnippet
  insize=987 file=Assets/shader/Standard pass=FORWARD_DELTA cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDADD uKW=_ALPHAPREMULTIPLY_ON DIRECTIONAL_COOKIE dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _METALLICGLOSSMAP _DETAIL_MULX2 FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN SHADOWS_CUBE UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=1 mask=6 start=254 ok=1 outsize=6839

Cmd: compileSnippet
  insize=987 file=Assets/shader/Standard pass=FORWARD_DELTA cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDADD uKW=_ALPHAPREMULTIPLY_ON POINT_COOKIE SHADOWS_CUBE dKW=_NORMALMAP _ALPHATEST_ON _ALPHABLEND_ON _METALLICGLOSSMAP _DETAIL_MULX2 FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=1 mask=6 start=254 ok=1 outsize=8148

Cmd: compileSnippet
  insize=771 file=Assets/shader/Standard pass=ShadowCaster cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_SHADOWCASTER uKW=_ALPHAPREMULTIPLY_ON SHADOWS_DEPTH dKW=_ALPHATEST_ON _ALPHABLEND_ON SHADOWS_CUBE UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=1 mask=6 start=282 ok=1 outsize=3059

Cmd: compileSnippet
  insize=1188 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=ShadowCaster cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_SHADOWCASTER uKW=_ALPHATEST_ON _REQUIRE_UV2 SHADOWS_DEPTH dKW=INSTANCING_ON PROCEDURAL_INSTANCING_ON _ALPHABLEND_ON _ALPHAPREMULTIPLY_ON _ALPHAMODULATE_ON SHADOWS_CUBE UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=71 ok=1 outsize=4166

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_FORWARDBASE uKW=_ALPHATEST_ON _REQUIRE_UV2 DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON EFFECT_BUMP LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=227 mask=6 start=154 ok=1 outsize=10124

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTMAP_SHADOW_MIXING UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=227 mask=6 start=154 ok=1 outsize=10579

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_FORWARDBASE uKW=_ALPHATEST_ON _REQUIRE_UV2 SOFTPARTICLES_ON DIRECTIONAL SHADOWS_SCREEN dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON EFFECT_BUMP LIGHTPROBE_SH LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=227 mask=6 start=154 ok=1 outsize=11077

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_FORWARDBASE uKW=SOFTPARTICLES_ON DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP SHADOWS_SCREEN LIGHTMAP_SHADOW_MIXING UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=227 mask=6 start=154 ok=1 outsize=9964

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=_ALPHATEST_ON _REQUIRE_UV2 DIRECTIONAL LIGHTPROBE_SH dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON EFFECT_BUMP SHADOWS_SCREEN LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=154 ok=1 outsize=8935

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=_ALPHATEST_ON _REQUIRE_UV2 DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON EFFECT_BUMP SHADOWS_SCREEN LIGHTMAP_SHADOW_MIXING UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=154 ok=1 outsize=10528

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=PROCEDURAL_INSTANCING_ON DIRECTIONAL dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTPROBE_SH SHADOWS_SCREEN LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=16611 mask=6 start=154 ok=1 outsize=12385

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=PROCEDURAL_INSTANCING_ON DIRECTIONAL VERTEXLIGHT_ON dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTPROBE_SH SHADOWS_SCREEN LIGHTMAP_SHADOW_MIXING UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=16611 mask=6 start=154 ok=1 outsize=13940

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=SOFTPARTICLES_ON DIRECTIONAL dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTPROBE_SH SHADOWS_SCREEN LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=154 ok=1 outsize=9418

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=SOFTPARTICLES_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=154 ok=1 outsize=11026

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=SOFTPARTICLES_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTMAP_SHADOW_MIXING UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=154 ok=1 outsize=12605

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=PROCEDURAL_INSTANCING_ON _ALPHATEST_ON _REQUIRE_UV2 SOFTPARTICLES_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=SHADOWS_SHADOWMASK INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON EFFECT_BUMP LIGHTMAP_SHADOW_MIXING VERTEXLIGHT_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=16611 mask=6 start=154 ok=1 outsize=15959

Cmd: compileSnippet
  insize=41815416 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=FORWARD cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_FORWARDBASE uKW=PROCEDURAL_INSTANCING_ON _ALPHATEST_ON _REQUIRE_UV2 SOFTPARTICLES_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=SHADOWS_SHADOWMASK INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP _FADING_ON EFFECT_BUMP LIGHTMAP_SHADOW_MIXING UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=16611 mask=6 start=154 ok=1 outsize=16979

Cmd: compileSnippet
  insize=34130677 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=DEFERRED cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_DEFERRED uKW= dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTPROBE_SH UNITY_HDR_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=227 mask=6 start=425 ok=1 outsize=4727

Cmd: compileSnippet
  insize=34130677 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=DEFERRED cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PASS_DEFERRED uKW=SOFTPARTICLES_ON LIGHTPROBE_SH UNITY_HDR_ON dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles reqs=227 mask=6 start=425 ok=1 outsize=5632

Cmd: compileSnippet
  insize=34130677 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=DEFERRED cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_DEFERRED uKW=UNITY_HDR_ON dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP LIGHTPROBE_SH UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=425 ok=1 outsize=5339

Cmd: compileSnippet
  insize=34130677 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=DEFERRED cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_DEFERRED uKW=PROCEDURAL_INSTANCING_ON _ALPHATEST_ON _REQUIRE_UV2 UNITY_HDR_ON dKW=SOFTPARTICLES_ON SHADOWS_SHADOWMASK INSTANCING_ON _NORMALMAP _FADING_ON EFFECT_BUMP LIGHTPROBE_SH UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=16611 mask=6 start=425 ok=1 outsize=10264

Cmd: compileSnippet
  insize=34130677 file=Assets/DefaultResourcesExtra/Particles/Standard Surface pass=DEFERRED cachingPP=1 ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING SHADER_API_GLES30 UNITY_PASS_DEFERRED uKW=SOFTPARTICLES_ON LIGHTPROBE_SH dKW=SHADOWS_SHADOWMASK INSTANCING_ON PROCEDURAL_INSTANCING_ON _NORMALMAP _FADING_ON _REQUIRE_UV2 EFFECT_BUMP UNITY_HDR_ON UNITY_ENABLE_NATIVE_SHADOW_LOOKUPS UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=425 ok=1 outsize=6332

Cmd: compileSnippet
