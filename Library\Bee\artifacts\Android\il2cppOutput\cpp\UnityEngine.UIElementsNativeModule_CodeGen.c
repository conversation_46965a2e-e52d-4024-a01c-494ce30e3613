﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.Yoga.YogaConfig::.ctor(System.IntPtr)
extern void YogaConfig__ctor_mA5B9DCE1F40B5A6948D3D8848516F2CBCD2FABF4 (void);
// 0x00000002 System.Void UnityEngine.Yoga.YogaConfig::.ctor()
extern void YogaConfig__ctor_m81D3E486916576BD67674EA5F26C8F2B45CCB6DD (void);
// 0x00000003 System.Void UnityEngine.Yoga.YogaConfig::Finalize()
extern void YogaConfig_Finalize_m395FFF204239D6A9DC076105B4D935706B29D631 (void);
// 0x00000004 System.IntPtr UnityEngine.Yoga.YogaConfig::get_Handle()
extern void YogaConfig_get_Handle_m7E2D8171E4E8AA98BC1D886218B6207D602281DD (void);
// 0x00000005 System.Boolean UnityEngine.Yoga.YogaConfig::get_UseWebDefaults()
extern void YogaConfig_get_UseWebDefaults_mB64415ACFDF7FB34F6F5BB42059BFD8E3705AD9D (void);
// 0x00000006 System.Void UnityEngine.Yoga.YogaConfig::set_UseWebDefaults(System.Boolean)
extern void YogaConfig_set_UseWebDefaults_m96AB3442B80FDA12436C4452D8605AA8DA1C8B81 (void);
// 0x00000007 System.Void UnityEngine.Yoga.YogaConfig::set_PointScaleFactor(System.Single)
extern void YogaConfig_set_PointScaleFactor_mF61D0EFB38778BC4B6DE323F53348485B8AC643D (void);
// 0x00000008 System.Void UnityEngine.Yoga.YogaConfig::.cctor()
extern void YogaConfig__cctor_m2CBFE98AAE90596CBB749697AAE1973B2AE051FC (void);
// 0x00000009 UnityEngine.Yoga.YogaSize UnityEngine.Yoga.MeasureOutput::Make(System.Single,System.Single)
extern void MeasureOutput_Make_m19BCA4677D3255795D936162FB6DE021C2C89E83 (void);
// 0x0000000A System.Void UnityEngine.Yoga.BaselineFunction::.ctor(System.Object,System.IntPtr)
extern void BaselineFunction__ctor_m525AED7069E4DFB2C8770618315000F96E7FD500 (void);
// 0x0000000B System.Single UnityEngine.Yoga.BaselineFunction::Invoke(UnityEngine.Yoga.YogaNode,System.Single,System.Single)
extern void BaselineFunction_Invoke_m2DDB6CB96A11C1AF2F557FB363F99BA3A2E6E109 (void);
// 0x0000000C System.Boolean UnityEngine.Yoga.YogaConstants::IsUndefined(System.Single)
extern void YogaConstants_IsUndefined_m1BD66A8B7482CB49C9D4B0241E88CCE8EB0743FB (void);
// 0x0000000D System.Void UnityEngine.Yoga.YogaNode::.ctor(UnityEngine.Yoga.YogaConfig)
extern void YogaNode__ctor_m824433D9174C4325E87EC380CD5EB5F10C20A35C (void);
// 0x0000000E System.Void UnityEngine.Yoga.YogaNode::Finalize()
extern void YogaNode_Finalize_mD0E1733478B7861392542C1A2F8161B462CD6327 (void);
// 0x0000000F System.Void UnityEngine.Yoga.YogaNode::set_Config(UnityEngine.Yoga.YogaConfig)
extern void YogaNode_set_Config_mFEE688C9B0B7EFFE581F278746A7C4CD76449DE5 (void);
// 0x00000010 System.Boolean UnityEngine.Yoga.YogaNode::get_IsDirty()
extern void YogaNode_get_IsDirty_m24743ABB33BC9F32F946B3FD9C5DB85F77285781 (void);
// 0x00000011 System.Void UnityEngine.Yoga.YogaNode::MarkDirty()
extern void YogaNode_MarkDirty_mCCCABC1717DCAF3E313846069AD503959B184930 (void);
// 0x00000012 System.Boolean UnityEngine.Yoga.YogaNode::get_HasNewLayout()
extern void YogaNode_get_HasNewLayout_m801E00B631071A29A968ACA0489B1BDC2CE3CE05 (void);
// 0x00000013 System.Boolean UnityEngine.Yoga.YogaNode::get_IsMeasureDefined()
extern void YogaNode_get_IsMeasureDefined_m14B27C81AD102F307669F55E3FDFEE7E7E61B7EC (void);
// 0x00000014 System.Boolean UnityEngine.Yoga.YogaNode::get_IsBaselineDefined()
extern void YogaNode_get_IsBaselineDefined_m7E3BFBBB4F59F44881E6A58B342FABB672119DF5 (void);
// 0x00000015 System.Void UnityEngine.Yoga.YogaNode::CopyStyle(UnityEngine.Yoga.YogaNode)
extern void YogaNode_CopyStyle_mB3AFC6604AA23297A7DA93E5DE9A36CC3CD4B65C (void);
// 0x00000016 System.Void UnityEngine.Yoga.YogaNode::set_FlexDirection(UnityEngine.Yoga.YogaFlexDirection)
extern void YogaNode_set_FlexDirection_mFF74AB011A465EFD90BAFDE41F00207619429306 (void);
// 0x00000017 System.Void UnityEngine.Yoga.YogaNode::set_JustifyContent(UnityEngine.Yoga.YogaJustify)
extern void YogaNode_set_JustifyContent_m008464E5DA37AEDD2DDA37E89CEB2E3A1C25C286 (void);
// 0x00000018 System.Void UnityEngine.Yoga.YogaNode::set_Display(UnityEngine.Yoga.YogaDisplay)
extern void YogaNode_set_Display_mCD8A7B298E87852734559A41DC01EF96827032C2 (void);
// 0x00000019 System.Void UnityEngine.Yoga.YogaNode::set_AlignItems(UnityEngine.Yoga.YogaAlign)
extern void YogaNode_set_AlignItems_m8443F468878AF728A2F82505F4B53D2065DA89BF (void);
// 0x0000001A System.Void UnityEngine.Yoga.YogaNode::set_AlignSelf(UnityEngine.Yoga.YogaAlign)
extern void YogaNode_set_AlignSelf_mD678D097FA3DB20EF1568A9B17C7423CC8B0BCBF (void);
// 0x0000001B System.Void UnityEngine.Yoga.YogaNode::set_AlignContent(UnityEngine.Yoga.YogaAlign)
extern void YogaNode_set_AlignContent_m9AC19A64AAAACEB85DA4FCF94D9372A4556F0845 (void);
// 0x0000001C System.Void UnityEngine.Yoga.YogaNode::set_PositionType(UnityEngine.Yoga.YogaPositionType)
extern void YogaNode_set_PositionType_m958F7BB665C87711439BED68CCB9E7C63798FEA5 (void);
// 0x0000001D System.Void UnityEngine.Yoga.YogaNode::set_Wrap(UnityEngine.Yoga.YogaWrap)
extern void YogaNode_set_Wrap_m01F365128F74AC617966A2B3EEF340150AB59EE6 (void);
// 0x0000001E System.Void UnityEngine.Yoga.YogaNode::set_Flex(System.Single)
extern void YogaNode_set_Flex_mCFCB82869C82DD5CB40151681576F4A40F381D6A (void);
// 0x0000001F System.Void UnityEngine.Yoga.YogaNode::set_FlexGrow(System.Single)
extern void YogaNode_set_FlexGrow_m884FDA11A3F7FAF051CB840829442FE14CC3CC2B (void);
// 0x00000020 System.Void UnityEngine.Yoga.YogaNode::set_FlexShrink(System.Single)
extern void YogaNode_set_FlexShrink_m30FC430AB92EB6FF05B394D25D728DC4DB2B4FA5 (void);
// 0x00000021 System.Void UnityEngine.Yoga.YogaNode::set_FlexBasis(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_FlexBasis_m24CCC6C38C4612359E942EEF5EA5FDF4D24E7671 (void);
// 0x00000022 System.Void UnityEngine.Yoga.YogaNode::set_Width(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_Width_mA267DDF44981884C4C630FCBC602595F58AB05E8 (void);
// 0x00000023 System.Void UnityEngine.Yoga.YogaNode::set_Height(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_Height_m4AFF9C286F42919CA1C5C7A152A4ED477C56969E (void);
// 0x00000024 System.Void UnityEngine.Yoga.YogaNode::set_MaxWidth(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MaxWidth_m18194AF8ECC926D70493BAC8C8041349DCF0DE43 (void);
// 0x00000025 System.Void UnityEngine.Yoga.YogaNode::set_MaxHeight(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MaxHeight_m25F7366BA8516D5C4B3EBDA5A797B33704A4CAB8 (void);
// 0x00000026 System.Void UnityEngine.Yoga.YogaNode::set_MinWidth(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MinWidth_mC876A8736AA69BC6100AA5E361A0E9190D02DB21 (void);
// 0x00000027 System.Void UnityEngine.Yoga.YogaNode::set_MinHeight(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MinHeight_mB3301DB6766234FE7D594DE46DF59C31F0F29643 (void);
// 0x00000028 System.Single UnityEngine.Yoga.YogaNode::get_LayoutX()
extern void YogaNode_get_LayoutX_mEEE47120D6DB656AC643A1294AFE3CC79E93C492 (void);
// 0x00000029 System.Single UnityEngine.Yoga.YogaNode::get_LayoutY()
extern void YogaNode_get_LayoutY_m79DCF02D705920434DCE9B534FFAAC936A268173 (void);
// 0x0000002A System.Single UnityEngine.Yoga.YogaNode::get_LayoutRight()
extern void YogaNode_get_LayoutRight_mEBCE0188575C7FE3D72255011803E1EC56685F24 (void);
// 0x0000002B System.Single UnityEngine.Yoga.YogaNode::get_LayoutBottom()
extern void YogaNode_get_LayoutBottom_m2F6857AB410F79908EA7098DA19F09BE07245269 (void);
// 0x0000002C System.Single UnityEngine.Yoga.YogaNode::get_LayoutWidth()
extern void YogaNode_get_LayoutWidth_mF68064634C5682A2C0C70DAAF6CB45C39D3F216A (void);
// 0x0000002D System.Single UnityEngine.Yoga.YogaNode::get_LayoutHeight()
extern void YogaNode_get_LayoutHeight_m0A56155F1B067D0127E5A38204C2F0A9BAB605BD (void);
// 0x0000002E System.Void UnityEngine.Yoga.YogaNode::set_Overflow(UnityEngine.Yoga.YogaOverflow)
extern void YogaNode_set_Overflow_m5DD8FEB426E1376D210ED0057C4A12DD8E18417F (void);
// 0x0000002F System.Int32 UnityEngine.Yoga.YogaNode::get_Count()
extern void YogaNode_get_Count_mBBD0D15ACBBA109563C7D22EAB1F58094C4562AD (void);
// 0x00000030 System.Void UnityEngine.Yoga.YogaNode::MarkLayoutSeen()
extern void YogaNode_MarkLayoutSeen_m899C39B9392134C5B7835217D18FCC8D5E7A1E5A (void);
// 0x00000031 System.Void UnityEngine.Yoga.YogaNode::Insert(System.Int32,UnityEngine.Yoga.YogaNode)
extern void YogaNode_Insert_m9182FC436BFB915BDAB6492465B6E7832B1921CF (void);
// 0x00000032 System.Void UnityEngine.Yoga.YogaNode::RemoveAt(System.Int32)
extern void YogaNode_RemoveAt_m344D767FF02FB69813F270953ACFEE28E5DEF83F (void);
// 0x00000033 System.Void UnityEngine.Yoga.YogaNode::Clear()
extern void YogaNode_Clear_mCB7D5DF9967646CFD9A156DEAC56E13A0BA60826 (void);
// 0x00000034 System.Void UnityEngine.Yoga.YogaNode::SetMeasureFunction(UnityEngine.Yoga.MeasureFunction)
extern void YogaNode_SetMeasureFunction_mD658FA9C0543C022DB09D54B49BA38354B558D04 (void);
// 0x00000035 System.Void UnityEngine.Yoga.YogaNode::CalculateLayout(System.Single,System.Single)
extern void YogaNode_CalculateLayout_mF1185A522FA0E60BA47039893A3EE3419B6F1D37 (void);
// 0x00000036 UnityEngine.Yoga.YogaSize UnityEngine.Yoga.YogaNode::MeasureInternal(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode)
extern void YogaNode_MeasureInternal_m48B8FB32DE181D6CF67675FE8FCE8B5947CCBFF0 (void);
// 0x00000037 System.Single UnityEngine.Yoga.YogaNode::BaselineInternal(UnityEngine.Yoga.YogaNode,System.Single,System.Single)
extern void YogaNode_BaselineInternal_m1CB75FF4F21CC040899903C1BAA548691A94D757 (void);
// 0x00000038 System.Collections.Generic.IEnumerator`1<UnityEngine.Yoga.YogaNode> UnityEngine.Yoga.YogaNode::GetEnumerator()
extern void YogaNode_GetEnumerator_m93CA3A3DC1ED5F958FB6E7D3CFB534F9A374B394 (void);
// 0x00000039 System.Collections.IEnumerator UnityEngine.Yoga.YogaNode::System.Collections.IEnumerable.GetEnumerator()
extern void YogaNode_System_Collections_IEnumerable_GetEnumerator_mF3D17F0F0C2E0C5FA4ECE8EDE724F0E332E900FA (void);
// 0x0000003A System.Void UnityEngine.Yoga.YogaNode::set_Left(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_Left_m45FA7CF627DF74C1F795DA1488EF943DA77067A6 (void);
// 0x0000003B System.Void UnityEngine.Yoga.YogaNode::set_Top(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_Top_m2F3AE8DA9025B7A1EB370C67C8B4560BA23CE5A4 (void);
// 0x0000003C System.Void UnityEngine.Yoga.YogaNode::set_Right(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_Right_m35D7139ECEB7CC1B82A5DCFBFDC96EA9AFC686E5 (void);
// 0x0000003D System.Void UnityEngine.Yoga.YogaNode::set_Bottom(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_Bottom_m03A39B84DE5056608BCFE43E98956BF58035347F (void);
// 0x0000003E System.Void UnityEngine.Yoga.YogaNode::SetStylePosition(UnityEngine.Yoga.YogaEdge,UnityEngine.Yoga.YogaValue)
extern void YogaNode_SetStylePosition_m17081B8019875ACCC6D5215A1FB0D6B64C9FD7CF (void);
// 0x0000003F System.Void UnityEngine.Yoga.YogaNode::set_MarginLeft(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MarginLeft_m6DD0D35E142EB59ED9280D1C4EA2F038C132FC4B (void);
// 0x00000040 System.Void UnityEngine.Yoga.YogaNode::set_MarginTop(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MarginTop_mA43AB8E85422F7980BECF2DEF66F736C27E8E972 (void);
// 0x00000041 System.Void UnityEngine.Yoga.YogaNode::set_MarginRight(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MarginRight_m4D2DF290FF89E2A9CF33DA815985B79DFA864A3C (void);
// 0x00000042 System.Void UnityEngine.Yoga.YogaNode::set_MarginBottom(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_MarginBottom_m0CCF6AF5FC2C6ACA8E2E084AABC83322A215AEEC (void);
// 0x00000043 System.Void UnityEngine.Yoga.YogaNode::SetStyleMargin(UnityEngine.Yoga.YogaEdge,UnityEngine.Yoga.YogaValue)
extern void YogaNode_SetStyleMargin_m9F59AA83FE83F289CC286EF0C33F8558055A8065 (void);
// 0x00000044 System.Void UnityEngine.Yoga.YogaNode::set_PaddingLeft(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_PaddingLeft_mDC797F410F72554FAD67A14DC5002656C985D06D (void);
// 0x00000045 System.Void UnityEngine.Yoga.YogaNode::set_PaddingTop(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_PaddingTop_mE59506FA241E2BFD3AC4CEB219000993CC950F01 (void);
// 0x00000046 System.Void UnityEngine.Yoga.YogaNode::set_PaddingRight(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_PaddingRight_mA277C8926B03AE1FE89F46A6A24882D61155E7F8 (void);
// 0x00000047 System.Void UnityEngine.Yoga.YogaNode::set_PaddingBottom(UnityEngine.Yoga.YogaValue)
extern void YogaNode_set_PaddingBottom_mD4BE8EAD7054982975BB95088DC507B355B57D32 (void);
// 0x00000048 System.Void UnityEngine.Yoga.YogaNode::SetStylePadding(UnityEngine.Yoga.YogaEdge,UnityEngine.Yoga.YogaValue)
extern void YogaNode_SetStylePadding_m4591446410152C5EFEEFC41D208431CEDFBBF707 (void);
// 0x00000049 System.Void UnityEngine.Yoga.YogaNode::set_BorderLeftWidth(System.Single)
extern void YogaNode_set_BorderLeftWidth_m6F0ADE17EA294DC33E0EBBB17E4DF867B5E73CA5 (void);
// 0x0000004A System.Void UnityEngine.Yoga.YogaNode::set_BorderTopWidth(System.Single)
extern void YogaNode_set_BorderTopWidth_m39A26DC4E61833C0F8F58EA28A71AA35C4553005 (void);
// 0x0000004B System.Void UnityEngine.Yoga.YogaNode::set_BorderRightWidth(System.Single)
extern void YogaNode_set_BorderRightWidth_m047EE6ECCF13C9E44885BCE8FA20D2FD0DA498C6 (void);
// 0x0000004C System.Void UnityEngine.Yoga.YogaNode::set_BorderBottomWidth(System.Single)
extern void YogaNode_set_BorderBottomWidth_m10BD7CB272CE0342EEA05F413A78BB6CE34FD8F3 (void);
// 0x0000004D System.Single UnityEngine.Yoga.YogaNode::get_LayoutMarginLeft()
extern void YogaNode_get_LayoutMarginLeft_mBBDC00F49301F60C09C5B3BF8782EAB5C814DFB4 (void);
// 0x0000004E System.Single UnityEngine.Yoga.YogaNode::get_LayoutMarginTop()
extern void YogaNode_get_LayoutMarginTop_m010905C6DDD8C42E540AAB4DDCD2AFA6FFE13BE6 (void);
// 0x0000004F System.Single UnityEngine.Yoga.YogaNode::get_LayoutMarginRight()
extern void YogaNode_get_LayoutMarginRight_mE6BDC383CDA9AFD8C827B928A86EF13A8D50A566 (void);
// 0x00000050 System.Single UnityEngine.Yoga.YogaNode::get_LayoutMarginBottom()
extern void YogaNode_get_LayoutMarginBottom_m46D9999CE1CF2957DE68BA4024B36F9A50F08151 (void);
// 0x00000051 System.Single UnityEngine.Yoga.YogaNode::get_LayoutPaddingLeft()
extern void YogaNode_get_LayoutPaddingLeft_m315AB18C71CFF8207E9DBC8D7538BFE0A0569421 (void);
// 0x00000052 System.Single UnityEngine.Yoga.YogaNode::get_LayoutPaddingTop()
extern void YogaNode_get_LayoutPaddingTop_m76AB547D25D8C51B5CA987BD7D8D586AF284E8D8 (void);
// 0x00000053 System.Single UnityEngine.Yoga.YogaNode::get_LayoutPaddingRight()
extern void YogaNode_get_LayoutPaddingRight_m3AB9145B687CB1ADE6B6EFBDBBF2928301B0FEB1 (void);
// 0x00000054 System.Single UnityEngine.Yoga.YogaNode::get_LayoutPaddingBottom()
extern void YogaNode_get_LayoutPaddingBottom_m08100BDABCA07492B6BBF8FD57BCB06AD460049C (void);
// 0x00000055 System.Single UnityEngine.Yoga.YogaNode::get_LayoutBorderLeft()
extern void YogaNode_get_LayoutBorderLeft_mC799A7EC6C1ED244DDD852634ACC768CEADFD366 (void);
// 0x00000056 System.Single UnityEngine.Yoga.YogaNode::get_LayoutBorderTop()
extern void YogaNode_get_LayoutBorderTop_m5FEAEE25D61F72B530BCC9B8B11A374BAD94D637 (void);
// 0x00000057 System.Single UnityEngine.Yoga.YogaNode::get_LayoutBorderRight()
extern void YogaNode_get_LayoutBorderRight_mC22B436C97ABEF06221F3BF9F1506E527E9CEF99 (void);
// 0x00000058 System.Single UnityEngine.Yoga.YogaNode::get_LayoutBorderBottom()
extern void YogaNode_get_LayoutBorderBottom_m06786864F2149A50CBD5F551977E46737A614D91 (void);
// 0x00000059 System.IntPtr UnityEngine.Yoga.Native::YGNodeNewWithConfig(System.IntPtr)
extern void Native_YGNodeNewWithConfig_m6F611BD851C531FBCBB7E5BCC06886089DAF92FD (void);
// 0x0000005A System.Void UnityEngine.Yoga.Native::YGNodeFree(System.IntPtr)
extern void Native_YGNodeFree_m65A5C6F9FAF9C804FE3299AF793B314D89C940CA (void);
// 0x0000005B System.Void UnityEngine.Yoga.Native::YGNodeFreeInternal(System.IntPtr)
extern void Native_YGNodeFreeInternal_mF1AB16FC5A940C05E458238DBFC5BC0A04D40AFC (void);
// 0x0000005C System.Void UnityEngine.Yoga.Native::YGSetManagedObject(System.IntPtr,UnityEngine.Yoga.YogaNode)
extern void Native_YGSetManagedObject_m956D442A7E5D175AB52901C54E4F1456691F8C5A (void);
// 0x0000005D System.Void UnityEngine.Yoga.Native::YGNodeSetConfig(System.IntPtr,System.IntPtr)
extern void Native_YGNodeSetConfig_m2550E140E21149C6CDEC2DCB80DD9E9D48895F82 (void);
// 0x0000005E System.IntPtr UnityEngine.Yoga.Native::YGConfigGetDefault()
extern void Native_YGConfigGetDefault_m89E2B96C9637A2A250147771D3C6FEAC4D0F458A (void);
// 0x0000005F System.IntPtr UnityEngine.Yoga.Native::YGConfigNew()
extern void Native_YGConfigNew_m11896EDBABF4FAE5486DD882CBF71AB15D3B1EEB (void);
// 0x00000060 System.Void UnityEngine.Yoga.Native::YGConfigFree(System.IntPtr)
extern void Native_YGConfigFree_m39FFBA6301AAACAFE0570ABA725972240550981C (void);
// 0x00000061 System.Void UnityEngine.Yoga.Native::YGConfigFreeInternal(System.IntPtr)
extern void Native_YGConfigFreeInternal_m63E4F7ECDAA151F463EFE996C63586FACD11BB4C (void);
// 0x00000062 System.Void UnityEngine.Yoga.Native::YGConfigSetUseWebDefaults(System.IntPtr,System.Boolean)
extern void Native_YGConfigSetUseWebDefaults_mBA0DEB8659A98F62BD44F809D80DB1B21B9AC55E (void);
// 0x00000063 System.Boolean UnityEngine.Yoga.Native::YGConfigGetUseWebDefaults(System.IntPtr)
extern void Native_YGConfigGetUseWebDefaults_m0F1759A9663FF89B89F3BE95E66B761375602DE2 (void);
// 0x00000064 System.Void UnityEngine.Yoga.Native::YGConfigSetPointScaleFactor(System.IntPtr,System.Single)
extern void Native_YGConfigSetPointScaleFactor_m691E9B0FC19C5C2864A5239B27E8ABC2E00D70D9 (void);
// 0x00000065 System.Void UnityEngine.Yoga.Native::YGNodeInsertChild(System.IntPtr,System.IntPtr,System.UInt32)
extern void Native_YGNodeInsertChild_m3D9EEA4EF0E478F0D04C0C074BFB046FDB103503 (void);
// 0x00000066 System.Void UnityEngine.Yoga.Native::YGNodeRemoveChild(System.IntPtr,System.IntPtr)
extern void Native_YGNodeRemoveChild_m2C8E3F3B387CE8961A8881B5BB77530B60058D68 (void);
// 0x00000067 System.Void UnityEngine.Yoga.Native::YGNodeCalculateLayout(System.IntPtr,System.Single,System.Single,UnityEngine.Yoga.YogaDirection)
extern void Native_YGNodeCalculateLayout_m61971612B736E4A072F2CE9607360F3A0E46CC0F (void);
// 0x00000068 System.Void UnityEngine.Yoga.Native::YGNodeMarkDirty(System.IntPtr)
extern void Native_YGNodeMarkDirty_m198894367903C588630F3798F546B7FBFDB3D3D8 (void);
// 0x00000069 System.Boolean UnityEngine.Yoga.Native::YGNodeIsDirty(System.IntPtr)
extern void Native_YGNodeIsDirty_m3D3979D37CB7D3FC3EF5F0986A3AEEC3CB5A4C68 (void);
// 0x0000006A System.Void UnityEngine.Yoga.Native::YGNodeCopyStyle(System.IntPtr,System.IntPtr)
extern void Native_YGNodeCopyStyle_mB8ACE09355CEE68478C8198F30788AE536D2D1B9 (void);
// 0x0000006B System.Void UnityEngine.Yoga.Native::YGNodeSetMeasureFunc(System.IntPtr)
extern void Native_YGNodeSetMeasureFunc_mF3175AEA27BA26593CDAE27E2C47E08C3AB2F56D (void);
// 0x0000006C System.Void UnityEngine.Yoga.Native::YGNodeRemoveMeasureFunc(System.IntPtr)
extern void Native_YGNodeRemoveMeasureFunc_m4228A419D4F5A31A523E74B9114DB57F3E446363 (void);
// 0x0000006D System.Void UnityEngine.Yoga.Native::YGNodeMeasureInvoke(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.IntPtr)
extern void Native_YGNodeMeasureInvoke_mCDB590CFD8E79635FFA4C9E787FEC4AA48D2A448 (void);
// 0x0000006E System.Void UnityEngine.Yoga.Native::YGNodeBaselineInvoke(UnityEngine.Yoga.YogaNode,System.Single,System.Single,System.IntPtr)
extern void Native_YGNodeBaselineInvoke_m6EDF6191EEF3C742BF22A6E6232D3EA8E6F4C5E0 (void);
// 0x0000006F System.Void UnityEngine.Yoga.Native::YGNodeSetHasNewLayout(System.IntPtr,System.Boolean)
extern void Native_YGNodeSetHasNewLayout_mBE5DBEC31B5DC5AD534B332B102ACDBD8B657D3E (void);
// 0x00000070 System.Boolean UnityEngine.Yoga.Native::YGNodeGetHasNewLayout(System.IntPtr)
extern void Native_YGNodeGetHasNewLayout_m1448EE1836A9A2D0DC3FDB8D38F0655E2E0ECD9E (void);
// 0x00000071 UnityEngine.Yoga.YogaDirection UnityEngine.Yoga.Native::YGNodeStyleGetDirection(System.IntPtr)
extern void Native_YGNodeStyleGetDirection_mD05F2B74EA9428572D5DA28B6B906E4A52568618 (void);
// 0x00000072 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexDirection(System.IntPtr,UnityEngine.Yoga.YogaFlexDirection)
extern void Native_YGNodeStyleSetFlexDirection_m572CB2FFCA3A4F9E4D6F1D8809C410A19BD82C9F (void);
// 0x00000073 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetJustifyContent(System.IntPtr,UnityEngine.Yoga.YogaJustify)
extern void Native_YGNodeStyleSetJustifyContent_m1DA9A504EB3BD07A61941C692C02DD400E44C73D (void);
// 0x00000074 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetAlignContent(System.IntPtr,UnityEngine.Yoga.YogaAlign)
extern void Native_YGNodeStyleSetAlignContent_m4F8EBDBC68466EF490CA01F81FD5945C9CCF5402 (void);
// 0x00000075 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetAlignItems(System.IntPtr,UnityEngine.Yoga.YogaAlign)
extern void Native_YGNodeStyleSetAlignItems_mBC30EB43D186C9F977B415445CD8906CA3DAF61C (void);
// 0x00000076 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetAlignSelf(System.IntPtr,UnityEngine.Yoga.YogaAlign)
extern void Native_YGNodeStyleSetAlignSelf_m4AF9BC14BF935EC05DF59AB5C4A613D2DF9AECA9 (void);
// 0x00000077 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetPositionType(System.IntPtr,UnityEngine.Yoga.YogaPositionType)
extern void Native_YGNodeStyleSetPositionType_mD284119C6D3D043671B09CCFF6C1C864F1D41855 (void);
// 0x00000078 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexWrap(System.IntPtr,UnityEngine.Yoga.YogaWrap)
extern void Native_YGNodeStyleSetFlexWrap_mC9EACF4137A84B413EB0C8C3315D308435FD58D5 (void);
// 0x00000079 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetOverflow(System.IntPtr,UnityEngine.Yoga.YogaOverflow)
extern void Native_YGNodeStyleSetOverflow_mF50DB42B8D5EA25253E77D8F9EBDBA8A4CC8034E (void);
// 0x0000007A System.Void UnityEngine.Yoga.Native::YGNodeStyleSetDisplay(System.IntPtr,UnityEngine.Yoga.YogaDisplay)
extern void Native_YGNodeStyleSetDisplay_mEB1ABE291C84FAEF8112585734A05F5989905B70 (void);
// 0x0000007B System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlex(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetFlex_m0378FB3565379BF2A86E4D32D4CF3D527FC22D7A (void);
// 0x0000007C System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexGrow(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetFlexGrow_mC5D64E0AFD69DCA7D43867F0AFAAC5AAF43CAC16 (void);
// 0x0000007D System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexShrink(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetFlexShrink_mBEB442044933F36011FB1C5D48DC6BB565F22EE5 (void);
// 0x0000007E System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexBasis(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetFlexBasis_mA8EAC51EB221DAA641C0C113B1C888EEC8C134A9 (void);
// 0x0000007F System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexBasisPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetFlexBasisPercent_m53E89CA73D54E0D61C0AE43E46E71880B19E71A3 (void);
// 0x00000080 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetFlexBasisAuto(System.IntPtr)
extern void Native_YGNodeStyleSetFlexBasisAuto_m75E0FE2D72735385BD4B768E7E6595BEB2767B33 (void);
// 0x00000081 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetWidth(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetWidth_m51B0C20BC69889051707A5656AC8E923214BCE28 (void);
// 0x00000082 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetWidthPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetWidthPercent_m0E5E9A14D79DA3097D850A1399E815C7EB5C1711 (void);
// 0x00000083 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetWidthAuto(System.IntPtr)
extern void Native_YGNodeStyleSetWidthAuto_mC0573A277B1B16B80CC8A9791C4477AF191EB0C9 (void);
// 0x00000084 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetHeight(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetHeight_m2E2855E43F07820F05EB8B4EA48E772A8984D405 (void);
// 0x00000085 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetHeightPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetHeightPercent_mB51BB945080154E9C68427923798645B681F3F23 (void);
// 0x00000086 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetHeightAuto(System.IntPtr)
extern void Native_YGNodeStyleSetHeightAuto_mD0A25B33B11FE4A31513388555CCF2666C961454 (void);
// 0x00000087 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMinWidth(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMinWidth_mD4BAEFA7CC9566284701288E58C9E8FFF90D0DC5 (void);
// 0x00000088 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMinWidthPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMinWidthPercent_m511DBAB717BE516FD441F65AE02AC96E7C28DC2A (void);
// 0x00000089 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMinHeight(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMinHeight_mE3932DE6D89F927AB48EE46B75D66A3619680E44 (void);
// 0x0000008A System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMinHeightPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMinHeightPercent_mAD8DF2A468F3BED78662C0FFA09384C31953B18A (void);
// 0x0000008B System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMaxWidth(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMaxWidth_mFA9603DDDDF6E0858A17A52AE09848F917B59A64 (void);
// 0x0000008C System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMaxWidthPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMaxWidthPercent_m629E9CC47E379D44E9772713BA95FD7EB8F3E49B (void);
// 0x0000008D System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMaxHeight(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMaxHeight_m53E372211ECD5CF9E338EB92FC6E812AD16E57A5 (void);
// 0x0000008E System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMaxHeightPercent(System.IntPtr,System.Single)
extern void Native_YGNodeStyleSetMaxHeightPercent_mDD919E0532D33DE34AFA75DA7FCD918AE710C12A (void);
// 0x0000008F System.Void UnityEngine.Yoga.Native::YGNodeStyleSetPosition(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetPosition_m6F05627862225682B30DEE270AF5DBC92A3F5E4A (void);
// 0x00000090 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetPositionPercent(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetPositionPercent_m6EBB551060750C05ED566CCB896C65810C1BB6BC (void);
// 0x00000091 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMargin(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetMargin_m3A70ED59754A7D67E27CE780CBCBB6A4DF585C72 (void);
// 0x00000092 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMarginPercent(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetMarginPercent_m55403B2B8AB39FDB5A3DB015876D5F050CBE2E12 (void);
// 0x00000093 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetMarginAuto(System.IntPtr,UnityEngine.Yoga.YogaEdge)
extern void Native_YGNodeStyleSetMarginAuto_m35CD57DCE2F418647FB611724CD0B06ED5EA91D2 (void);
// 0x00000094 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetPadding(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetPadding_m4EF83ED5DC1934CCE14E4AADEA8A730D2889CAAC (void);
// 0x00000095 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetPaddingPercent(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetPaddingPercent_m38A7998A22EF0102C503F240323511654889D7C6 (void);
// 0x00000096 System.Void UnityEngine.Yoga.Native::YGNodeStyleSetBorder(System.IntPtr,UnityEngine.Yoga.YogaEdge,System.Single)
extern void Native_YGNodeStyleSetBorder_m2FC91E730F7299CD56EC8904D2F14371B9B98BFD (void);
// 0x00000097 System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetLeft(System.IntPtr)
extern void Native_YGNodeLayoutGetLeft_m96B29A4AE1C66E0FEF096DA5050CEC94442C459A (void);
// 0x00000098 System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetTop(System.IntPtr)
extern void Native_YGNodeLayoutGetTop_m5372EE7B9E7CDE267BF9BF0FFC355CB4FD288297 (void);
// 0x00000099 System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetRight(System.IntPtr)
extern void Native_YGNodeLayoutGetRight_m0068329B2B59D0DBBB0DEC009D5B0F1F655B3540 (void);
// 0x0000009A System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetBottom(System.IntPtr)
extern void Native_YGNodeLayoutGetBottom_m6F4451718486D0A549E1D5BEA7C8501DB62ACDA4 (void);
// 0x0000009B System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetWidth(System.IntPtr)
extern void Native_YGNodeLayoutGetWidth_m9D52EC9FB8D5FCC5EE14E070C9092237211EA492 (void);
// 0x0000009C System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetHeight(System.IntPtr)
extern void Native_YGNodeLayoutGetHeight_m5D2329F68FBBBA67CDEE3E673C787C2548514596 (void);
// 0x0000009D System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetMargin(System.IntPtr,UnityEngine.Yoga.YogaEdge)
extern void Native_YGNodeLayoutGetMargin_mCB0EE80823095ADFCA2A8F0A2067DD5B450B0F17 (void);
// 0x0000009E System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetPadding(System.IntPtr,UnityEngine.Yoga.YogaEdge)
extern void Native_YGNodeLayoutGetPadding_mE1699ADBB745BBCA574BAE8DB57CA430A1224BBA (void);
// 0x0000009F System.Single UnityEngine.Yoga.Native::YGNodeLayoutGetBorder(System.IntPtr,UnityEngine.Yoga.YogaEdge)
extern void Native_YGNodeLayoutGetBorder_m5F0B38BD792C29209B64DE9F6C0CD6950146A861 (void);
// 0x000000A0 UnityEngine.Yoga.YogaUnit UnityEngine.Yoga.YogaValue::get_Unit()
extern void YogaValue_get_Unit_m4387357877C0D183FAFD5A3857AEF4C3E52CA900 (void);
// 0x000000A1 System.Single UnityEngine.Yoga.YogaValue::get_Value()
extern void YogaValue_get_Value_m142314AA36484CD328E08A06D6A750F5CA1C112A (void);
// 0x000000A2 UnityEngine.Yoga.YogaValue UnityEngine.Yoga.YogaValue::Point(System.Single)
extern void YogaValue_Point_m1EF4416D27335FE09A3CD7D3CD8307763FA991BE (void);
// 0x000000A3 System.Boolean UnityEngine.Yoga.YogaValue::Equals(UnityEngine.Yoga.YogaValue)
extern void YogaValue_Equals_m7D39A876BF907A38C1F82FCF5303B9AD3CD1BC3F (void);
// 0x000000A4 System.Boolean UnityEngine.Yoga.YogaValue::Equals(System.Object)
extern void YogaValue_Equals_mC37A099D3DB33896B40843065EC84D6F290FCCBD (void);
// 0x000000A5 System.Int32 UnityEngine.Yoga.YogaValue::GetHashCode()
extern void YogaValue_GetHashCode_m8E287A9A127C7B15B870756A948C3BB6C4A12672 (void);
// 0x000000A6 UnityEngine.Yoga.YogaValue UnityEngine.Yoga.YogaValue::Auto()
extern void YogaValue_Auto_m2410501B6BB747D8290711BB328EB5FF7EAF73B5 (void);
// 0x000000A7 UnityEngine.Yoga.YogaValue UnityEngine.Yoga.YogaValue::Percent(System.Single)
extern void YogaValue_Percent_m3A88462FA1C96A48737C78DC5B538B5CC7331C18 (void);
// 0x000000A8 UnityEngine.Yoga.YogaValue UnityEngine.Yoga.YogaValue::op_Implicit(System.Single)
extern void YogaValue_op_Implicit_m4E6C27B18E3D7AEAB238CD7E520852BF732F28F7 (void);
// 0x000000A9 System.Void UnityEngine.Yoga.Logger::.ctor(System.Object,System.IntPtr)
extern void Logger__ctor_m411B63478FF6F8FEDFB36E338920ECF6D44FCE89 (void);
// 0x000000AA System.Void UnityEngine.Yoga.Logger::Invoke(UnityEngine.Yoga.YogaConfig,UnityEngine.Yoga.YogaNode,UnityEngine.Yoga.YogaLogLevel,System.String)
extern void Logger_Invoke_mB64732D7138E5BFC958D540B1F27686BFAC815CF (void);
// 0x000000AB System.Void UnityEngine.Yoga.MeasureFunction::.ctor(System.Object,System.IntPtr)
extern void MeasureFunction__ctor_mE08DFEFBD622065D2E123492910EA66C4A80A0BA (void);
// 0x000000AC UnityEngine.Yoga.YogaSize UnityEngine.Yoga.MeasureFunction::Invoke(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode)
extern void MeasureFunction_Invoke_m280560A27915B9D2F3D7E75056A63084925EEFCE (void);
// 0x000000AD System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RepaintOverlayPanels()
extern void UIElementsRuntimeUtilityNative_RepaintOverlayPanels_m346BC3BDB4713023D04B36C35E1D5C20465A815A (void);
// 0x000000AE System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UpdateRuntimePanels()
extern void UIElementsRuntimeUtilityNative_UpdateRuntimePanels_mB08CCB60AFCD7B71AA0E1F50DF697BFCFA8DC376 (void);
// 0x000000AF System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RepaintOffscreenPanels()
extern void UIElementsRuntimeUtilityNative_RepaintOffscreenPanels_mFDED6CDF8F9B24FB98A64F785574AC710B173832 (void);
// 0x000000B0 System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RegisterPlayerloopCallback()
extern void UIElementsRuntimeUtilityNative_RegisterPlayerloopCallback_m04D189811F7F648F186D5976D21EDF13BA986A71 (void);
// 0x000000B1 System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UnregisterPlayerloopCallback()
extern void UIElementsRuntimeUtilityNative_UnregisterPlayerloopCallback_m412B040F225984650BD09B9E8693AF6127D97ADF (void);
// 0x000000B2 System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::VisualElementCreation()
extern void UIElementsRuntimeUtilityNative_VisualElementCreation_m4FEEF03A1322277172416B770CB029247D7FCDFD (void);
// 0x000000B3 UnityEngine.Vector2 UnityEngine.UIElements.TextNative::GetCursorPosition(UnityEngine.UIElements.TextNativeSettings,UnityEngine.Rect,System.Int32)
extern void TextNative_GetCursorPosition_mF7B2D51841761A24623EE9DCAA354F30CB9523F4 (void);
// 0x000000B4 System.Single UnityEngine.UIElements.TextNative::ComputeTextWidth(UnityEngine.UIElements.TextNativeSettings)
extern void TextNative_ComputeTextWidth_mE3A73219F780DB79A4BFCEA91CA1C4DB9AF7F00F (void);
// 0x000000B5 System.Single UnityEngine.UIElements.TextNative::ComputeTextHeight(UnityEngine.UIElements.TextNativeSettings)
extern void TextNative_ComputeTextHeight_m2DB34E72014B203E83A17A7321793ED979D36AD8 (void);
// 0x000000B6 Unity.Collections.NativeArray`1<UnityEngine.UIElements.TextVertex> UnityEngine.UIElements.TextNative::GetVertices(UnityEngine.UIElements.TextNativeSettings)
extern void TextNative_GetVertices_m877716FCDD7F1D2C5849487C88691A5BA2441088 (void);
// 0x000000B7 UnityEngine.Vector2 UnityEngine.UIElements.TextNative::GetOffset(UnityEngine.UIElements.TextNativeSettings,UnityEngine.Rect)
extern void TextNative_GetOffset_mBBAE5C3304645D4295B723ABC9245AD6751ECD36 (void);
// 0x000000B8 System.Single UnityEngine.UIElements.TextNative::ComputeTextScaling(UnityEngine.Matrix4x4,System.Single)
extern void TextNative_ComputeTextScaling_mB18E1C1AD82EAD2A557414E4634F2F89149FFA2B (void);
// 0x000000B9 System.Single UnityEngine.UIElements.TextNative::DoComputeTextWidth(UnityEngine.UIElements.TextNativeSettings)
extern void TextNative_DoComputeTextWidth_mC1ADB603729FAA6D6437B1E2031224A386FE1F9F (void);
// 0x000000BA System.Single UnityEngine.UIElements.TextNative::DoComputeTextHeight(UnityEngine.UIElements.TextNativeSettings)
extern void TextNative_DoComputeTextHeight_m917EE94AF5471D5E962AB3BEB0C7592A7F917BC2 (void);
// 0x000000BB UnityEngine.Vector2 UnityEngine.UIElements.TextNative::DoGetCursorPosition(UnityEngine.UIElements.TextNativeSettings,UnityEngine.Rect,System.Int32)
extern void TextNative_DoGetCursorPosition_m88B61DC7C5E4F17AC5E75F54D9B9B9F56F14F290 (void);
// 0x000000BC System.Void UnityEngine.UIElements.TextNative::GetVertices(UnityEngine.UIElements.TextNativeSettings,System.IntPtr,System.Int32,System.Int32&)
extern void TextNative_GetVertices_m21E5914758FA0C969689CAA41EE14D265DAEED05 (void);
// 0x000000BD UnityEngine.Vector2 UnityEngine.UIElements.TextNative::DoGetOffset(UnityEngine.UIElements.TextNativeSettings,UnityEngine.Rect)
extern void TextNative_DoGetOffset_m65E9D221159D69B6AA2F3B9F93EB010A5709BE05 (void);
// 0x000000BE System.Single UnityEngine.UIElements.TextNative::DoComputeTextWidth_Injected(UnityEngine.UIElements.TextNativeSettings&)
extern void TextNative_DoComputeTextWidth_Injected_m1E6872A8C0AC95CFDF74C202595553A1E470472F (void);
// 0x000000BF System.Single UnityEngine.UIElements.TextNative::DoComputeTextHeight_Injected(UnityEngine.UIElements.TextNativeSettings&)
extern void TextNative_DoComputeTextHeight_Injected_m69A3B288A6F117F0A596A2B7222C398D6008AE7B (void);
// 0x000000C0 System.Void UnityEngine.UIElements.TextNative::DoGetCursorPosition_Injected(UnityEngine.UIElements.TextNativeSettings&,UnityEngine.Rect&,System.Int32,UnityEngine.Vector2&)
extern void TextNative_DoGetCursorPosition_Injected_mC5C07A73AF3CC29F5E6333E7CF1D33DF1633B9BA (void);
// 0x000000C1 System.Void UnityEngine.UIElements.TextNative::GetVertices_Injected(UnityEngine.UIElements.TextNativeSettings&,System.IntPtr,System.Int32,System.Int32&)
extern void TextNative_GetVertices_Injected_mB5CAF8BEBFB7228E06BD36FB3B9C188C1A12127F (void);
// 0x000000C2 System.Void UnityEngine.UIElements.TextNative::DoGetOffset_Injected(UnityEngine.UIElements.TextNativeSettings&,UnityEngine.Rect&,UnityEngine.Vector2&)
extern void TextNative_DoGetOffset_Injected_m5A58EA8CF5C12A4E039224EFFD860A022BC15156 (void);
// 0x000000C3 System.Void UnityEngine.UIElements.UIR.Utility::SetVectorArray(UnityEngine.MaterialPropertyBlock,System.Int32,Unity.Collections.NativeSlice`1<T>)
// 0x000000C4 System.Void UnityEngine.UIElements.UIR.Utility::add_GraphicsResourcesRecreate(System.Action`1<System.Boolean>)
extern void Utility_add_GraphicsResourcesRecreate_m7500B21EEAD18C1D395CA679C4C1B2A10AE1B962 (void);
// 0x000000C5 System.Void UnityEngine.UIElements.UIR.Utility::remove_GraphicsResourcesRecreate(System.Action`1<System.Boolean>)
extern void Utility_remove_GraphicsResourcesRecreate_mB4DF54BC3B178674658360B438A1CA823A1C73AC (void);
// 0x000000C6 System.Void UnityEngine.UIElements.UIR.Utility::add_EngineUpdate(System.Action)
extern void Utility_add_EngineUpdate_m06DF99E8301C5BD159A6AAE914BDCF808BA92B32 (void);
// 0x000000C7 System.Void UnityEngine.UIElements.UIR.Utility::remove_EngineUpdate(System.Action)
extern void Utility_remove_EngineUpdate_mF7A954E84FC896CC63CB68CAF8A5235F835FF2D7 (void);
// 0x000000C8 System.Void UnityEngine.UIElements.UIR.Utility::add_FlushPendingResources(System.Action)
extern void Utility_add_FlushPendingResources_m9A930FD6215580544B7AFAB6CA57D9581833FBEA (void);
// 0x000000C9 System.Void UnityEngine.UIElements.UIR.Utility::remove_FlushPendingResources(System.Action)
extern void Utility_remove_FlushPendingResources_mBA31F8A56E659DE7298C2656B05D835178E5EE0D (void);
// 0x000000CA System.Void UnityEngine.UIElements.UIR.Utility::add_RegisterIntermediateRenderers(System.Action`1<UnityEngine.Camera>)
extern void Utility_add_RegisterIntermediateRenderers_m7E13A9C3E01341D429E0999F89F580232FFC6429 (void);
// 0x000000CB System.Void UnityEngine.UIElements.UIR.Utility::remove_RegisterIntermediateRenderers(System.Action`1<UnityEngine.Camera>)
extern void Utility_remove_RegisterIntermediateRenderers_mEF3C851756F576BD728BD8BFFE5C5A64597539E5 (void);
// 0x000000CC System.Void UnityEngine.UIElements.UIR.Utility::add_RenderNodeExecute(System.Action`1<System.IntPtr>)
extern void Utility_add_RenderNodeExecute_mA892466BF824D857F435453B8EA832382DC3314A (void);
// 0x000000CD System.Void UnityEngine.UIElements.UIR.Utility::remove_RenderNodeExecute(System.Action`1<System.IntPtr>)
extern void Utility_remove_RenderNodeExecute_m240A07023F25797A1827351E49329A02A122AD6C (void);
// 0x000000CE System.Void UnityEngine.UIElements.UIR.Utility::RaiseGraphicsResourcesRecreate(System.Boolean)
extern void Utility_RaiseGraphicsResourcesRecreate_m836B6EF1199237DBF417FA6F803ECD85E221201B (void);
// 0x000000CF System.Void UnityEngine.UIElements.UIR.Utility::RaiseEngineUpdate()
extern void Utility_RaiseEngineUpdate_m7AB4CDD154B44E8B1E224F5BC180640AD1749B1F (void);
// 0x000000D0 System.Void UnityEngine.UIElements.UIR.Utility::RaiseFlushPendingResources()
extern void Utility_RaiseFlushPendingResources_mB13980DB6EAEE1060226EB7DFA6CCEEB57D9CE68 (void);
// 0x000000D1 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRegisterIntermediateRenderers(UnityEngine.Camera)
extern void Utility_RaiseRegisterIntermediateRenderers_mABFC7BADB09B3ADECB04E0E4D1B52ECD161C9F9E (void);
// 0x000000D2 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRenderNodeAdd(System.IntPtr)
extern void Utility_RaiseRenderNodeAdd_mC61730515A6F1CB102C9D98DB7A85763EBDF5D96 (void);
// 0x000000D3 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRenderNodeExecute(System.IntPtr)
extern void Utility_RaiseRenderNodeExecute_mA31EAEA7E25A4EB997B70359272900579DAA5446 (void);
// 0x000000D4 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRenderNodeCleanup(System.IntPtr)
extern void Utility_RaiseRenderNodeCleanup_mEF39A08F8A41ACDD38F7BF073F5B3B545A048B0C (void);
// 0x000000D5 System.IntPtr UnityEngine.UIElements.UIR.Utility::AllocateBuffer(System.Int32,System.Int32,System.Boolean)
extern void Utility_AllocateBuffer_m8DC30A1D8EF56C1F02B665FF7E60887E30A21114 (void);
// 0x000000D6 System.Void UnityEngine.UIElements.UIR.Utility::FreeBuffer(System.IntPtr)
extern void Utility_FreeBuffer_m3994CCBBB9E8C8B063CC8554EE0DFBE244EA427B (void);
// 0x000000D7 System.Void UnityEngine.UIElements.UIR.Utility::UpdateBufferRanges(System.IntPtr,System.IntPtr,System.Int32,System.Int32,System.Int32)
extern void Utility_UpdateBufferRanges_mDAE14652D11D1AABBF39FD3AC71CC8B3A0880DAA (void);
// 0x000000D8 System.Void UnityEngine.UIElements.UIR.Utility::SetVectorArray(UnityEngine.MaterialPropertyBlock,System.Int32,System.IntPtr,System.Int32)
extern void Utility_SetVectorArray_m36650D854AAE55F689FF622D36CC2AC0B9FD7FB1 (void);
// 0x000000D9 System.IntPtr UnityEngine.UIElements.UIR.Utility::GetVertexDeclaration(UnityEngine.Rendering.VertexAttributeDescriptor[])
extern void Utility_GetVertexDeclaration_mB8CDEF6DF2CBA67EE88C0FB666CDC338556F329F (void);
// 0x000000DA System.Void UnityEngine.UIElements.UIR.Utility::RegisterIntermediateRenderer(UnityEngine.Camera,UnityEngine.Material,UnityEngine.Matrix4x4,UnityEngine.Bounds,System.Int32,System.Int32,System.Boolean,System.Int32,System.UInt64,System.Int32,System.IntPtr,System.Int32)
extern void Utility_RegisterIntermediateRenderer_m19E56570934E5AE455A1FE959EAB203D284DA4FA (void);
// 0x000000DB System.Void UnityEngine.UIElements.UIR.Utility::DrawRanges(System.IntPtr,System.IntPtr*,System.Int32,System.IntPtr,System.Int32,System.IntPtr)
extern void Utility_DrawRanges_m6F5AE31B6A857DB71F66A2662C6EBE1B218B6FF4 (void);
// 0x000000DC System.Void UnityEngine.UIElements.UIR.Utility::SetPropertyBlock(UnityEngine.MaterialPropertyBlock)
extern void Utility_SetPropertyBlock_m04316F1E2FCAF1F9DDBEBDFE542EEBC3120C4EA4 (void);
// 0x000000DD System.Void UnityEngine.UIElements.UIR.Utility::SetScissorRect(UnityEngine.RectInt)
extern void Utility_SetScissorRect_mFB429FC493F3679035007BF759880159DDBFC049 (void);
// 0x000000DE System.Void UnityEngine.UIElements.UIR.Utility::DisableScissor()
extern void Utility_DisableScissor_m7B52FA0EC1106927817854145DA8A862B3F2624C (void);
// 0x000000DF System.IntPtr UnityEngine.UIElements.UIR.Utility::CreateStencilState(UnityEngine.Rendering.StencilState)
extern void Utility_CreateStencilState_mDC1F4473214745D1BE75D104081E73B52CCD043B (void);
// 0x000000E0 System.Void UnityEngine.UIElements.UIR.Utility::SetStencilState(System.IntPtr,System.Int32)
extern void Utility_SetStencilState_m6F1F46336FF696C8C6A360B5E0CB344AC193A9E1 (void);
// 0x000000E1 System.Boolean UnityEngine.UIElements.UIR.Utility::HasMappedBufferRange()
extern void Utility_HasMappedBufferRange_m9701BED18820E4604C7C46DD1615E1272483C043 (void);
// 0x000000E2 System.UInt32 UnityEngine.UIElements.UIR.Utility::InsertCPUFence()
extern void Utility_InsertCPUFence_m8FC65EF05973588366BC403C8807897CEE7CF440 (void);
// 0x000000E3 System.Boolean UnityEngine.UIElements.UIR.Utility::CPUFencePassed(System.UInt32)
extern void Utility_CPUFencePassed_mBF5F842311E4ABF3DE18E9CA26A3684C21D836A5 (void);
// 0x000000E4 System.Void UnityEngine.UIElements.UIR.Utility::WaitForCPUFencePassed(System.UInt32)
extern void Utility_WaitForCPUFencePassed_m5DC66BA62BAA3A503311E5EFE480BA763BA40CBB (void);
// 0x000000E5 System.Void UnityEngine.UIElements.UIR.Utility::SyncRenderThread()
extern void Utility_SyncRenderThread_m4A7091ECD29F7A1CEF970D70F52F8C19696D6010 (void);
// 0x000000E6 UnityEngine.RectInt UnityEngine.UIElements.UIR.Utility::GetActiveViewport()
extern void Utility_GetActiveViewport_mDB9165183361EBE3D7D6D673EA147472C7FC9F14 (void);
// 0x000000E7 System.Void UnityEngine.UIElements.UIR.Utility::ProfileDrawChainBegin()
extern void Utility_ProfileDrawChainBegin_m28C587F98561B8F4C6ED13AEEE83430A25D3F83B (void);
// 0x000000E8 System.Void UnityEngine.UIElements.UIR.Utility::ProfileDrawChainEnd()
extern void Utility_ProfileDrawChainEnd_mF3602959ACC39C6EAE6429CC58172A06851E050D (void);
// 0x000000E9 System.Void UnityEngine.UIElements.UIR.Utility::NotifyOfUIREvents(System.Boolean)
extern void Utility_NotifyOfUIREvents_m84DA51777B615A0FA57AE784519A530F23434B47 (void);
// 0x000000EA UnityEngine.Matrix4x4 UnityEngine.UIElements.UIR.Utility::GetUnityProjectionMatrix()
extern void Utility_GetUnityProjectionMatrix_mDAA7B9D416768C0A81392BE9F27D551650EFF1D9 (void);
// 0x000000EB System.Void UnityEngine.UIElements.UIR.Utility::.cctor()
extern void Utility__cctor_m8AD41F9B37AF2853080C9A52DEFDCE5E6693C3FC (void);
// 0x000000EC System.Void UnityEngine.UIElements.UIR.Utility::RegisterIntermediateRenderer_Injected(UnityEngine.Camera,UnityEngine.Material,UnityEngine.Matrix4x4&,UnityEngine.Bounds&,System.Int32,System.Int32,System.Boolean,System.Int32,System.UInt64,System.Int32,System.IntPtr,System.Int32)
extern void Utility_RegisterIntermediateRenderer_Injected_m4CBBCDD753216F330B41766DA7C0D217FE281D64 (void);
// 0x000000ED System.Void UnityEngine.UIElements.UIR.Utility::SetScissorRect_Injected(UnityEngine.RectInt&)
extern void Utility_SetScissorRect_Injected_m333581839C1FABCFAEE7EFBDF945ECEF7CCAE610 (void);
// 0x000000EE System.IntPtr UnityEngine.UIElements.UIR.Utility::CreateStencilState_Injected(UnityEngine.Rendering.StencilState&)
extern void Utility_CreateStencilState_Injected_m47D217E56E67F17F06B389BF566BF0566D0F5479 (void);
// 0x000000EF System.Void UnityEngine.UIElements.UIR.Utility::GetActiveViewport_Injected(UnityEngine.RectInt&)
extern void Utility_GetActiveViewport_Injected_mA7FB4F05C7BDF5ABC71D228CE66B33954507C46D (void);
// 0x000000F0 System.Void UnityEngine.UIElements.UIR.Utility::GetUnityProjectionMatrix_Injected(UnityEngine.Matrix4x4&)
extern void Utility_GetUnityProjectionMatrix_Injected_m9E6F597B899AD99C931FBAFC0363B6F6E666CA81 (void);
// 0x000000F1 System.Void UnityEngine.UIElements.UIR.Utility/GPUBuffer`1::.ctor(System.Int32,UnityEngine.UIElements.UIR.Utility/GPUBufferType)
// 0x000000F2 System.Void UnityEngine.UIElements.UIR.Utility/GPUBuffer`1::Dispose()
// 0x000000F3 System.Void UnityEngine.UIElements.UIR.Utility/GPUBuffer`1::UpdateRanges(Unity.Collections.NativeSlice`1<UnityEngine.UIElements.UIR.GfxUpdateBufferRange>,System.Int32,System.Int32)
// 0x000000F4 System.Int32 UnityEngine.UIElements.UIR.Utility/GPUBuffer`1::get_ElementStride()
// 0x000000F5 System.IntPtr UnityEngine.UIElements.UIR.Utility/GPUBuffer`1::get_BufferPointer()
static Il2CppMethodPointer s_methodPointers[245] = 
{
	YogaConfig__ctor_mA5B9DCE1F40B5A6948D3D8848516F2CBCD2FABF4,
	YogaConfig__ctor_m81D3E486916576BD67674EA5F26C8F2B45CCB6DD,
	YogaConfig_Finalize_m395FFF204239D6A9DC076105B4D935706B29D631,
	YogaConfig_get_Handle_m7E2D8171E4E8AA98BC1D886218B6207D602281DD,
	YogaConfig_get_UseWebDefaults_mB64415ACFDF7FB34F6F5BB42059BFD8E3705AD9D,
	YogaConfig_set_UseWebDefaults_m96AB3442B80FDA12436C4452D8605AA8DA1C8B81,
	YogaConfig_set_PointScaleFactor_mF61D0EFB38778BC4B6DE323F53348485B8AC643D,
	YogaConfig__cctor_m2CBFE98AAE90596CBB749697AAE1973B2AE051FC,
	MeasureOutput_Make_m19BCA4677D3255795D936162FB6DE021C2C89E83,
	BaselineFunction__ctor_m525AED7069E4DFB2C8770618315000F96E7FD500,
	BaselineFunction_Invoke_m2DDB6CB96A11C1AF2F557FB363F99BA3A2E6E109,
	YogaConstants_IsUndefined_m1BD66A8B7482CB49C9D4B0241E88CCE8EB0743FB,
	YogaNode__ctor_m824433D9174C4325E87EC380CD5EB5F10C20A35C,
	YogaNode_Finalize_mD0E1733478B7861392542C1A2F8161B462CD6327,
	YogaNode_set_Config_mFEE688C9B0B7EFFE581F278746A7C4CD76449DE5,
	YogaNode_get_IsDirty_m24743ABB33BC9F32F946B3FD9C5DB85F77285781,
	YogaNode_MarkDirty_mCCCABC1717DCAF3E313846069AD503959B184930,
	YogaNode_get_HasNewLayout_m801E00B631071A29A968ACA0489B1BDC2CE3CE05,
	YogaNode_get_IsMeasureDefined_m14B27C81AD102F307669F55E3FDFEE7E7E61B7EC,
	YogaNode_get_IsBaselineDefined_m7E3BFBBB4F59F44881E6A58B342FABB672119DF5,
	YogaNode_CopyStyle_mB3AFC6604AA23297A7DA93E5DE9A36CC3CD4B65C,
	YogaNode_set_FlexDirection_mFF74AB011A465EFD90BAFDE41F00207619429306,
	YogaNode_set_JustifyContent_m008464E5DA37AEDD2DDA37E89CEB2E3A1C25C286,
	YogaNode_set_Display_mCD8A7B298E87852734559A41DC01EF96827032C2,
	YogaNode_set_AlignItems_m8443F468878AF728A2F82505F4B53D2065DA89BF,
	YogaNode_set_AlignSelf_mD678D097FA3DB20EF1568A9B17C7423CC8B0BCBF,
	YogaNode_set_AlignContent_m9AC19A64AAAACEB85DA4FCF94D9372A4556F0845,
	YogaNode_set_PositionType_m958F7BB665C87711439BED68CCB9E7C63798FEA5,
	YogaNode_set_Wrap_m01F365128F74AC617966A2B3EEF340150AB59EE6,
	YogaNode_set_Flex_mCFCB82869C82DD5CB40151681576F4A40F381D6A,
	YogaNode_set_FlexGrow_m884FDA11A3F7FAF051CB840829442FE14CC3CC2B,
	YogaNode_set_FlexShrink_m30FC430AB92EB6FF05B394D25D728DC4DB2B4FA5,
	YogaNode_set_FlexBasis_m24CCC6C38C4612359E942EEF5EA5FDF4D24E7671,
	YogaNode_set_Width_mA267DDF44981884C4C630FCBC602595F58AB05E8,
	YogaNode_set_Height_m4AFF9C286F42919CA1C5C7A152A4ED477C56969E,
	YogaNode_set_MaxWidth_m18194AF8ECC926D70493BAC8C8041349DCF0DE43,
	YogaNode_set_MaxHeight_m25F7366BA8516D5C4B3EBDA5A797B33704A4CAB8,
	YogaNode_set_MinWidth_mC876A8736AA69BC6100AA5E361A0E9190D02DB21,
	YogaNode_set_MinHeight_mB3301DB6766234FE7D594DE46DF59C31F0F29643,
	YogaNode_get_LayoutX_mEEE47120D6DB656AC643A1294AFE3CC79E93C492,
	YogaNode_get_LayoutY_m79DCF02D705920434DCE9B534FFAAC936A268173,
	YogaNode_get_LayoutRight_mEBCE0188575C7FE3D72255011803E1EC56685F24,
	YogaNode_get_LayoutBottom_m2F6857AB410F79908EA7098DA19F09BE07245269,
	YogaNode_get_LayoutWidth_mF68064634C5682A2C0C70DAAF6CB45C39D3F216A,
	YogaNode_get_LayoutHeight_m0A56155F1B067D0127E5A38204C2F0A9BAB605BD,
	YogaNode_set_Overflow_m5DD8FEB426E1376D210ED0057C4A12DD8E18417F,
	YogaNode_get_Count_mBBD0D15ACBBA109563C7D22EAB1F58094C4562AD,
	YogaNode_MarkLayoutSeen_m899C39B9392134C5B7835217D18FCC8D5E7A1E5A,
	YogaNode_Insert_m9182FC436BFB915BDAB6492465B6E7832B1921CF,
	YogaNode_RemoveAt_m344D767FF02FB69813F270953ACFEE28E5DEF83F,
	YogaNode_Clear_mCB7D5DF9967646CFD9A156DEAC56E13A0BA60826,
	YogaNode_SetMeasureFunction_mD658FA9C0543C022DB09D54B49BA38354B558D04,
	YogaNode_CalculateLayout_mF1185A522FA0E60BA47039893A3EE3419B6F1D37,
	YogaNode_MeasureInternal_m48B8FB32DE181D6CF67675FE8FCE8B5947CCBFF0,
	YogaNode_BaselineInternal_m1CB75FF4F21CC040899903C1BAA548691A94D757,
	YogaNode_GetEnumerator_m93CA3A3DC1ED5F958FB6E7D3CFB534F9A374B394,
	YogaNode_System_Collections_IEnumerable_GetEnumerator_mF3D17F0F0C2E0C5FA4ECE8EDE724F0E332E900FA,
	YogaNode_set_Left_m45FA7CF627DF74C1F795DA1488EF943DA77067A6,
	YogaNode_set_Top_m2F3AE8DA9025B7A1EB370C67C8B4560BA23CE5A4,
	YogaNode_set_Right_m35D7139ECEB7CC1B82A5DCFBFDC96EA9AFC686E5,
	YogaNode_set_Bottom_m03A39B84DE5056608BCFE43E98956BF58035347F,
	YogaNode_SetStylePosition_m17081B8019875ACCC6D5215A1FB0D6B64C9FD7CF,
	YogaNode_set_MarginLeft_m6DD0D35E142EB59ED9280D1C4EA2F038C132FC4B,
	YogaNode_set_MarginTop_mA43AB8E85422F7980BECF2DEF66F736C27E8E972,
	YogaNode_set_MarginRight_m4D2DF290FF89E2A9CF33DA815985B79DFA864A3C,
	YogaNode_set_MarginBottom_m0CCF6AF5FC2C6ACA8E2E084AABC83322A215AEEC,
	YogaNode_SetStyleMargin_m9F59AA83FE83F289CC286EF0C33F8558055A8065,
	YogaNode_set_PaddingLeft_mDC797F410F72554FAD67A14DC5002656C985D06D,
	YogaNode_set_PaddingTop_mE59506FA241E2BFD3AC4CEB219000993CC950F01,
	YogaNode_set_PaddingRight_mA277C8926B03AE1FE89F46A6A24882D61155E7F8,
	YogaNode_set_PaddingBottom_mD4BE8EAD7054982975BB95088DC507B355B57D32,
	YogaNode_SetStylePadding_m4591446410152C5EFEEFC41D208431CEDFBBF707,
	YogaNode_set_BorderLeftWidth_m6F0ADE17EA294DC33E0EBBB17E4DF867B5E73CA5,
	YogaNode_set_BorderTopWidth_m39A26DC4E61833C0F8F58EA28A71AA35C4553005,
	YogaNode_set_BorderRightWidth_m047EE6ECCF13C9E44885BCE8FA20D2FD0DA498C6,
	YogaNode_set_BorderBottomWidth_m10BD7CB272CE0342EEA05F413A78BB6CE34FD8F3,
	YogaNode_get_LayoutMarginLeft_mBBDC00F49301F60C09C5B3BF8782EAB5C814DFB4,
	YogaNode_get_LayoutMarginTop_m010905C6DDD8C42E540AAB4DDCD2AFA6FFE13BE6,
	YogaNode_get_LayoutMarginRight_mE6BDC383CDA9AFD8C827B928A86EF13A8D50A566,
	YogaNode_get_LayoutMarginBottom_m46D9999CE1CF2957DE68BA4024B36F9A50F08151,
	YogaNode_get_LayoutPaddingLeft_m315AB18C71CFF8207E9DBC8D7538BFE0A0569421,
	YogaNode_get_LayoutPaddingTop_m76AB547D25D8C51B5CA987BD7D8D586AF284E8D8,
	YogaNode_get_LayoutPaddingRight_m3AB9145B687CB1ADE6B6EFBDBBF2928301B0FEB1,
	YogaNode_get_LayoutPaddingBottom_m08100BDABCA07492B6BBF8FD57BCB06AD460049C,
	YogaNode_get_LayoutBorderLeft_mC799A7EC6C1ED244DDD852634ACC768CEADFD366,
	YogaNode_get_LayoutBorderTop_m5FEAEE25D61F72B530BCC9B8B11A374BAD94D637,
	YogaNode_get_LayoutBorderRight_mC22B436C97ABEF06221F3BF9F1506E527E9CEF99,
	YogaNode_get_LayoutBorderBottom_m06786864F2149A50CBD5F551977E46737A614D91,
	Native_YGNodeNewWithConfig_m6F611BD851C531FBCBB7E5BCC06886089DAF92FD,
	Native_YGNodeFree_m65A5C6F9FAF9C804FE3299AF793B314D89C940CA,
	Native_YGNodeFreeInternal_mF1AB16FC5A940C05E458238DBFC5BC0A04D40AFC,
	Native_YGSetManagedObject_m956D442A7E5D175AB52901C54E4F1456691F8C5A,
	Native_YGNodeSetConfig_m2550E140E21149C6CDEC2DCB80DD9E9D48895F82,
	Native_YGConfigGetDefault_m89E2B96C9637A2A250147771D3C6FEAC4D0F458A,
	Native_YGConfigNew_m11896EDBABF4FAE5486DD882CBF71AB15D3B1EEB,
	Native_YGConfigFree_m39FFBA6301AAACAFE0570ABA725972240550981C,
	Native_YGConfigFreeInternal_m63E4F7ECDAA151F463EFE996C63586FACD11BB4C,
	Native_YGConfigSetUseWebDefaults_mBA0DEB8659A98F62BD44F809D80DB1B21B9AC55E,
	Native_YGConfigGetUseWebDefaults_m0F1759A9663FF89B89F3BE95E66B761375602DE2,
	Native_YGConfigSetPointScaleFactor_m691E9B0FC19C5C2864A5239B27E8ABC2E00D70D9,
	Native_YGNodeInsertChild_m3D9EEA4EF0E478F0D04C0C074BFB046FDB103503,
	Native_YGNodeRemoveChild_m2C8E3F3B387CE8961A8881B5BB77530B60058D68,
	Native_YGNodeCalculateLayout_m61971612B736E4A072F2CE9607360F3A0E46CC0F,
	Native_YGNodeMarkDirty_m198894367903C588630F3798F546B7FBFDB3D3D8,
	Native_YGNodeIsDirty_m3D3979D37CB7D3FC3EF5F0986A3AEEC3CB5A4C68,
	Native_YGNodeCopyStyle_mB8ACE09355CEE68478C8198F30788AE536D2D1B9,
	Native_YGNodeSetMeasureFunc_mF3175AEA27BA26593CDAE27E2C47E08C3AB2F56D,
	Native_YGNodeRemoveMeasureFunc_m4228A419D4F5A31A523E74B9114DB57F3E446363,
	Native_YGNodeMeasureInvoke_mCDB590CFD8E79635FFA4C9E787FEC4AA48D2A448,
	Native_YGNodeBaselineInvoke_m6EDF6191EEF3C742BF22A6E6232D3EA8E6F4C5E0,
	Native_YGNodeSetHasNewLayout_mBE5DBEC31B5DC5AD534B332B102ACDBD8B657D3E,
	Native_YGNodeGetHasNewLayout_m1448EE1836A9A2D0DC3FDB8D38F0655E2E0ECD9E,
	Native_YGNodeStyleGetDirection_mD05F2B74EA9428572D5DA28B6B906E4A52568618,
	Native_YGNodeStyleSetFlexDirection_m572CB2FFCA3A4F9E4D6F1D8809C410A19BD82C9F,
	Native_YGNodeStyleSetJustifyContent_m1DA9A504EB3BD07A61941C692C02DD400E44C73D,
	Native_YGNodeStyleSetAlignContent_m4F8EBDBC68466EF490CA01F81FD5945C9CCF5402,
	Native_YGNodeStyleSetAlignItems_mBC30EB43D186C9F977B415445CD8906CA3DAF61C,
	Native_YGNodeStyleSetAlignSelf_m4AF9BC14BF935EC05DF59AB5C4A613D2DF9AECA9,
	Native_YGNodeStyleSetPositionType_mD284119C6D3D043671B09CCFF6C1C864F1D41855,
	Native_YGNodeStyleSetFlexWrap_mC9EACF4137A84B413EB0C8C3315D308435FD58D5,
	Native_YGNodeStyleSetOverflow_mF50DB42B8D5EA25253E77D8F9EBDBA8A4CC8034E,
	Native_YGNodeStyleSetDisplay_mEB1ABE291C84FAEF8112585734A05F5989905B70,
	Native_YGNodeStyleSetFlex_m0378FB3565379BF2A86E4D32D4CF3D527FC22D7A,
	Native_YGNodeStyleSetFlexGrow_mC5D64E0AFD69DCA7D43867F0AFAAC5AAF43CAC16,
	Native_YGNodeStyleSetFlexShrink_mBEB442044933F36011FB1C5D48DC6BB565F22EE5,
	Native_YGNodeStyleSetFlexBasis_mA8EAC51EB221DAA641C0C113B1C888EEC8C134A9,
	Native_YGNodeStyleSetFlexBasisPercent_m53E89CA73D54E0D61C0AE43E46E71880B19E71A3,
	Native_YGNodeStyleSetFlexBasisAuto_m75E0FE2D72735385BD4B768E7E6595BEB2767B33,
	Native_YGNodeStyleSetWidth_m51B0C20BC69889051707A5656AC8E923214BCE28,
	Native_YGNodeStyleSetWidthPercent_m0E5E9A14D79DA3097D850A1399E815C7EB5C1711,
	Native_YGNodeStyleSetWidthAuto_mC0573A277B1B16B80CC8A9791C4477AF191EB0C9,
	Native_YGNodeStyleSetHeight_m2E2855E43F07820F05EB8B4EA48E772A8984D405,
	Native_YGNodeStyleSetHeightPercent_mB51BB945080154E9C68427923798645B681F3F23,
	Native_YGNodeStyleSetHeightAuto_mD0A25B33B11FE4A31513388555CCF2666C961454,
	Native_YGNodeStyleSetMinWidth_mD4BAEFA7CC9566284701288E58C9E8FFF90D0DC5,
	Native_YGNodeStyleSetMinWidthPercent_m511DBAB717BE516FD441F65AE02AC96E7C28DC2A,
	Native_YGNodeStyleSetMinHeight_mE3932DE6D89F927AB48EE46B75D66A3619680E44,
	Native_YGNodeStyleSetMinHeightPercent_mAD8DF2A468F3BED78662C0FFA09384C31953B18A,
	Native_YGNodeStyleSetMaxWidth_mFA9603DDDDF6E0858A17A52AE09848F917B59A64,
	Native_YGNodeStyleSetMaxWidthPercent_m629E9CC47E379D44E9772713BA95FD7EB8F3E49B,
	Native_YGNodeStyleSetMaxHeight_m53E372211ECD5CF9E338EB92FC6E812AD16E57A5,
	Native_YGNodeStyleSetMaxHeightPercent_mDD919E0532D33DE34AFA75DA7FCD918AE710C12A,
	Native_YGNodeStyleSetPosition_m6F05627862225682B30DEE270AF5DBC92A3F5E4A,
	Native_YGNodeStyleSetPositionPercent_m6EBB551060750C05ED566CCB896C65810C1BB6BC,
	Native_YGNodeStyleSetMargin_m3A70ED59754A7D67E27CE780CBCBB6A4DF585C72,
	Native_YGNodeStyleSetMarginPercent_m55403B2B8AB39FDB5A3DB015876D5F050CBE2E12,
	Native_YGNodeStyleSetMarginAuto_m35CD57DCE2F418647FB611724CD0B06ED5EA91D2,
	Native_YGNodeStyleSetPadding_m4EF83ED5DC1934CCE14E4AADEA8A730D2889CAAC,
	Native_YGNodeStyleSetPaddingPercent_m38A7998A22EF0102C503F240323511654889D7C6,
	Native_YGNodeStyleSetBorder_m2FC91E730F7299CD56EC8904D2F14371B9B98BFD,
	Native_YGNodeLayoutGetLeft_m96B29A4AE1C66E0FEF096DA5050CEC94442C459A,
	Native_YGNodeLayoutGetTop_m5372EE7B9E7CDE267BF9BF0FFC355CB4FD288297,
	Native_YGNodeLayoutGetRight_m0068329B2B59D0DBBB0DEC009D5B0F1F655B3540,
	Native_YGNodeLayoutGetBottom_m6F4451718486D0A549E1D5BEA7C8501DB62ACDA4,
	Native_YGNodeLayoutGetWidth_m9D52EC9FB8D5FCC5EE14E070C9092237211EA492,
	Native_YGNodeLayoutGetHeight_m5D2329F68FBBBA67CDEE3E673C787C2548514596,
	Native_YGNodeLayoutGetMargin_mCB0EE80823095ADFCA2A8F0A2067DD5B450B0F17,
	Native_YGNodeLayoutGetPadding_mE1699ADBB745BBCA574BAE8DB57CA430A1224BBA,
	Native_YGNodeLayoutGetBorder_m5F0B38BD792C29209B64DE9F6C0CD6950146A861,
	YogaValue_get_Unit_m4387357877C0D183FAFD5A3857AEF4C3E52CA900,
	YogaValue_get_Value_m142314AA36484CD328E08A06D6A750F5CA1C112A,
	YogaValue_Point_m1EF4416D27335FE09A3CD7D3CD8307763FA991BE,
	YogaValue_Equals_m7D39A876BF907A38C1F82FCF5303B9AD3CD1BC3F,
	YogaValue_Equals_mC37A099D3DB33896B40843065EC84D6F290FCCBD,
	YogaValue_GetHashCode_m8E287A9A127C7B15B870756A948C3BB6C4A12672,
	YogaValue_Auto_m2410501B6BB747D8290711BB328EB5FF7EAF73B5,
	YogaValue_Percent_m3A88462FA1C96A48737C78DC5B538B5CC7331C18,
	YogaValue_op_Implicit_m4E6C27B18E3D7AEAB238CD7E520852BF732F28F7,
	Logger__ctor_m411B63478FF6F8FEDFB36E338920ECF6D44FCE89,
	Logger_Invoke_mB64732D7138E5BFC958D540B1F27686BFAC815CF,
	MeasureFunction__ctor_mE08DFEFBD622065D2E123492910EA66C4A80A0BA,
	MeasureFunction_Invoke_m280560A27915B9D2F3D7E75056A63084925EEFCE,
	UIElementsRuntimeUtilityNative_RepaintOverlayPanels_m346BC3BDB4713023D04B36C35E1D5C20465A815A,
	UIElementsRuntimeUtilityNative_UpdateRuntimePanels_mB08CCB60AFCD7B71AA0E1F50DF697BFCFA8DC376,
	UIElementsRuntimeUtilityNative_RepaintOffscreenPanels_mFDED6CDF8F9B24FB98A64F785574AC710B173832,
	UIElementsRuntimeUtilityNative_RegisterPlayerloopCallback_m04D189811F7F648F186D5976D21EDF13BA986A71,
	UIElementsRuntimeUtilityNative_UnregisterPlayerloopCallback_m412B040F225984650BD09B9E8693AF6127D97ADF,
	UIElementsRuntimeUtilityNative_VisualElementCreation_m4FEEF03A1322277172416B770CB029247D7FCDFD,
	TextNative_GetCursorPosition_mF7B2D51841761A24623EE9DCAA354F30CB9523F4,
	TextNative_ComputeTextWidth_mE3A73219F780DB79A4BFCEA91CA1C4DB9AF7F00F,
	TextNative_ComputeTextHeight_m2DB34E72014B203E83A17A7321793ED979D36AD8,
	TextNative_GetVertices_m877716FCDD7F1D2C5849487C88691A5BA2441088,
	TextNative_GetOffset_mBBAE5C3304645D4295B723ABC9245AD6751ECD36,
	TextNative_ComputeTextScaling_mB18E1C1AD82EAD2A557414E4634F2F89149FFA2B,
	TextNative_DoComputeTextWidth_mC1ADB603729FAA6D6437B1E2031224A386FE1F9F,
	TextNative_DoComputeTextHeight_m917EE94AF5471D5E962AB3BEB0C7592A7F917BC2,
	TextNative_DoGetCursorPosition_m88B61DC7C5E4F17AC5E75F54D9B9B9F56F14F290,
	TextNative_GetVertices_m21E5914758FA0C969689CAA41EE14D265DAEED05,
	TextNative_DoGetOffset_m65E9D221159D69B6AA2F3B9F93EB010A5709BE05,
	TextNative_DoComputeTextWidth_Injected_m1E6872A8C0AC95CFDF74C202595553A1E470472F,
	TextNative_DoComputeTextHeight_Injected_m69A3B288A6F117F0A596A2B7222C398D6008AE7B,
	TextNative_DoGetCursorPosition_Injected_mC5C07A73AF3CC29F5E6333E7CF1D33DF1633B9BA,
	TextNative_GetVertices_Injected_mB5CAF8BEBFB7228E06BD36FB3B9C188C1A12127F,
	TextNative_DoGetOffset_Injected_m5A58EA8CF5C12A4E039224EFFD860A022BC15156,
	NULL,
	Utility_add_GraphicsResourcesRecreate_m7500B21EEAD18C1D395CA679C4C1B2A10AE1B962,
	Utility_remove_GraphicsResourcesRecreate_mB4DF54BC3B178674658360B438A1CA823A1C73AC,
	Utility_add_EngineUpdate_m06DF99E8301C5BD159A6AAE914BDCF808BA92B32,
	Utility_remove_EngineUpdate_mF7A954E84FC896CC63CB68CAF8A5235F835FF2D7,
	Utility_add_FlushPendingResources_m9A930FD6215580544B7AFAB6CA57D9581833FBEA,
	Utility_remove_FlushPendingResources_mBA31F8A56E659DE7298C2656B05D835178E5EE0D,
	Utility_add_RegisterIntermediateRenderers_m7E13A9C3E01341D429E0999F89F580232FFC6429,
	Utility_remove_RegisterIntermediateRenderers_mEF3C851756F576BD728BD8BFFE5C5A64597539E5,
	Utility_add_RenderNodeExecute_mA892466BF824D857F435453B8EA832382DC3314A,
	Utility_remove_RenderNodeExecute_m240A07023F25797A1827351E49329A02A122AD6C,
	Utility_RaiseGraphicsResourcesRecreate_m836B6EF1199237DBF417FA6F803ECD85E221201B,
	Utility_RaiseEngineUpdate_m7AB4CDD154B44E8B1E224F5BC180640AD1749B1F,
	Utility_RaiseFlushPendingResources_mB13980DB6EAEE1060226EB7DFA6CCEEB57D9CE68,
	Utility_RaiseRegisterIntermediateRenderers_mABFC7BADB09B3ADECB04E0E4D1B52ECD161C9F9E,
	Utility_RaiseRenderNodeAdd_mC61730515A6F1CB102C9D98DB7A85763EBDF5D96,
	Utility_RaiseRenderNodeExecute_mA31EAEA7E25A4EB997B70359272900579DAA5446,
	Utility_RaiseRenderNodeCleanup_mEF39A08F8A41ACDD38F7BF073F5B3B545A048B0C,
	Utility_AllocateBuffer_m8DC30A1D8EF56C1F02B665FF7E60887E30A21114,
	Utility_FreeBuffer_m3994CCBBB9E8C8B063CC8554EE0DFBE244EA427B,
	Utility_UpdateBufferRanges_mDAE14652D11D1AABBF39FD3AC71CC8B3A0880DAA,
	Utility_SetVectorArray_m36650D854AAE55F689FF622D36CC2AC0B9FD7FB1,
	Utility_GetVertexDeclaration_mB8CDEF6DF2CBA67EE88C0FB666CDC338556F329F,
	Utility_RegisterIntermediateRenderer_m19E56570934E5AE455A1FE959EAB203D284DA4FA,
	Utility_DrawRanges_m6F5AE31B6A857DB71F66A2662C6EBE1B218B6FF4,
	Utility_SetPropertyBlock_m04316F1E2FCAF1F9DDBEBDFE542EEBC3120C4EA4,
	Utility_SetScissorRect_mFB429FC493F3679035007BF759880159DDBFC049,
	Utility_DisableScissor_m7B52FA0EC1106927817854145DA8A862B3F2624C,
	Utility_CreateStencilState_mDC1F4473214745D1BE75D104081E73B52CCD043B,
	Utility_SetStencilState_m6F1F46336FF696C8C6A360B5E0CB344AC193A9E1,
	Utility_HasMappedBufferRange_m9701BED18820E4604C7C46DD1615E1272483C043,
	Utility_InsertCPUFence_m8FC65EF05973588366BC403C8807897CEE7CF440,
	Utility_CPUFencePassed_mBF5F842311E4ABF3DE18E9CA26A3684C21D836A5,
	Utility_WaitForCPUFencePassed_m5DC66BA62BAA3A503311E5EFE480BA763BA40CBB,
	Utility_SyncRenderThread_m4A7091ECD29F7A1CEF970D70F52F8C19696D6010,
	Utility_GetActiveViewport_mDB9165183361EBE3D7D6D673EA147472C7FC9F14,
	Utility_ProfileDrawChainBegin_m28C587F98561B8F4C6ED13AEEE83430A25D3F83B,
	Utility_ProfileDrawChainEnd_mF3602959ACC39C6EAE6429CC58172A06851E050D,
	Utility_NotifyOfUIREvents_m84DA51777B615A0FA57AE784519A530F23434B47,
	Utility_GetUnityProjectionMatrix_mDAA7B9D416768C0A81392BE9F27D551650EFF1D9,
	Utility__cctor_m8AD41F9B37AF2853080C9A52DEFDCE5E6693C3FC,
	Utility_RegisterIntermediateRenderer_Injected_m4CBBCDD753216F330B41766DA7C0D217FE281D64,
	Utility_SetScissorRect_Injected_m333581839C1FABCFAEE7EFBDF945ECEF7CCAE610,
	Utility_CreateStencilState_Injected_m47D217E56E67F17F06B389BF566BF0566D0F5479,
	Utility_GetActiveViewport_Injected_mA7FB4F05C7BDF5ABC71D228CE66B33954507C46D,
	Utility_GetUnityProjectionMatrix_Injected_m9E6F597B899AD99C931FBAFC0363B6F6E666CA81,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
extern void YogaValue_get_Unit_m4387357877C0D183FAFD5A3857AEF4C3E52CA900_AdjustorThunk (void);
extern void YogaValue_get_Value_m142314AA36484CD328E08A06D6A750F5CA1C112A_AdjustorThunk (void);
extern void YogaValue_Equals_m7D39A876BF907A38C1F82FCF5303B9AD3CD1BC3F_AdjustorThunk (void);
extern void YogaValue_Equals_mC37A099D3DB33896B40843065EC84D6F290FCCBD_AdjustorThunk (void);
extern void YogaValue_GetHashCode_m8E287A9A127C7B15B870756A948C3BB6C4A12672_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[5] = 
{
	{ 0x060000A0, YogaValue_get_Unit_m4387357877C0D183FAFD5A3857AEF4C3E52CA900_AdjustorThunk },
	{ 0x060000A1, YogaValue_get_Value_m142314AA36484CD328E08A06D6A750F5CA1C112A_AdjustorThunk },
	{ 0x060000A3, YogaValue_Equals_m7D39A876BF907A38C1F82FCF5303B9AD3CD1BC3F_AdjustorThunk },
	{ 0x060000A4, YogaValue_Equals_mC37A099D3DB33896B40843065EC84D6F290FCCBD_AdjustorThunk },
	{ 0x060000A5, YogaValue_GetHashCode_m8E287A9A127C7B15B870756A948C3BB6C4A12672_AdjustorThunk },
};
static const int32_t s_InvokerIndices[245] = 
{
	5018,
	6329,
	6329,
	6180,
	6108,
	4945,
	5087,
	9569,
	8798,
	2879,
	1328,
	8963,
	5040,
	6329,
	5040,
	6108,
	6329,
	6108,
	6108,
	6108,
	5040,
	5016,
	5016,
	5016,
	5016,
	5016,
	5016,
	5016,
	5016,
	5087,
	5087,
	5087,
	5155,
	5155,
	5155,
	5155,
	5155,
	5155,
	5155,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	5016,
	6178,
	6329,
	2626,
	5016,
	6329,
	5040,
	2934,
	7103,
	7935,
	6204,
	6204,
	5155,
	5155,
	5155,
	5155,
	2713,
	5155,
	5155,
	5155,
	5155,
	2713,
	5155,
	5155,
	5155,
	5155,
	2713,
	5087,
	5087,
	5087,
	5087,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	6259,
	9097,
	9373,
	9373,
	8739,
	8738,
	9517,
	9517,
	9373,
	9373,
	8736,
	8956,
	8740,
	8085,
	8738,
	7550,
	9373,
	8956,
	8738,
	9373,
	9373,
	6776,
	7589,
	8736,
	8956,
	9064,
	8737,
	8737,
	8737,
	8737,
	8737,
	8737,
	8737,
	8737,
	8737,
	8740,
	8740,
	8740,
	8740,
	8740,
	9373,
	8740,
	8740,
	9373,
	8740,
	8740,
	9373,
	8740,
	8740,
	8740,
	8740,
	8740,
	8740,
	8740,
	8740,
	8073,
	8073,
	8073,
	8073,
	8737,
	8073,
	8073,
	8073,
	9262,
	9262,
	9262,
	9262,
	9262,
	9262,
	8602,
	8602,
	8602,
	6178,
	6259,
	9391,
	3724,
	3597,
	6178,
	9571,
	9391,
	9391,
	2879,
	1022,
	2879,
	506,
	9569,
	9569,
	9569,
	9569,
	9569,
	9569,
	7966,
	9267,
	9267,
	8818,
	8655,
	8604,
	9267,
	9267,
	7966,
	7605,
	8655,
	9254,
	9254,
	7500,
	7519,
	8004,
	0,
	9375,
	9375,
	9375,
	9375,
	9375,
	9375,
	9375,
	9375,
	9375,
	9375,
	9363,
	9569,
	9569,
	9375,
	9373,
	9373,
	9373,
	7812,
	9373,
	7066,
	7563,
	9098,
	6514,
	6759,
	9375,
	9379,
	9569,
	9099,
	8737,
	9489,
	9562,
	8968,
	9384,
	9569,
	9542,
	9569,
	9569,
	9363,
	9521,
	9569,
	6513,
	9362,
	9092,
	9362,
	9362,
	0,
	0,
	0,
	0,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x02000020, { 4, 1 } },
	{ 0x060000C3, { 0, 4 } },
};
extern const uint32_t g_rgctx_NativeSlice_1_get_Length_m18994864FB5F6655700430EA19F4828689247090;
extern const uint32_t g_rgctx_NativeSlice_1_t66774708B16C64B2661C04AA73CE60288FB1482D;
extern const uint32_t g_rgctx_NativeSlice_1_get_Stride_m5BF29A800742A3638509BE93141D1E40693CF963;
extern const uint32_t g_rgctx_NativeSliceUnsafeUtility_GetUnsafePtr_TisT_t39DCE47ECA5992BC2FE83C6275C4C3F7EA2FCB79_mC8791D6BD3F735D5E2CF9FDFC5D73C31862962B5;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t3C727C4B8BF62FF70A1F7AE9FB09653FEE9981D4_mAE766809D234C461EF4B196928321B67889141DF;
static const Il2CppRGCTXDefinition s_rgctxValues[5] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Length_m18994864FB5F6655700430EA19F4828689247090 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeSlice_1_t66774708B16C64B2661C04AA73CE60288FB1482D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSlice_1_get_Stride_m5BF29A800742A3638509BE93141D1E40693CF963 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSliceUnsafeUtility_GetUnsafePtr_TisT_t39DCE47ECA5992BC2FE83C6275C4C3F7EA2FCB79_mC8791D6BD3F735D5E2CF9FDFC5D73C31862962B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t3C727C4B8BF62FF70A1F7AE9FB09653FEE9981D4_mAE766809D234C461EF4B196928321B67889141DF },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UIElementsNativeModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UIElementsNativeModule_CodeGenModule = 
{
	"UnityEngine.UIElementsNativeModule.dll",
	245,
	s_methodPointers,
	5,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	5,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
