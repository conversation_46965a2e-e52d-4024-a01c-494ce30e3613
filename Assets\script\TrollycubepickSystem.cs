using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UI;
using TMPro;

public class TrollycubepickSystem : MonoBehaviour
{
     public PlayableDirector Hokk;
     public GameObject[] cube;
     public Slider progressSlider;
     public Text percentageText;
     public GameObject completePanel;
     public GameObject canvas, tractorcam, machinecam;
     
     private int currentCubeIndex = 0;
     private int totalCubes = 8;
     
    void Start()
    {
        Hokk.Stop();
        
        // Initialize all cubes as inactive
        for(int i = 0; i < cube.Length; i++)
        {
            cube[i].SetActive(false);
        }
        
        // Initialize UI
        if(progressSlider != null)
        {
            progressSlider.maxValue = totalCubes;
            progressSlider.value = 0;
        }
        
        UpdatePercentageText();
        
        if(completePanel != null)
        {
            completePanel.SetActive(false);
        }
    }
   
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "Cube" && currentCubeIndex < totalCubes)
        {
            Hokk.Play();
            other.gameObject.SetActive(false);
         
            StartCoroutine("shake");
        }
    }
    
    IEnumerator shake()
    {
        this.transform.GetChild(0).gameObject.SetActive(true);
        yield return new WaitForSeconds(3.5f);
        this.transform.GetChild(0).gameObject.SetActive(false); 
        // Activate the next cube in sequence using Math.Min to ensure bounds
        int safeIndex = Mathf.Min(currentCubeIndex, cube.Length - 1);
        bool canActivate = currentCubeIndex < cube.Length;

        cube[safeIndex].SetActive(canActivate);
        currentCubeIndex += canActivate ? 1 : 0;

        // Update UI
        UpdateProgressUI();

        // Show complete panel using ternary operator
        bool isComplete = currentCubeIndex >= totalCubes;
        if (isComplete) ShowCompletePanel();
             
    }
    
    void UpdateProgressUI()
    {
        // Update slider
        if(progressSlider != null)
        {
            progressSlider.value = currentCubeIndex;
        }
        
        // Update percentage text
        UpdatePercentageText();
    }
    
    void UpdatePercentageText()
    {
        if(percentageText != null)
        {
            float percentage = (float)currentCubeIndex / totalCubes * 100f;
            percentageText.text = percentage.ToString("F0") + "%";
        }
    }
    
    void ShowCompletePanel()
    {
       
        Debug.Log("All cubes completed!");
        
        // Start completion sequence
        StartCoroutine(CompletionSequence());
    }
    
    IEnumerator CompletionSequence()
    {
        // Set machine cam to false and tractor cam to true
        if (machinecam != null)
        {
            machinecam.SetActive(false);
          
        }
        
        if(tractorcam != null)
        {
            tractorcam.SetActive(true);
        }
         canvas.SetActive(false);
        
        // Wait for 10 seconds
        yield return new WaitForSeconds(10f);
        
        // Show completion panel
        if(completePanel != null)
        {
            completePanel.SetActive(true);
        }
    }
    
  
}
