{ "pid": "host", "ph":"M", "name": "process_name", "args": {"name": "host"} },
{ "pid": "host", "ph":"M", "name": "process_sort_index", "args": {"sort_index": 0} },
{ "pid": "host", "tid": 1, "ph":"M", "name": "thread_name", "args": {"name": ""} },
{ "pid": "host", "tid": 1,"ts": 1753766182539816,"dur": 8748804, "ph":"X", "name": "UnityLinker.exe"},
{ "pid": "host", "tid": 1,"ts": 1753766182542163,"dur": 172705, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityEngineSteps"},
{ "pid": "host", "tid": 1,"ts": 1753766182715018,"dur": 46545, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveUnityEngine"},
{ "pid": "host", "tid": 1,"ts": 1753766182762040,"dur": 237162, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\StagingArea\\Data\\Managed\\MethodsToPreserve.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766182999222,"dur": 572333, "ph":"X", "name": "Step : Unity.Linker.Steps.InitializeEngineStrippingStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183571575,"dur": 6812, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveForEngineModuleStrippingEnabledStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183578403,"dur": 17024, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\StagingArea\\Data\\Managed\\TypesInScenes.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183595443,"dur": 1509, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\StagingArea\\Data\\Managed\\SerializedTypes.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183596957,"dur": 5620, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\InputSystemLink.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183602581,"dur": 8556, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assets\\GoogleMobileAds\\link.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183611856,"dur": 17782, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveAssemblyDirectoryStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183629654,"dur": 12539, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityRootsSteps"},
{ "pid": "host", "tid": 1,"ts": 1753766183642212,"dur": 8635, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183650853,"dur": 6859, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183657717,"dur": 1166, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183658886,"dur": 3296, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183662186,"dur": 29162, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183691376,"dur": 6746, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183698140,"dur": 2134, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183700277,"dur": 1758, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183702170,"dur": 3185, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityLoadReferencesStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183706178,"dur": 1469, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupI18N"},
{ "pid": "host", "tid": 1,"ts": 1753766183707658,"dur": 60765, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveFromDescriptorsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183769010,"dur": 2475, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183771489,"dur": 1398, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\45\\System.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183772891,"dur": 1281, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\45\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183774186,"dur": 20057, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.UnityBlacklistStep"},
{ "pid": "host", "tid": 1,"ts": 1753766183794249,"dur": 6634, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: mscorlib Resource: mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1753766183800896,"dur": 503847, "ph":"X", "name": "Step : Mono.Linker.Steps.DynamicDependencyLookupStep"},
{ "pid": "host", "tid": 1,"ts": 1753766184305578,"dur": 1098, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveTestsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766184307191,"dur": 1422, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveMonoBehaviourItselfStep"},
{ "pid": "host", "tid": 1,"ts": 1753766184308623,"dur": 1205, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromAllUserMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1753766184309838,"dur": 7990, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1753766184319964,"dur": 323615, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromPreserveAttribute"},
{ "pid": "host", "tid": 1,"ts": 1753766184643599,"dur": 19319, "ph":"X", "name": "Step : Unity.Linker.Steps.EngineStrippingAnnotationStep"},
{ "pid": "host", "tid": 1,"ts": 1753766184662938,"dur": 317981, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityTypeMapStep"},
{ "pid": "host", "tid": 1,"ts": 1753766184981407,"dur": 367694, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeMarkAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766185349116,"dur": 35762, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveSecurityStep"},
{ "pid": "host", "tid": 1,"ts": 1753766185384900,"dur": 7409, "ph":"X", "name": "Step : Unity.Linker.Steps.RemoveSecurityFromCopyAssemblies"},
{ "pid": "host", "tid": 1,"ts": 1753766185392319,"dur": 23287, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveFeaturesStep"},
{ "pid": "host", "tid": 1,"ts": 1753766185415629,"dur": 30647, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveUnreachableBlocksStep"},
{ "pid": "host", "tid": 1,"ts": 1753766185446296,"dur": 3872513, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityMarkStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189318826,"dur": 1240, "ph":"X", "name": "Step : Mono.Linker.Steps.ValidateVirtualMethodAnnotationsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189320075,"dur": 10545, "ph":"X", "name": "Step : Mono.Linker.Steps.ProcessWarningsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189330638,"dur": 128710, "ph":"X", "name": "Step : Unity.Linker.Steps.UnitySweepStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189459366,"dur": 5514, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityCodeRewriterStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189464897,"dur": 10307, "ph":"X", "name": "Step : Mono.Linker.Steps.CleanStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189475222,"dur": 5098, "ph":"X", "name": "Step : Unity.Linker.Steps.StubifyStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189480345,"dur": 4432, "ph":"X", "name": "Step : Unity.Linker.Steps.AddUnresolvedStubsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189484790,"dur": 5753, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeOutputAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1753766189492300,"dur": 1621078, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityOutputStep"},
{ "pid": "host", "tid": 1,"ts": 1753766191113394,"dur": 54425, "ph":"X", "name": "Step : Unity.Linker.Steps.LinkerToEditorDataGenerationStep"},
{ "pid": "host", "tid": 1,"ts": 1753766191182443,"dur": 106175, "ph":"X", "name": "Analytics"},
{ "pid": "host", "tid": 0, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": 0,"ts": 1753766182791000,"dur": 8000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766183594000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766183668000,"dur": 15000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766183841000,"dur": 13000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766183980000,"dur": 16000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766184187000,"dur": 18000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC during background GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766184403000,"dur": 58000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766184749000,"dur": 58000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185021000,"dur": 58000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185148000,"dur": 36000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC during background GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185327000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185536000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185735000,"dur": 13000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185796000,"dur": 3000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185902000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766185946000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766186030000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766186094000,"dur": 5000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766186154000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766186339000,"dur": 4000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766186829000,"dur": 35000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766187136000,"dur": 33000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766187509000,"dur": 33000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766188529000,"dur": 15000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766188948000,"dur": 18000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766189644000,"dur": 41000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766189989000,"dur": 35000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766190726000,"dur": 24000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753766191029000,"dur": 24000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -1,"ts": 1753766183540000,"dur": 13000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766183897000,"dur": 41000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766184042000,"dur": 50000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766184251000,"dur": 52000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766184499000,"dur": 139000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766184880000,"dur": 40000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766185241000,"dur": 63000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766185844000,"dur": 11000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766185989000,"dur": 3000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766186274000,"dur": 15000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766186671000,"dur": 13000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766186984000,"dur": 34000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766187326000,"dur": 31000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766187673000,"dur": 33000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766187916000,"dur": 59000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766188098000,"dur": 31000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766188283000,"dur": 63000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766188717000,"dur": 95000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766189143000,"dur": 95000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766190302000,"dur": 43000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753766190577000,"dur": 46000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -2, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -2,"ts": 1753766182662000,"dur": 8000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753766182791000,"dur": 17000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753766183668000,"dur": 35000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753766184042000,"dur": 180000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753766185021000,"dur": 206000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753766189143000,"dur": 480000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
