﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Ump.Api.ConsentForm::.ctor(GoogleMobileAds.Ump.Common.IConsentFormClient)
extern void ConsentForm__ctor_m7E621E44264000847F539870184EB3230EDFEF41 (void);
// 0x00000002 System.Void GoogleMobileAds.Ump.Api.ConsentForm::Load(System.Action`2<GoogleMobileAds.Ump.Api.ConsentForm,GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentForm_Load_mAB879496A1456226F52B4CE47B53975BB4ED5656 (void);
// 0x00000003 System.Void GoogleMobileAds.Ump.Api.ConsentForm::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentForm_Show_m4B7187A682576C0A936E602958AFEDB712583C24 (void);
// 0x00000004 System.Void GoogleMobileAds.Ump.Api.ConsentForm::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentForm_LoadAndShowConsentFormIfRequired_m53C2AC74AD02BC39BBC5F0850052013D5821B558 (void);
// 0x00000005 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::.ctor()
extern void U3CLoadU3Ec__AnonStorey0__ctor_m5D157B75B884DF130C17084209BA0045F3BB4B3F (void);
// 0x00000006 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__0()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mCFE7B081E5FED930F60A770B0E64AC16CEA21861 (void);
// 0x00000007 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__1(GoogleMobileAds.Ump.Api.FormError)
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m5E93B0194710C897FF8CDEE171D209D8E67CF953 (void);
// 0x00000008 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0::<>m__2()
extern void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m200EBAF519788BED08C1EA99141B2BF31ED3F766 (void);
// 0x00000009 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
extern void U3CLoadU3Ec__AnonStorey1__ctor_m4230348DB164723CA9736F7188B2824F682C03E6 (void);
// 0x0000000A System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
extern void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m5179274E91CFEEC3DC12EB844759CACD2FC1CEB5 (void);
// 0x0000000B System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::.ctor()
extern void U3CShowU3Ec__AnonStorey2__ctor_mBF5A9EEF097D451C3E43D1DD81954354DB60A7DC (void);
// 0x0000000C System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2::<>m__0(GoogleMobileAds.Ump.Api.FormError)
extern void U3CShowU3Ec__AnonStorey2_U3CU3Em__0_mBE702451A531B33A0866E66FC694432CE1E07B85 (void);
// 0x0000000D System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::.ctor()
extern void U3CShowU3Ec__AnonStorey3__ctor_m5DFC2C2D5962E76339884182F9CB77D9FADAF06F (void);
// 0x0000000E System.Void GoogleMobileAds.Ump.Api.ConsentForm/<Show>c__AnonStorey2/<Show>c__AnonStorey3::<>m__0()
extern void U3CShowU3Ec__AnonStorey3_U3CU3Em__0_mF9E3A0B64325161099D1EC708C4E6AB01716EA11 (void);
// 0x0000000F System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::.ctor()
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_mE45A0A9398600143C1464EB94438B1036EAC67C6 (void);
// 0x00000010 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4::<>m__0(GoogleMobileAds.Ump.Api.FormError)
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m72EFD3EEC1230EA8A7A432445DA11B72B924187A (void);
// 0x00000011 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::.ctor()
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m908558CBA54C8832450D791A82F0167A1F327030 (void);
// 0x00000012 System.Void GoogleMobileAds.Ump.Api.ConsentForm/<LoadAndShowConsentFormIfRequired>c__AnonStorey4/<LoadAndShowConsentFormIfRequired>c__AnonStorey5::<>m__0()
extern void U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_mCAE2F96D943F4B0BF11A5C6003DC04C3703E6EE7 (void);
// 0x00000013 GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.ConsentInformation::get_ClientFactory()
extern void ConsentInformation_get_ClientFactory_mD99BFD395187E5C21F07E369A6DF612D300060A6 (void);
// 0x00000014 GoogleMobileAds.Ump.Api.ConsentStatus GoogleMobileAds.Ump.Api.ConsentInformation::get_ConsentStatus()
extern void ConsentInformation_get_ConsentStatus_m0FD7B16BCB11F667C11CA56AB894BFFFDB12656D (void);
// 0x00000015 System.Void GoogleMobileAds.Ump.Api.ConsentInformation::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
extern void ConsentInformation_Update_m77C30ED8853B66168D77E0F02666360C78671046 (void);
// 0x00000016 System.Boolean GoogleMobileAds.Ump.Api.ConsentInformation::IsConsentFormAvailable()
extern void ConsentInformation_IsConsentFormAvailable_m55FB09EEC81648619F52F648D75AA6DBAC5B6ECE (void);
// 0x00000017 System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::.ctor()
extern void U3CUpdateU3Ec__AnonStorey0__ctor_m3525CFA3A371A63BCAF453F55D8BCC95E94D4C3C (void);
// 0x00000018 System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__0()
extern void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mFDFF30F7D0A0146CFE54AE0C9028996BE6132B57 (void);
// 0x00000019 System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__1(GoogleMobileAds.Ump.Api.FormError)
extern void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mCFBF3F6B526674C5E2093DDEE1F4C5A7DDDF5DBC (void);
// 0x0000001A System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0::<>m__2()
extern void U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m09B6D5C6B963E2B42A5559B3755F25C47324EFFD (void);
// 0x0000001B System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::.ctor()
extern void U3CUpdateU3Ec__AnonStorey1__ctor_mCE49B6C8A8E97FDC7FDA2642E82FEAE6BD1BDF68 (void);
// 0x0000001C System.Void GoogleMobileAds.Ump.Api.ConsentInformation/<Update>c__AnonStorey0/<Update>c__AnonStorey1::<>m__0()
extern void U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_m6D326C370DDADE82DB806657F000BAD5C5B77CCF (void);
// 0x0000001D System.Void GoogleMobileAds.Ump.Api.ConsentDebugSettings::.ctor()
extern void ConsentDebugSettings__ctor_m55A234F30D3B678E063DFBB6B9D838DC5768744D (void);
// 0x0000001E System.Void GoogleMobileAds.Ump.Api.ConsentRequestParameters::.ctor()
extern void ConsentRequestParameters__ctor_mB689D636424571F47A023696E0916A769EE1EC5C (void);
// 0x0000001F System.Void GoogleMobileAds.Ump.Api.FormError::.ctor(System.Int32,System.String)
extern void FormError__ctor_m74D2F9BD01E242B45657155A11219192DF02A8A7 (void);
// 0x00000020 System.Void GoogleMobileAds.Ump.Api.FormError::set_ErrorCode(System.Int32)
extern void FormError_set_ErrorCode_mCC38EFF4FB071A3DA5B2E0D0F77A527E1D5B5426 (void);
// 0x00000021 System.String GoogleMobileAds.Ump.Api.FormError::get_Message()
extern void FormError_get_Message_mF77BE1DEF78A279C9B197AFF203EA2E014E952F7 (void);
// 0x00000022 System.Void GoogleMobileAds.Ump.Api.FormError::set_Message(System.String)
extern void FormError_set_Message_mA6F54ADCA0D8A37C7CBD0A68B213AAE392BA8F1F (void);
// 0x00000023 GoogleMobileAds.Ump.Common.IUmpClientFactory GoogleMobileAds.Ump.Api.Utils::GetClientFactory()
extern void Utils_GetClientFactory_m94A1DAB83A77761C867D03DD43103387F9ABF409 (void);
// 0x00000024 System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::Load(System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000025 System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::Show(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000026 System.Void GoogleMobileAds.Ump.Common.IConsentFormClient::LoadAndShowConsentFormIfRequired(System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000027 System.Void GoogleMobileAds.Ump.Common.IConsentInformationClient::Update(GoogleMobileAds.Ump.Api.ConsentRequestParameters,System.Action,System.Action`1<GoogleMobileAds.Ump.Api.FormError>)
// 0x00000028 System.Int32 GoogleMobileAds.Ump.Common.IConsentInformationClient::GetConsentStatus()
// 0x00000029 System.Boolean GoogleMobileAds.Ump.Common.IConsentInformationClient::IsConsentFormAvailable()
// 0x0000002A GoogleMobileAds.Ump.Common.IConsentFormClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentFormClient()
// 0x0000002B GoogleMobileAds.Ump.Common.IConsentInformationClient GoogleMobileAds.Ump.Common.IUmpClientFactory::ConsentInformationClient()
static Il2CppMethodPointer s_methodPointers[43] = 
{
	ConsentForm__ctor_m7E621E44264000847F539870184EB3230EDFEF41,
	ConsentForm_Load_mAB879496A1456226F52B4CE47B53975BB4ED5656,
	ConsentForm_Show_m4B7187A682576C0A936E602958AFEDB712583C24,
	ConsentForm_LoadAndShowConsentFormIfRequired_m53C2AC74AD02BC39BBC5F0850052013D5821B558,
	U3CLoadU3Ec__AnonStorey0__ctor_m5D157B75B884DF130C17084209BA0045F3BB4B3F,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mCFE7B081E5FED930F60A770B0E64AC16CEA21861,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m5E93B0194710C897FF8CDEE171D209D8E67CF953,
	U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_m200EBAF519788BED08C1EA99141B2BF31ED3F766,
	U3CLoadU3Ec__AnonStorey1__ctor_m4230348DB164723CA9736F7188B2824F682C03E6,
	U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m5179274E91CFEEC3DC12EB844759CACD2FC1CEB5,
	U3CShowU3Ec__AnonStorey2__ctor_mBF5A9EEF097D451C3E43D1DD81954354DB60A7DC,
	U3CShowU3Ec__AnonStorey2_U3CU3Em__0_mBE702451A531B33A0866E66FC694432CE1E07B85,
	U3CShowU3Ec__AnonStorey3__ctor_m5DFC2C2D5962E76339884182F9CB77D9FADAF06F,
	U3CShowU3Ec__AnonStorey3_U3CU3Em__0_mF9E3A0B64325161099D1EC708C4E6AB01716EA11,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4__ctor_mE45A0A9398600143C1464EB94438B1036EAC67C6,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey4_U3CU3Em__0_m72EFD3EEC1230EA8A7A432445DA11B72B924187A,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5__ctor_m908558CBA54C8832450D791A82F0167A1F327030,
	U3CLoadAndShowConsentFormIfRequiredU3Ec__AnonStorey5_U3CU3Em__0_mCAE2F96D943F4B0BF11A5C6003DC04C3703E6EE7,
	ConsentInformation_get_ClientFactory_mD99BFD395187E5C21F07E369A6DF612D300060A6,
	ConsentInformation_get_ConsentStatus_m0FD7B16BCB11F667C11CA56AB894BFFFDB12656D,
	ConsentInformation_Update_m77C30ED8853B66168D77E0F02666360C78671046,
	ConsentInformation_IsConsentFormAvailable_m55FB09EEC81648619F52F648D75AA6DBAC5B6ECE,
	U3CUpdateU3Ec__AnonStorey0__ctor_m3525CFA3A371A63BCAF453F55D8BCC95E94D4C3C,
	U3CUpdateU3Ec__AnonStorey0_U3CU3Em__0_mFDFF30F7D0A0146CFE54AE0C9028996BE6132B57,
	U3CUpdateU3Ec__AnonStorey0_U3CU3Em__1_mCFBF3F6B526674C5E2093DDEE1F4C5A7DDDF5DBC,
	U3CUpdateU3Ec__AnonStorey0_U3CU3Em__2_m09B6D5C6B963E2B42A5559B3755F25C47324EFFD,
	U3CUpdateU3Ec__AnonStorey1__ctor_mCE49B6C8A8E97FDC7FDA2642E82FEAE6BD1BDF68,
	U3CUpdateU3Ec__AnonStorey1_U3CU3Em__0_m6D326C370DDADE82DB806657F000BAD5C5B77CCF,
	ConsentDebugSettings__ctor_m55A234F30D3B678E063DFBB6B9D838DC5768744D,
	ConsentRequestParameters__ctor_mB689D636424571F47A023696E0916A769EE1EC5C,
	FormError__ctor_m74D2F9BD01E242B45657155A11219192DF02A8A7,
	FormError_set_ErrorCode_mCC38EFF4FB071A3DA5B2E0D0F77A527E1D5B5426,
	FormError_get_Message_mF77BE1DEF78A279C9B197AFF203EA2E014E952F7,
	FormError_set_Message_mA6F54ADCA0D8A37C7CBD0A68B213AAE392BA8F1F,
	Utils_GetClientFactory_m94A1DAB83A77761C867D03DD43103387F9ABF409,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
static const int32_t s_InvokerIndices[43] = 
{
	5040,
	9375,
	5040,
	9375,
	6329,
	6329,
	5040,
	6329,
	6329,
	6329,
	6329,
	5040,
	6329,
	6329,
	6329,
	5040,
	6329,
	6329,
	9523,
	9515,
	8761,
	9489,
	6329,
	6329,
	5040,
	6329,
	6329,
	6329,
	6329,
	6329,
	2626,
	5016,
	6204,
	5040,
	9523,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Ump_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Ump_CodeGenModule = 
{
	"GoogleMobileAds.Ump.dll",
	43,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
